<!DOCTYPE html>
<html data-wf-domain="java-tech-blog.com" data-wf-page="algorithms-showcase"
      data-wf-site="java-tech-blog-site" lang="zh-CN">
<head>
    <meta charset="utf-8"/>
    <title>数据结构与算法演示 | Java技术博客</title>
    <meta content="交互式数据结构与算法演示平台，包含排序、搜索、图算法、动态规划等多种算法的可视化展示和LeetCode题解。"
          name="description"/>
    <meta content="数据结构与算法演示 | Java技术博客" property="og:title"/>
    <meta content="交互式数据结构与算法演示平台，包含排序、搜索、图算法、动态规划等多种算法的可视化展示和LeetCode题解。"
          property="og:description"/>
    <meta content="images/algorithms-og.jpg" property="og:image"/>
    <meta content="数据结构与算法演示 | Java技术博客" property="twitter:title"/>
    <meta content="交互式数据结构与算法演示平台，包含排序、搜索、图算法、动态规划等多种算法的可视化展示和LeetCode题解。"
          property="twitter:description"/>
    <meta content="width=device-width, initial-scale=1" name="viewport"/>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 50px;
            color: white;
        }
        
        .header h1 {
            font-size: 3rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }
        
        .nav-back {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1000;
        }
        
        .nav-back a {
            display: inline-flex;
            align-items: center;
            padding: 10px 20px;
            background: rgba(255,255,255,0.9);
            color: #333;
            text-decoration: none;
            border-radius: 25px;
            font-weight: 500;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }
        
        .nav-back a:hover {
            background: white;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .search-container {
            margin-bottom: 30px;
            text-align: center;
        }
        
        .search-box {
            width: 100%;
            max-width: 500px;
            padding: 15px 20px;
            font-size: 16px;
            border: none;
            border-radius: 25px;
            background: rgba(255,255,255,0.9);
            backdrop-filter: blur(10px);
            outline: none;
            transition: all 0.3s ease;
        }
        
        .search-box:focus {
            background: white;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }
        
        .categories {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin-bottom: 50px;
        }
        
        .category {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 30px;
            backdrop-filter: blur(10px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        
        .category:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }
        
        .category h2 {
            color: #4a5568;
            margin-bottom: 20px;
            font-size: 1.5rem;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .category-icon {
            width: 30px;
            height: 30px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }
        
        .algorithm-list {
            list-style: none;
        }
        
        .algorithm-item {
            margin-bottom: 12px;
        }
        
        .algorithm-link {
            display: block;
            padding: 12px 15px;
            background: #f7fafc;
            border-radius: 10px;
            text-decoration: none;
            color: #2d3748;
            transition: all 0.3s ease;
            border-left: 4px solid transparent;
        }
        
        .algorithm-link:hover {
            background: #e2e8f0;
            border-left-color: #667eea;
            transform: translateX(5px);
        }
        
        .algorithm-title {
            font-weight: 600;
            margin-bottom: 4px;
        }
        
        .algorithm-desc {
            font-size: 0.9rem;
            color: #718096;
        }
        
        .leetcode-badge {
            display: inline-block;
            background: #ff6b6b;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 500;
            margin-left: 8px;
        }
        
        .complexity-info {
            font-size: 0.8rem;
            color: #38a169;
            margin-top: 4px;
        }
        
        @media (max-width: 768px) {
            .categories {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .category {
                padding: 20px;
            }
        }
    </style>
</head>

<body>
    <div class="nav-back">
        <a href="index.html">← 返回主页</a>
    </div>
    
    <div class="container">
        <div class="header">
            <h1>数据结构与算法演示</h1>
            <p>交互式可视化学习平台 - 让算法变得生动有趣</p>
        </div>
        
        <div class="search-container">
            <input type="text" class="search-box" placeholder="搜索算法或数据结构..." id="searchInput">
        </div>
        
        <div class="categories" id="categoriesContainer">
            <!-- 分类内容将通过JavaScript动态生成 -->
        </div>
    </div>

    <script>
        // 算法数据结构
        const algorithmData = {
            "基础概念": {
                icon: "📚",
                items: [
                    {
                        title: "二进制运算",
                        desc: "位运算、移位操作、浮点数表示",
                        links: [
                            { name: "基本运算 (+、-、%、&、移位)", url: "data-structures-frontend/binary_operation.html" },
                            { name: "浮点数运算", url: "data-structures-frontend/binary_operation_float.html" },
                            { name: "异或运算", url: "data-structures-frontend/binary_operation_xor.html" }
                        ]
                    },
                    {
                        title: "缓存行与局部性原理",
                        desc: "内存访问模式对性能的影响",
                        links: [
                            { name: "二维数组遍历（外j内i）", url: "data-structures-frontend/cache_line1.html" },
                            { name: "二维数组遍历（外i内j）", url: "data-structures-frontend/cache_line2.html" }
                        ]
                    }
                ]
            },
            "数据结构": {
                icon: "🏗️",
                items: [
                    {
                        title: "线性结构",
                        desc: "数组、链表、栈、队列等基础数据结构",
                        complexity: "时间复杂度: O(1) - O(n)",
                        links: [
                            { name: "动态数组", url: "data-structures-frontend/ds_dynamic_array.html" },
                            { name: "单向链表", url: "data-structures-frontend/ds_singly_linked_list.html" },
                            { name: "单向链表（带哨兵）", url: "data-structures-frontend/ds_singly_linked_list(sentinal).html" },
                            { name: "双向链表（带哨兵）", url: "data-structures-frontend/ds_doubly_linked_list(sentinal).html" }
                        ]
                    },
                    {
                        title: "优先级队列与堆",
                        desc: "堆数据结构的实现和应用",
                        complexity: "时间复杂度: O(log n)",
                        links: [
                            { name: "优先级队列（无序数组）", url: "data-structures-frontend/priority_queue_1.html" },
                            { name: "优先级队列（有序数组）", url: "data-structures-frontend/priority_queue_2.html" },
                            { name: "优先级队列（堆）", url: "data-structures-frontend/priority_queue_3.html" },
                            { name: "大顶堆", url: "data-structures-frontend/heap_max.html" },
                            { name: "小顶堆", url: "data-structures-frontend/heap_min.html" }
                        ]
                    },
                    {
                        title: "哈希表",
                        desc: "散列表的实现和冲突处理",
                        complexity: "平均时间复杂度: O(1)",
                        links: [
                            { name: "哈希表实现", url: "data-structures-frontend/hashtable.html" }
                        ]
                    }
                ]
            },
            "排序算法": {
                icon: "🔄",
                items: [
                    {
                        title: "基础排序",
                        desc: "冒泡、选择、插入排序算法",
                        complexity: "时间复杂度: O(n²)",
                        links: [
                            { name: "冒泡排序", url: "data-structures-frontend/sort_bubble1.html" },
                            { name: "冒泡排序（改进1）", url: "data-structures-frontend/sort_bubble2.html" },
                            { name: "冒泡排序（改进2）", url: "data-structures-frontend/sort_bubble3.html" },
                            { name: "选择排序", url: "data-structures-frontend/sort_selection.html" },
                            { name: "插入排序", url: "data-structures-frontend/sort_insertion1.html" },
                            { name: "插入排序（交换法）", url: "data-structures-frontend/sort_insertion2.html" }
                        ]
                    },
                    {
                        title: "高级排序",
                        desc: "归并、快速、堆排序算法",
                        complexity: "时间复杂度: O(n log n)",
                        links: [
                            { name: "希尔排序", url: "data-structures-frontend/sort_shell.html" },
                            { name: "归并排序", url: "data-structures-frontend/sort_merge.html" },
                            { name: "快速排序（单边）", url: "data-structures-frontend/sort_quick.html" },
                            { name: "快速排序（双边）", url: "data-structures-frontend/sort_quick_hoare.html" },
                            { name: "快速排序（处理重复）", url: "data-structures-frontend/sort_quick_duplicate.html" },
                            { name: "堆排序", url: "data-structures-frontend/sort_heap.html" }
                        ]
                    }
                ]
            },
            "搜索算法": {
                icon: "🔍",
                items: [
                    {
                        title: "二分查找",
                        desc: "在有序数组中快速查找元素",
                        complexity: "时间复杂度: O(log n)",
                        links: [
                            { name: "二分查找-基础版", url: "data-structures-frontend/search_binary1.html" },
                            { name: "二分查找-基础版（有问题）", url: "data-structures-frontend/search_binary1_error.html" },
                            { name: "二分查找-改动版", url: "data-structures-frontend/search_binary2.html" },
                            { name: "二分查找-改动版（有问题）", url: "data-structures-frontend/search_binary2_error.html" },
                            { name: "二分查找-平衡版", url: "data-structures-frontend/search_binary3.html" },
                            { name: "二分查找-Leftmost 返回 -1", url: "data-structures-frontend/search_binary_left1.html" },
                            { name: "二分查找-Rightmost 返回 -1", url: "data-structures-frontend/search_binary_right1.html" },
                            { name: "二分查找-Leftmost 返回 i", url: "data-structures-frontend/search_binary_left2.html" },
                            { name: "二分查找-Rightmost 返回 i-1", url: "data-structures-frontend/search_binary_right2.html" }
                        ]
                    }
                ]
            },
            "树结构": {
                icon: "🌳",
                items: [
                    {
                        title: "二叉树",
                        desc: "二叉树的遍历和基本操作",
                        complexity: "时间复杂度: O(n) - O(log n)",
                        links: [
                            { name: "二叉树遍历", url: "data-structures-frontend/tree_binary.html" },
                            { name: "二叉树遍历（非递归）", url: "data-structures-frontend/tree_binary2.html" },
                            { name: "二叉搜索树", url: "data-structures-frontend/tree_binary_search.html" }
                        ]
                    },
                    {
                        title: "平衡树",
                        desc: "自平衡二叉搜索树",
                        complexity: "时间复杂度: O(log n)",
                        links: [
                            { name: "AVL树", url: "data-structures-frontend/tree_avl.html" },
                            { name: "红黑树", url: "data-structures-frontend/tree_redblack.html" }
                        ]
                    },
                    {
                        title: "B树",
                        desc: "多路搜索树，常用于数据库索引",
                        complexity: "时间复杂度: O(log n)",
                        links: [
                            { name: "B-树(t=2)", url: "data-structures-frontend/tree_2_3.html" },
                            { name: "B-树(t=3)", url: "data-structures-frontend/tree_n.html" }
                        ]
                    }
                ]
            },
            "图算法": {
                icon: "🕸️",
                items: [
                    {
                        title: "最短路径",
                        desc: "图中两点间的最短路径算法",
                        complexity: "时间复杂度: O(V²) - O(V³)",
                        links: [
                            { name: "Dijkstra算法", url: "data-structures-frontend/graph_dijkstra.html" },
                            { name: "Dijkstra算法（负边）", url: "data-structures-frontend/graph_dijkstra_negative_edge.html" },
                            { name: "Floyd算法", url: "data-structures-frontend/graph_floyd.html" },
                            { name: "Floyd算法（负环）", url: "data-structures-frontend/graph_floyd_negative_cycle.html" },
                            { name: "Bellman-Ford算法", url: "data-structures-frontend/graph_bellman.html" }
                        ]
                    },
                    {
                        title: "最小生成树",
                        desc: "连接所有顶点的最小权重树",
                        complexity: "时间复杂度: O(E log V)",
                        links: [
                            { name: "Prim算法", url: "data-structures-frontend/graph_prim.html" }
                        ]
                    }
                ]
            },
            "递归与回溯": {
                icon: "🔄",
                items: [
                    {
                        title: "递归算法",
                        desc: "递归思想的经典应用",
                        complexity: "时间复杂度: 取决于具体问题",
                        links: [
                            { name: "斐波那契数列", url: "data-structures-frontend/recursion_fibonacci.html" },
                            { name: "合并k个有序链表", url: "data-structures-frontend/recursion_merge.html" }
                        ]
                    },
                    {
                        title: "回溯算法",
                        desc: "通过试错寻找解决方案",
                        complexity: "时间复杂度: 指数级",
                        links: [
                            { name: "LeetCode 46 - 全排列", url: "data-structures-frontend/leetcode_46.html", leetcode: "46" },
                            { name: "LeetCode 47 - 全排列II", url: "data-structures-frontend/leetcode_47.html", leetcode: "47" },
                            { name: "LeetCode 77 - 组合", url: "data-structures-frontend/leetcode_77.html", leetcode: "77" },
                            { name: "LeetCode 39 - 组合总和", url: "data-structures-frontend/leetcode_39.html", leetcode: "39" },
                            { name: "LeetCode 51 - N皇后", url: "data-structures-frontend/leetcode_51.html", leetcode: "51" },
                            { name: "LeetCode 37 - 数独", url: "data-structures-frontend/leetcode_37.html", leetcode: "37" }
                        ]
                    }
                ]
            },
            "双指针技巧": {
                icon: "👆",
                items: [
                    {
                        title: "双指针算法",
                        desc: "使用两个指针解决数组问题",
                        complexity: "时间复杂度: O(n)",
                        links: [
                            { name: "龟兔赛跑算法", url: "data-structures-frontend/floyd-s-hare-and-tortoise/dist/index.html" },
                            { name: "LeetCode 11 - 盛水最多的容器", url: "data-structures-frontend/leetcode_11.html", leetcode: "11" },
                            { name: "LeetCode 167 - 两数之和II", url: "data-structures-frontend/leetcode_167.html", leetcode: "167" },
                            { name: "LeetCode 15 - 三数之和", url: "data-structures-frontend/leetcode_15.html", leetcode: "15" },
                            { name: "LeetCode 18 - 四数之和", url: "data-structures-frontend/leetcode_18.html", leetcode: "18" },
                            { name: "LeetCode 283 - 移动零", url: "data-structures-frontend/leetcode_283.html", leetcode: "283" }
                        ]
                    }
                ]
            },
            "字符串算法": {
                icon: "📝",
                items: [
                    {
                        title: "字符串匹配",
                        desc: "高效的字符串搜索算法",
                        complexity: "时间复杂度: O(n+m)",
                        links: [
                            { name: "LeetCode 28 - 字符串匹配（暴力）", url: "data-structures-frontend/leetcode_28.html", leetcode: "28" },
                            { name: "LeetCode 28 - 字符串匹配（KMP）", url: "data-structures-frontend/leetcode_28kmp.html", leetcode: "28" },
                            { name: "KMP算法 - 生成最长前后缀数组", url: "data-structures-frontend/leetcode_28kmpnext.html" },
                            { name: "LeetCode 14 - 最长公共前缀", url: "data-structures-frontend/leetcode_14.html", leetcode: "14" },
                            { name: "LeetCode 5 - 最长回文子串", url: "data-structures-frontend/leetcode_5.html", leetcode: "5" },
                            { name: "LeetCode 76 - 最小覆盖子串", url: "data-structures-frontend/leetcode_76.html", leetcode: "76" }
                        ]
                    }
                ]
            },
            "动态规划": {
                icon: "💰",
                items: [
                    {
                        title: "股票问题",
                        desc: "经典的动态规划问题系列",
                        complexity: "时间复杂度: O(n)",
                        links: [
                            { name: "LeetCode 121 - 买卖股票的最佳时机", url: "data-structures-frontend/leetcode_121.html", leetcode: "121" },
                            { name: "LeetCode 122 - 买卖股票的最佳时机II", url: "data-structures-frontend/leetcode_122.html", leetcode: "122" },
                            { name: "LeetCode 123 - 买卖股票的最佳时机III", url: "data-structures-frontend/leetcode_123.html", leetcode: "123" },
                            { name: "LeetCode 714 - 买卖股票的最佳时机含手续费", url: "data-structures-frontend/leetcode_714.html", leetcode: "714" },
                            { name: "LeetCode 309 - 最佳买卖股票时机含冷冻期", url: "data-structures-frontend/leetcode_309.html", leetcode: "309" }
                        ]
                    }
                ]
            },
            "栈与队列": {
                icon: "📚",
                items: [
                    {
                        title: "单调栈与单调队列",
                        desc: "维护单调性的栈和队列应用",
                        complexity: "时间复杂度: O(n)",
                        links: [
                            { name: "LeetCode 239 - 滑动窗口最大值", url: "data-structures-frontend/leetcode_239.html", leetcode: "239" },
                            { name: "LeetCode 42 - 接雨水", url: "data-structures-frontend/leetcode_42.html", leetcode: "42" },
                            { name: "LeetCode 155 - 最小栈", url: "data-structures-frontend/leetcode_155.html", leetcode: "155" },
                            { name: "LeetCode 155 - 最小栈（解法2）", url: "data-structures-frontend/leetcode_155_2.html", leetcode: "155" }
                        ]
                    }
                ]
            },
            "系统设计": {
                icon: "⚙️",
                items: [
                    {
                        title: "缓存设计",
                        desc: "LRU、LFU等缓存淘汰策略",
                        complexity: "时间复杂度: O(1)",
                        links: [
                            { name: "LeetCode 146 - LRU缓存", url: "data-structures-frontend/leetcode_146.html", leetcode: "146" },
                            { name: "LeetCode 460 - LFU缓存", url: "data-structures-frontend/leetcode_460.html", leetcode: "460" }
                        ]
                    },
                    {
                        title: "数据结构设计",
                        desc: "设计特殊功能的数据结构",
                        complexity: "时间复杂度: 取决于操作",
                        links: [
                            { name: "LeetCode 1206 - 设计跳表", url: "data-structures-frontend/leetcode_1206.html", leetcode: "1206" },
                            { name: "LeetCode 355 - 设计推特", url: "data-structures-frontend/leetcode_355.html", leetcode: "355" },
                            { name: "LeetCode 295 - 数据流的中位数", url: "data-structures-frontend/leetcode_295.html", leetcode: "295" }
                        ]
                    }
                ]
            },
            "贪心算法": {
                icon: "🎯",
                items: [
                    {
                        title: "贪心策略",
                        desc: "局部最优解构成全局最优解",
                        complexity: "时间复杂度: 取决于具体问题",
                        links: [
                            { name: "Huffman编码树", url: "data-structures-frontend/huffman.html" }
                        ]
                    }
                ]
            },
            "树相关LeetCode": {
                icon: "🌲",
                items: [
                    {
                        title: "二叉树题目",
                        desc: "LeetCode中的经典二叉树问题",
                        complexity: "时间复杂度: O(n)",
                        links: [
                            { name: "LeetCode 101 - 对称二叉树", url: "data-structures-frontend/leetcode_101.html", leetcode: "101" },
                            { name: "LeetCode 226 - 翻转二叉树", url: "data-structures-frontend/leetcode_226.html", leetcode: "226" },
                            { name: "LeetCode 98 - 验证二叉搜索树", url: "data-structures-frontend/leetcode_98.html", leetcode: "98" },
                            { name: "LeetCode 1008 - 前序遍历构造二叉搜索树", url: "data-structures-frontend/leetcode_1008.html", leetcode: "1008" }
                        ]
                    }
                ]
            }
        };

        // 生成页面内容
        function generateCategories() {
            const container = document.getElementById('categoriesContainer');
            container.innerHTML = '';

            Object.entries(algorithmData).forEach(([categoryName, categoryData]) => {
                const categoryDiv = document.createElement('div');
                categoryDiv.className = 'category';

                let categoryHTML = `
                    <h2>
                        <span class="category-icon">${categoryData.icon}</span>
                        ${categoryName}
                    </h2>
                `;

                categoryData.items.forEach(item => {
                    categoryHTML += `
                        <div class="algorithm-group">
                            <h3 style="color: #2d3748; margin: 20px 0 10px 0; font-size: 1.1rem;">${item.title}</h3>
                            <p style="color: #718096; margin-bottom: 10px; font-size: 0.9rem;">${item.desc}</p>
                            ${item.complexity ? `<div class="complexity-info">${item.complexity}</div>` : ''}
                            <ul class="algorithm-list">
                    `;

                    item.links.forEach(link => {
                        const leetcodeBadge = link.leetcode ? `<span class="leetcode-badge">LC ${link.leetcode}</span>` : '';
                        categoryHTML += `
                            <li class="algorithm-item">
                                <a href="${link.url}" class="algorithm-link" target="_blank">
                                    <div class="algorithm-title">${link.name}${leetcodeBadge}</div>
                                </a>
                            </li>
                        `;
                    });

                    categoryHTML += `
                            </ul>
                        </div>
                    `;
                });

                categoryDiv.innerHTML = categoryHTML;
                container.appendChild(categoryDiv);
            });
        }

        // 搜索功能
        function setupSearch() {
            const searchInput = document.getElementById('searchInput');
            let allItems = [];

            // 收集所有算法项目
            Object.entries(algorithmData).forEach(([categoryName, categoryData]) => {
                categoryData.items.forEach(item => {
                    item.links.forEach(link => {
                        allItems.push({
                            name: link.name,
                            url: link.url,
                            category: categoryName,
                            groupTitle: item.title,
                            desc: item.desc,
                            leetcode: link.leetcode
                        });
                    });
                });
            });

            searchInput.addEventListener('input', (e) => {
                const searchTerm = e.target.value.toLowerCase();

                if (searchTerm === '') {
                    generateCategories();
                    return;
                }

                const filteredItems = allItems.filter(item =>
                    item.name.toLowerCase().includes(searchTerm) ||
                    item.category.toLowerCase().includes(searchTerm) ||
                    item.groupTitle.toLowerCase().includes(searchTerm) ||
                    (item.leetcode && item.leetcode.includes(searchTerm))
                );

                displaySearchResults(filteredItems);
            });
        }

        // 显示搜索结果
        function displaySearchResults(items) {
            const container = document.getElementById('categoriesContainer');

            if (items.length === 0) {
                container.innerHTML = `
                    <div class="category" style="text-align: center; padding: 50px;">
                        <h2 style="color: #718096;">未找到匹配的算法</h2>
                        <p style="color: #a0aec0;">请尝试其他关键词</p>
                    </div>
                `;
                return;
            }

            container.innerHTML = `
                <div class="category">
                    <h2>
                        <span class="category-icon">🔍</span>
                        搜索结果 (${items.length} 项)
                    </h2>
                    <ul class="algorithm-list">
                        ${items.map(item => {
                            const leetcodeBadge = item.leetcode ? `<span class="leetcode-badge">LC ${item.leetcode}</span>` : '';
                            return `
                                <li class="algorithm-item">
                                    <a href="${item.url}" class="algorithm-link" target="_blank">
                                        <div class="algorithm-title">${item.name}${leetcodeBadge}</div>
                                        <div class="algorithm-desc">${item.category} - ${item.groupTitle}</div>
                                    </a>
                                </li>
                            `;
                        }).join('')}
                    </ul>
                </div>
            `;
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', () => {
            generateCategories();
            setupSearch();
        });
    </script>
</body>
</html>
