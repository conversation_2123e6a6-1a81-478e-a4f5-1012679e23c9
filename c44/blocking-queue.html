<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8"/>
    <title>并发编程核心：手撕双锁阻塞队列的设计与实现原理 | Java技术博客</title>
    <meta content="深入解析阻塞队列的设计原理，从单锁到双锁的性能优化，包含完整的Java实现代码和并发安全分析。"
          name="description"/>
    <meta content="并发编程核心：手撕双锁阻塞队列的设计与实现原理 | Java技术博客" property="og:title"/>
    <meta content="深入解析阻塞队列的设计原理，从单锁到双锁的性能优化，包含完整的Java实现代码和并发安全分析。"
          property="og:description"/>
    <meta content="width=device-width, initial-scale=1" name="viewport"/>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Microsoft YaHei', sans-serif;
            line-height: 1.8;
            color: #252321;
            background-color: #f6f4f1;
            overflow-x: hidden;
        }
        
        .nav-back {
            position: fixed;
            top: 30px;
            left: 30px;
            z-index: 1000;
        }
        
        .nav-back a {
            display: inline-flex;
            align-items: center;
            padding: 12px 24px;
            background: rgba(37, 35, 33, 0.9);
            color: #f6f4f1;
            text-decoration: none;
            border-radius: 30px;
            font-weight: 500;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            font-size: 14px;
        }
        
        .nav-back a:hover {
            background: #252321;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(37, 35, 33, 0.3);
        }
        
        .page-header {
            background: white;
            padding: 100px 20px 60px 20px;
            text-align: center;
            border-bottom: 1px solid rgba(188, 136, 74, 0.1);
        }
        
        .page-title {
            font-size: clamp(2rem, 4vw, 3rem);
            font-weight: 700;
            margin-bottom: 15px;
            color: #252321;
            line-height: 1.2;
        }
        
        .page-subtitle {
            font-size: clamp(1rem, 2vw, 1.2rem);
            color: #666;
            margin-bottom: 20px;
            font-weight: 400;
        }
        
        .page-date {
            font-size: 0.9rem;
            color: #bc884a;
            font-weight: 500;
            display: inline-block;
            padding: 8px 16px;
            background: rgba(188, 136, 74, 0.1);
            border-radius: 20px;
        }
        
        .content-section {
            max-width: 1000px;
            margin: 0 auto;
            padding: 80px 20px;
        }
        
        .section-title {
            font-size: 2.5rem;
            color: #252321;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
        }
        
        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #bc884a, #8b6914);
            border-radius: 2px;
        }
        
        .problem-section {
            background: linear-gradient(135deg, rgba(231, 76, 60, 0.05) 0%, rgba(192, 57, 43, 0.05) 100%);
            padding: 40px;
            border-radius: 20px;
            margin: 40px 0;
            border-left: 5px solid #e74c3c;
        }
        
        .solution-section {
            background: linear-gradient(135deg, rgba(46, 204, 113, 0.05) 0%, rgba(39, 174, 96, 0.05) 100%);
            padding: 40px;
            border-radius: 20px;
            margin: 40px 0;
            border-left: 5px solid #2ecc71;
        }
        
        .overview-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 40px;
            margin: 60px 0;
        }
        
        .overview-card {
            background: white;
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(37, 35, 33, 0.1);
            transition: all 0.3s ease;
            border: 1px solid rgba(188, 136, 74, 0.1);
        }
        
        .overview-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(37, 35, 33, 0.15);
        }
        
        .card-title {
            font-size: 1.5rem;
            color: #252321;
            margin-bottom: 20px;
            font-weight: 600;
        }
        
        .card-content {
            color: #666;
            line-height: 1.8;
        }
        
        .problem-list {
            list-style: none;
            counter-reset: problem-counter;
        }
        
        .problem-list li {
            counter-increment: problem-counter;
            margin: 20px 0;
            padding: 20px;
            background: rgba(231, 76, 60, 0.05);
            border-radius: 10px;
            position: relative;
            padding-left: 60px;
        }
        
        .problem-list li::before {
            content: counter(problem-counter);
            position: absolute;
            left: 20px;
            top: 50%;
            transform: translateY(-50%);
            background: #e74c3c;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 14px;
        }
        
        .solution-list {
            list-style: none;
            counter-reset: solution-counter;
        }
        
        .solution-list li {
            counter-increment: solution-counter;
            margin: 20px 0;
            padding: 20px;
            background: rgba(46, 204, 113, 0.05);
            border-radius: 10px;
            position: relative;
            padding-left: 60px;
        }
        
        .solution-list li::before {
            content: counter(solution-counter);
            position: absolute;
            left: 20px;
            top: 50%;
            transform: translateY(-50%);
            background: #2ecc71;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 14px;
        }
        
        .code-section {
            margin: 60px 0;
        }
        
        .code-container {
            background: #1e1e1e;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            margin: 30px 0;
        }
        
        .code-header {
            background: #2d2d2d;
            padding: 15px 20px;
            color: #ccc;
            font-size: 14px;
            font-weight: 500;
            border-bottom: 1px solid #444;
        }
        
        .code-block {
            margin: 0;
            padding: 30px;
            background: #1e1e1e;
            color: #d4d4d4;
            font-family: 'Fira Code', 'Monaco', 'Consolas', monospace;
            font-size: 14px;
            line-height: 1.6;
            overflow-x: auto;
            white-space: pre-wrap;
        }
        
        .highlight {
            background: rgba(188, 136, 74, 0.1);
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: 600;
            color: #bc884a;
        }
        
        .back-to-top {
            position: fixed;
            bottom: 30px;
            right: 30px;
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #bc884a, #8b6914);
            color: white;
            border: none;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            box-shadow: 0 4px 12px rgba(188, 136, 74, 0.3);
            transition: all 0.3s ease;
            opacity: 0;
            visibility: hidden;
            z-index: 1000;
        }
        
        .back-to-top.visible {
            opacity: 1;
            visibility: visible;
        }
        
        .back-to-top:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 16px rgba(188, 136, 74, 0.4);
        }
        
        .footer-section {
            background: linear-gradient(135deg, #252321 0%, #3a3632 100%);
            color: white;
            text-align: center;
            padding: 60px 20px;
            margin-top: 80px;
        }
        
        .footer-content p {
            margin: 10px 0;
            opacity: 0.9;
        }
        
        .footer-content p:first-child {
            font-size: 1.1rem;
            font-weight: 500;
        }
        
        .footer-content p:last-child {
            font-size: 0.9rem;
            opacity: 0.7;
        }
        
        @media (max-width: 768px) {
            .nav-back {
                top: 15px;
                left: 15px;
            }
            
            .nav-back a {
                padding: 10px 18px;
                font-size: 13px;
            }
            
            .page-header {
                padding: 80px 15px 40px 15px;
            }
            
            .content-section {
                padding: 40px 15px;
            }
            
            .section-title {
                font-size: 2rem;
                margin-bottom: 25px;
            }
            
            .overview-grid {
                grid-template-columns: 1fr;
                gap: 25px;
                margin: 40px 0;
            }
            
            .overview-card {
                padding: 25px 20px;
            }
            
            .problem-section,
            .solution-section {
                padding: 25px 20px;
                margin: 30px 0;
            }
            
            .code-block {
                font-size: 12px;
                padding: 20px;
            }
            
            .back-to-top {
                bottom: 20px;
                right: 20px;
                width: 45px;
                height: 45px;
                font-size: 18px;
            }
        }
    </style>
</head>
<body>
    <nav class="nav-back">
        <a href="index.html">← 返回首页</a>
    </nav>

    <header class="page-header">
        <h1 class="page-title">并发编程核心：双锁阻塞队列</h1>
        <p class="page-subtitle">手撕双锁阻塞队列的设计与实现原理</p>
        <div class="page-date">2025-01-12</div>
    </header>

    <main class="content-section">
        <section id="overview">
            <h2 class="section-title">概述</h2>
            
            <div class="problem-section">
                <h3 class="card-title">传统队列的问题</h3>
                <p style="margin-bottom: 20px;">之前的队列在很多场景下都不能很好地工作，主要存在以下问题：</p>
                <ol class="problem-list">
                    <li>大部分场景要求分离向队列放入（生产者）、从队列拿出（消费者）两个角色、它们得由不同的线程来担当，而之前的实现根本没有考虑<span class="highlight">线程安全问题</span></li>
                    <li>队列为空，那么在之前的实现里会返回 null，如果就是硬要拿到一个元素呢？只能<span class="highlight">不断循环尝试</span></li>
                    <li>队列为满，那么再之前的实现里会返回 false，如果就是硬要塞入一个元素呢？只能<span class="highlight">不断循环尝试</span></li>
                </ol>
            </div>

            <div class="solution-section">
                <h3 class="card-title">解决方案</h3>
                <p style="margin-bottom: 20px;">因此我们需要解决的问题有：</p>
                <ol class="solution-list">
                    <li>用<span class="highlight">锁保证线程安全</span></li>
                    <li>用<span class="highlight">条件变量</span>让等待非空线程与等待不满线程进入等待状态，而不是不断循环尝试，让 CPU 空转</li>
                </ol>
            </div>
        </section>

        <section id="thread-safety">
            <h2 class="section-title">线程安全问题演示</h2>

            <div class="overview-card">
                <h3 class="card-title">线程不安全的例子</h3>
                <div class="card-content">
                    <p>有同学对线程安全还没有足够的认识，下面举一个反例，两个线程都要执行入队操作（几乎在同一时刻）：</p>
                </div>
            </div>

            <div class="code-container">
                <div class="code-header">ThreadUnsafeExample.java</div>
                <pre class="code-block"><code>public class TestThreadUnsafe {
    private final String[] array = new String[10];
    private int tail = 0;

    public void offer(String e) {
        array[tail] = e;
        tail++;
    }

    @Override
    public String toString() {
        return Arrays.toString(array);
    }

    public static void main(String[] args) {
        TestThreadUnsafe queue = new TestThreadUnsafe();
        new Thread(()-> queue.offer("e1"), "t1").start();
        new Thread(()-> queue.offer("e2"), "t2").start();
    }
}</code></pre>
            </div>

            <div class="overview-card">
                <h3 class="card-title">执行时序分析</h3>
                <div class="card-content">
                    <p>执行的时间序列如下，假设初始状态 tail = 0，在执行过程中由于 CPU 在两个线程之间切换，造成了指令交错：</p>
                    <br>
                    <table style="width: 100%; border-collapse: collapse; margin: 20px 0;">
                        <thead>
                            <tr style="background: #f8f6f3;">
                                <th style="padding: 12px; border: 1px solid #ddd; text-align: left;">线程1</th>
                                <th style="padding: 12px; border: 1px solid #ddd; text-align: left;">线程2</th>
                                <th style="padding: 12px; border: 1px solid #ddd; text-align: left;">说明</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td style="padding: 12px; border: 1px solid #ddd;">array[tail]=e1</td>
                                <td style="padding: 12px; border: 1px solid #ddd;"></td>
                                <td style="padding: 12px; border: 1px solid #ddd;">线程1 向 tail 位置加入 e1，但还没来得及执行 tail++</td>
                            </tr>
                            <tr>
                                <td style="padding: 12px; border: 1px solid #ddd;"></td>
                                <td style="padding: 12px; border: 1px solid #ddd;">array[tail]=e2</td>
                                <td style="padding: 12px; border: 1px solid #ddd;">线程2 向 tail 位置加入 e2，<span class="highlight">覆盖掉了 e1</span></td>
                            </tr>
                            <tr>
                                <td style="padding: 12px; border: 1px solid #ddd;"></td>
                                <td style="padding: 12px; border: 1px solid #ddd;">tail++</td>
                                <td style="padding: 12px; border: 1px solid #ddd;">tail 自增为1</td>
                            </tr>
                            <tr>
                                <td style="padding: 12px; border: 1px solid #ddd;">tail++</td>
                                <td style="padding: 12px; border: 1px solid #ddd;"></td>
                                <td style="padding: 12px; border: 1px solid #ddd;">tail 自增为2</td>
                            </tr>
                            <tr style="background: #ffe6e6;">
                                <td colspan="3" style="padding: 12px; border: 1px solid #ddd; text-align: center;">
                                    <strong>最后状态：tail 为 2，数组为 [e2, null, null ...]</strong>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                    <p style="color: #e74c3c; font-weight: 500;">糟糕的是，由于指令交错的顺序不同，得到的结果不止以上一种，宏观上造成混乱的效果。</p>
                </div>
            </div>
        </section>

        <section id="single-lock">
            <h2 class="section-title">单锁实现</h2>

            <div class="overview-grid">
                <div class="overview-card">
                    <h3 class="card-title">锁的选择</h3>
                    <div class="card-content">
                        <p>Java 中要防止代码段交错执行，需要使用锁，有两种选择：</p>
                        <br>
                        <ul style="list-style-type: disc; margin-left: 20px;">
                            <li><span class="highlight">synchronized</span> 代码块，属于关键字级别提供锁保护，功能少</li>
                            <li><span class="highlight">ReentrantLock</span> 类，功能丰富</li>
                        </ul>
                    </div>
                </div>

                <div class="overview-card">
                    <h3 class="card-title">条件变量</h3>
                    <div class="card-content">
                        <p>ReentrantLock 可以配合条件变量来实现阻塞等待：</p>
                        <br>
                        <ul style="list-style-type: disc; margin-left: 20px;">
                            <li><span class="highlight">tailWaits</span>：队列满时等待的线程</li>
                            <li><span class="highlight">headWaits</span>：队列空时等待的线程</li>
                            <li><span class="highlight">await()</span>：进入等待状态</li>
                            <li><span class="highlight">signal()</span>：唤醒等待线程</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="code-container">
                <div class="code-header">基本锁保护示例</div>
                <pre class="code-block"><code>ReentrantLock lock = new ReentrantLock();

public void offer(String e) throws InterruptedException {
    lock.lockInterruptibly();
    try {
        array[tail] = e;
        tail++;
    } finally {
        lock.unlock();
    }
}</code></pre>
            </div>

            <div class="overview-card">
                <h3 class="card-title">为什么使用 while 而不是 if？</h3>
                <div class="card-content">
                    <p>思考为何要用 <span class="highlight">while</span> 而不是 <span class="highlight">if</span>，设队列容量是 3：</p>
                    <br>
                    <table style="width: 100%; border-collapse: collapse; margin: 20px 0;">
                        <thead>
                            <tr style="background: #f8f6f3;">
                                <th style="padding: 10px; border: 1px solid #ddd;">操作前</th>
                                <th style="padding: 10px; border: 1px solid #ddd;">offer(4)</th>
                                <th style="padding: 10px; border: 1px solid #ddd;">offer(5)</th>
                                <th style="padding: 10px; border: 1px solid #ddd;">poll()</th>
                                <th style="padding: 10px; border: 1px solid #ddd;">操作后</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td style="padding: 10px; border: 1px solid #ddd;">[1 2 3]</td>
                                <td style="padding: 10px; border: 1px solid #ddd;">队列满，进入tailWaits等待</td>
                                <td style="padding: 10px; border: 1px solid #ddd;"></td>
                                <td style="padding: 10px; border: 1px solid #ddd;"></td>
                                <td style="padding: 10px; border: 1px solid #ddd;">[1 2 3]</td>
                            </tr>
                            <tr>
                                <td style="padding: 10px; border: 1px solid #ddd;">[1 2 3]</td>
                                <td style="padding: 10px; border: 1px solid #ddd;"></td>
                                <td style="padding: 10px; border: 1px solid #ddd;"></td>
                                <td style="padding: 10px; border: 1px solid #ddd;">取走1，唤醒线程</td>
                                <td style="padding: 10px; border: 1px solid #ddd;">[2 3]</td>
                            </tr>
                            <tr>
                                <td style="padding: 10px; border: 1px solid #ddd;">[2 3]</td>
                                <td style="padding: 10px; border: 1px solid #ddd;"></td>
                                <td style="padding: 10px; border: 1px solid #ddd;">抢先获得锁，放入5</td>
                                <td style="padding: 10px; border: 1px solid #ddd;"></td>
                                <td style="padding: 10px; border: 1px solid #ddd;">[2 3 5]</td>
                            </tr>
                            <tr style="background: #ffe6e6;">
                                <td style="padding: 10px; border: 1px solid #ddd;">[2 3 5]</td>
                                <td style="padding: 10px; border: 1px solid #ddd;">从等待处继续执行</td>
                                <td style="padding: 10px; border: 1px solid #ddd;"></td>
                                <td style="padding: 10px; border: 1px solid #ddd;"></td>
                                <td style="padding: 10px; border: 1px solid #ddd;">[2 3 5 ?]</td>
                            </tr>
                        </tbody>
                    </table>
                    <p style="color: #e74c3c; font-weight: 500;">这种情况称之为<span class="highlight">虚假唤醒</span>，唤醒后应该重新检查条件！</p>
                </div>
            </div>
        </section>

        <section id="single-lock-implementation">
            <h2 class="section-title">单锁完整实现</h2>

            <div class="code-container">
                <div class="code-header">BlockingQueue1.java - 单锁实现</div>
                <pre class="code-block"><code>/**
 * 单锁实现
 * @param &lt;E&gt; 元素类型
 */
public class BlockingQueue1&lt;E&gt; implements BlockingQueue&lt;E&gt; {
    private final E[] array;
    private int head = 0;
    private int tail = 0;
    private int size = 0; // 元素个数

    @SuppressWarnings("all")
    public BlockingQueue1(int capacity) {
        array = (E[]) new Object[capacity];
    }

    ReentrantLock lock = new ReentrantLock();
    Condition tailWaits = lock.newCondition();
    Condition headWaits = lock.newCondition();

    @Override
    public void offer(E e) throws InterruptedException {
        lock.lockInterruptibly();
        try {
            while (isFull()) {
                tailWaits.await();
            }
            array[tail] = e;
            if (++tail == array.length) {
                tail = 0;
            }
            size++;
            headWaits.signal();
        } finally {
            lock.unlock();
        }
    }

    @Override
    public E poll() throws InterruptedException {
        lock.lockInterruptibly();
        try {
            while (isEmpty()) {
                headWaits.await();
            }
            E e = array[head];
            array[head] = null; // help GC
            if (++head == array.length) {
                head = 0;
            }
            size--;
            tailWaits.signal();
            return e;
        } finally {
            lock.unlock();
        }
    }

    private boolean isEmpty() {
        return size == 0;
    }

    private boolean isFull() {
        return size == array.length;
    }
}</code></pre>
            </div>
        </section>

        <section id="double-lock">
            <h2 class="section-title">双锁实现</h2>

            <div class="overview-card">
                <h3 class="card-title">单锁的性能瓶颈</h3>
                <div class="card-content">
                    <p>单锁的缺点在于：</p>
                    <br>
                    <ul style="list-style-type: disc; margin-left: 20px;">
                        <li>生产和消费几乎是不冲突的，唯一冲突的是生产者和消费者它们有可能同时修改 <span class="highlight">size</span></li>
                        <li>冲突的主要是生产者之间：多个 <span class="highlight">offer</span> 线程修改 <span class="highlight">tail</span></li>
                        <li>冲突的还有消费者之间：多个 <span class="highlight">poll</span> 线程修改 <span class="highlight">head</span></li>
                    </ul>
                    <br>
                    <p>如果希望进一步提高性能，可以用<span class="highlight">两把锁</span>：</p>
                    <ul style="list-style-type: disc; margin-left: 20px;">
                        <li>一把锁保护 <span class="highlight">tail</span></li>
                        <li>另一把锁保护 <span class="highlight">head</span></li>
                    </ul>
                </div>
            </div>

            <div class="code-container">
                <div class="code-header">双锁基本结构</div>
                <pre class="code-block"><code>ReentrantLock headLock = new ReentrantLock();  // 保护 head 的锁
Condition headWaits = headLock.newCondition(); // 队列空时，需要等待的线程集合

ReentrantLock tailLock = new ReentrantLock();  // 保护 tail 的锁
Condition tailWaits = tailLock.newCondition(); // 队列满时，需要等待的线程集合

AtomicInteger size = new AtomicInteger(0);     // 保护 size 的原子变量</code></pre>
            </div>

            <div class="overview-card">
                <h3 class="card-title">级联通知优化</h3>
                <div class="card-content">
                    <p>性能还可以进一步提升，采用<span class="highlight">级联通知（cascading notifies）</span>技巧：</p>
                    <br>
                    <ol style="margin-left: 20px;">
                        <li>当 offer 增加前队列为空，即从 0 变化到不空，才由此 offer 线程来通知 headWaits</li>
                        <li>队列从 0 变化到不空，会唤醒一个等待的 poll 线程，这个线程被唤醒后，如果检查出此时有其它 offer 线程新增了元素，那么不妨由此 poll 线程来唤醒其它 poll 线程</li>
                        <li>在 poll 时队列从满变化到不满，才由此 poll 线程来唤醒一个等待的 offer 线程</li>
                    </ol>
                    <br>
                    <p style="color: #2ecc71; font-weight: 500;">目的是减少线程对锁的争抢次数，提高性能。</p>
                </div>
            </div>

            <div class="code-container">
                <div class="code-header">BlockingQueue2.java - 双锁实现（精简版）</div>
                <pre class="code-block"><code>public class BlockingQueue2&lt;E&gt; implements BlockingQueue&lt;E&gt; {
    private final E[] array;
    private int head = 0;
    private int tail = 0;
    private final AtomicInteger size = new AtomicInteger(0);

    ReentrantLock headLock = new ReentrantLock();
    Condition headWaits = headLock.newCondition();
    ReentrantLock tailLock = new ReentrantLock();
    Condition tailWaits = tailLock.newCondition();

    @Override
    public void offer(E e) throws InterruptedException {
        int c;
        tailLock.lockInterruptibly();
        try {
            while (isFull()) {
                tailWaits.await();
            }
            array[tail] = e;
            if (++tail == array.length) {
                tail = 0;
            }
            c = size.getAndIncrement();
            // a. 队列不满, 但不是从满->不满, 由此offer线程唤醒其它offer线程
            if (c + 1 < array.length) {
                tailWaits.signal();
            }
        } finally {
            tailLock.unlock();
        }
        // b. 从0->不空, 由此offer线程唤醒等待的poll线程
        if (c == 0) {
            headLock.lock();
            try {
                headWaits.signal();
            } finally {
                headLock.unlock();
            }
        }
    }

    @Override
    public E poll() throws InterruptedException {
        E e;
        int c;
        headLock.lockInterruptibly();
        try {
            while (isEmpty()) {
                headWaits.await();
            }
            e = array[head];
            if (++head == array.length) {
                head = 0;
            }
            c = size.getAndDecrement();
            // b. 队列不空, 但不是从0变化到不空，由此poll线程通知其它poll线程
            if (c > 1) {
                headWaits.signal();
            }
        } finally {
            headLock.unlock();
        }
        // a. 从满->不满, 由此poll线程唤醒等待的offer线程
        if (c == array.length) {
            tailLock.lock();
            try {
                tailWaits.signal();
            } finally {
                tailLock.unlock();
            }
        }
        return e;
    }
}</code></pre>
            </div>

            <div class="overview-card">
                <h3 class="card-title">设计精髓</h3>
                <div class="card-content">
                    <p style="color: #bc884a; font-weight: 600; font-size: 1.1rem;">双锁实现的非常精巧，据说作者 Doug Lea 花了一年的时间才完善了此段代码。</p>
                    <br>
                    <p>这个实现展现了并发编程的艺术：</p>
                    <ul style="list-style-type: disc; margin-left: 20px;">
                        <li><span class="highlight">锁分离</span>：将读写操作分离，减少锁竞争</li>
                        <li><span class="highlight">原子操作</span>：使用 AtomicInteger 保证 size 的线程安全</li>
                        <li><span class="highlight">级联通知</span>：减少不必要的锁获取，提升性能</li>
                        <li><span class="highlight">避免死锁</span>：精心设计的锁获取顺序</li>
                    </ul>
                </div>
            </div>
        </section>
    </main>

    <footer class="footer-section">
        <div class="footer-content">
            <p>&copy; 2025 Java技术博客. 专注于并发编程与数据结构的深度解析.</p>
            <p>让复杂的并发概念变得简单易懂</p>
        </div>
    </footer>

    <button class="back-to-top" onclick="scrollToTop()" title="回到顶部">
        ↑
    </button>

    <script>
        // 平滑滚动到顶部功能
        function scrollToTop() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        }

        // 回到顶部按钮显示/隐藏
        window.addEventListener('scroll', function() {
            const backToTopBtn = document.querySelector('.back-to-top');
            if (window.scrollY > 300) {
                backToTopBtn.classList.add('visible');
            } else {
                backToTopBtn.classList.remove('visible');
            }
        });

        // 页面滚动动画
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        // 观察需要动画的元素
        document.addEventListener('DOMContentLoaded', function() {
            const animatedElements = document.querySelectorAll('.overview-card, .code-container, .problem-section, .solution-section');
            
            animatedElements.forEach(el => {
                el.style.opacity = '0';
                el.style.transform = 'translateY(30px)';
                el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
                observer.observe(el);
            });
        });
    </script>
</body>
</html>
