<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8"/>
    <title>深入理解红黑树：手撕红黑树的完整实现与性能分析 | Java技术博客</title>
    <meta content="深入解析红黑树数据结构，包含历史背景、核心特性、完整实现代码和性能对比分析。从理论到实践，全面掌握红黑树。"
          name="description"/>
    <meta content="深入理解红黑树：手撕红黑树的完整实现与性能分析 | Java技术博客" property="og:title"/>
    <meta content="深入解析红黑树数据结构，包含历史背景、核心特性、完整实现代码和性能对比分析。从理论到实践，全面掌握红黑树。"
          property="og:description"/>
    <meta content="width=device-width, initial-scale=1" name="viewport"/>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, 'Microsoft YaHei', sans-serif;
            line-height: 1.8;
            color: #252321;
            background-color: #f6f4f1;
            overflow-x: hidden;
        }
        
        .nav-back {
            position: fixed;
            top: 30px;
            left: 30px;
            z-index: 1000;
        }
        
        .nav-back a {
            display: inline-flex;
            align-items: center;
            padding: 12px 24px;
            background: rgba(37, 35, 33, 0.9);
            color: #f6f4f1;
            text-decoration: none;
            border-radius: 30px;
            font-weight: 500;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            font-size: 14px;
        }
        
        .nav-back a:hover {
            background: #252321;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(37, 35, 33, 0.3);
        }
        
        .page-header {
            background: white;
            padding: 100px 20px 60px 20px;
            text-align: center;
            border-bottom: 1px solid rgba(188, 136, 74, 0.1);
        }

        .page-title {
            font-size: clamp(2rem, 4vw, 3rem);
            font-weight: 700;
            margin-bottom: 15px;
            color: #252321;
            line-height: 1.2;
        }

        .page-subtitle {
            font-size: clamp(1rem, 2vw, 1.2rem);
            color: #666;
            margin-bottom: 20px;
            font-weight: 400;
        }

        .page-date {
            font-size: 0.9rem;
            color: #bc884a;
            font-weight: 500;
            display: inline-block;
            padding: 8px 16px;
            background: rgba(188, 136, 74, 0.1);
            border-radius: 20px;
        }
        
        .content-section {
            max-width: 1000px;
            margin: 0 auto;
            padding: 80px 20px;
        }
        
        .section-title {
            font-size: 2.5rem;
            color: #252321;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
        }
        
        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #bc884a, #8b6914);
            border-radius: 2px;
        }
        
        .overview-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 40px;
            margin: 60px 0;
        }
        
        .overview-card {
            background: white;
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(37, 35, 33, 0.1);
            transition: all 0.3s ease;
            border: 1px solid rgba(188, 136, 74, 0.1);
        }
        
        .overview-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(37, 35, 33, 0.15);
        }
        
        .card-title {
            font-size: 1.5rem;
            color: #252321;
            margin-bottom: 20px;
            font-weight: 600;
        }
        
        .card-content {
            color: #666;
            line-height: 1.8;
        }
        
        .history-section {
            background: linear-gradient(135deg, rgba(188, 136, 74, 0.05) 0%, rgba(139, 105, 20, 0.05) 100%);
            padding: 80px 20px;
            margin: 60px 0;
            border-radius: 30px;
        }
        
        .blockquote {
            background: white;
            padding: 40px;
            border-left: 5px solid #bc884a;
            border-radius: 10px;
            margin: 30px 0;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
            font-style: italic;
            color: #555;
            line-height: 1.8;
        }
        
        .properties-list {
            background: white;
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(37, 35, 33, 0.1);
            margin: 40px 0;
        }
        
        .properties-list ol {
            list-style: none;
            counter-reset: property-counter;
        }
        
        .properties-list li {
            counter-increment: property-counter;
            margin: 20px 0;
            padding: 20px;
            background: #f8f6f3;
            border-radius: 10px;
            position: relative;
            padding-left: 60px;
        }
        
        .properties-list li::before {
            content: counter(property-counter);
            position: absolute;
            left: 20px;
            top: 50%;
            transform: translateY(-50%);
            background: linear-gradient(135deg, #bc884a, #8b6914);
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 14px;
        }
        
        .emoji {
            font-size: 1.2em;
            margin: 0 5px;
        }

        .code-section {
            margin: 60px 0;
        }

        .code-container {
            background: #1e1e1e;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }

        .code-block {
            margin: 0;
            padding: 40px;
            background: #1e1e1e;
            color: #d4d4d4;
            font-family: 'Fira Code', 'Monaco', 'Consolas', monospace;
            font-size: 14px;
            line-height: 1.6;
            overflow-x: auto;
            white-space: pre-wrap;
        }

        .comparison-table {
            margin: 40px 0;
            overflow-x: auto;
        }

        .performance-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(37, 35, 33, 0.1);
        }

        .performance-table th,
        .performance-table td {
            padding: 20px;
            text-align: left;
            border-bottom: 1px solid #f0f0f0;
        }

        .performance-table th {
            background: linear-gradient(135deg, #bc884a, #8b6914);
            color: white;
            font-weight: 600;
            font-size: 1.1rem;
        }

        .performance-table tr:hover {
            background: #f8f6f3;
        }

        .performance-table tr:last-child td {
            border-bottom: none;
        }

        .summary-section {
            background: white;
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(37, 35, 33, 0.1);
            margin: 60px 0;
        }

        .summary-content {
            color: #555;
            line-height: 1.8;
            font-size: 1.1rem;
        }

        .footer-section {
            background: linear-gradient(135deg, #252321 0%, #3a3632 100%);
            color: white;
            text-align: center;
            padding: 60px 20px;
            margin-top: 80px;
        }

        .footer-content p {
            margin: 10px 0;
            opacity: 0.9;
        }

        .footer-content p:first-child {
            font-size: 1.1rem;
            font-weight: 500;
        }

        .footer-content p:last-child {
            font-size: 0.9rem;
            opacity: 0.7;
        }

        .visualization-container {
            background: white;
            padding: 60px 40px;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(37, 35, 33, 0.1);
            margin: 40px 0;
            text-align: center;
        }

        .tree-container {
            margin: 40px 0;
            display: flex;
            justify-content: center;
            align-items: flex-start;
            min-height: 300px;
        }

        .tree-node {
            position: relative;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 16px;
            margin: 10px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
            transition: all 0.3s ease;
        }

        .tree-node:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 12px rgba(0,0,0,0.3);
        }

        .black-node {
            background: linear-gradient(135deg, #2c2c2c, #1a1a1a);
            border: 3px solid #444;
        }

        .red-node {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            border: 3px solid #a93226;
        }

        .node-children {
            position: absolute;
            top: 70px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 80px;
        }

        .root-node {
            margin-top: 20px;
        }

        .tree-node::before {
            content: '';
            position: absolute;
            top: -20px;
            left: 50%;
            transform: translateX(-50%);
            width: 2px;
            height: 20px;
            background: #666;
        }

        .root-node::before {
            display: none;
        }

        .left-child::after,
        .right-child::after {
            content: '';
            position: absolute;
            top: -20px;
            width: 40px;
            height: 2px;
            background: #666;
        }

        .left-child::after {
            right: 25px;
        }

        .right-child::after {
            left: 25px;
        }

        .legend {
            display: flex;
            justify-content: center;
            gap: 40px;
            margin-top: 40px;
            flex-wrap: wrap;
        }

        .legend-item {
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 1.1rem;
            color: #555;
        }

        .legend-node {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }

        .back-to-top {
            position: fixed;
            bottom: 30px;
            right: 30px;
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #bc884a, #8b6914);
            color: white;
            border: none;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            box-shadow: 0 4px 12px rgba(188, 136, 74, 0.3);
            transition: all 0.3s ease;
            opacity: 0;
            visibility: hidden;
            z-index: 1000;
        }

        .back-to-top.visible {
            opacity: 1;
            visibility: visible;
        }

        .back-to-top:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 16px rgba(188, 136, 74, 0.4);
        }

        @media (max-width: 768px) {
            .nav-back {
                top: 15px;
                left: 15px;
            }

            .nav-back a {
                padding: 10px 18px;
                font-size: 13px;
            }

            .page-header {
                padding: 80px 15px 40px 15px;
            }

            .content-section {
                padding: 40px 15px;
            }

            .section-title {
                font-size: 2rem;
                margin-bottom: 25px;
            }

            .overview-grid {
                grid-template-columns: 1fr;
                gap: 25px;
                margin: 40px 0;
            }

            .overview-card {
                padding: 25px 20px;
            }

            .card-title {
                font-size: 1.3rem;
                margin-bottom: 15px;
            }

            .history-section {
                padding: 40px 15px;
                margin: 40px 0;
                border-radius: 20px;
            }

            .blockquote {
                padding: 25px 20px;
                margin: 20px 0;
                font-size: 0.95rem;
            }

            .properties-list {
                padding: 25px 15px;
                margin: 30px 0;
            }

            .properties-list li {
                padding: 15px;
                padding-left: 50px;
                margin: 15px 0;
                font-size: 0.95rem;
            }

            .properties-list li::before {
                width: 25px;
                height: 25px;
                font-size: 12px;
                left: 15px;
            }

            .code-block {
                font-size: 11px;
                padding: 15px;
                line-height: 1.4;
            }

            .performance-table {
                font-size: 0.85rem;
            }

            .performance-table th,
            .performance-table td {
                padding: 12px 8px;
            }

            .performance-table th {
                font-size: 0.9rem;
            }

            .summary-section {
                padding: 25px 15px;
                margin: 40px 0;
            }

            .summary-content {
                font-size: 1rem;
            }

            .footer-section {
                padding: 40px 15px;
                margin-top: 60px;
            }

            .visualization-container {
                padding: 30px 15px;
            }

            .tree-node {
                width: 35px;
                height: 35px;
                font-size: 12px;
                margin: 6px;
            }

            .node-children {
                gap: 50px;
                top: 50px;
            }

            .legend {
                gap: 15px;
                margin-top: 30px;
            }

            .legend-item {
                font-size: 0.9rem;
                gap: 8px;
            }

            .legend-node {
                width: 25px;
                height: 25px;
            }

            .back-to-top {
                bottom: 20px;
                right: 20px;
                width: 45px;
                height: 45px;
                font-size: 18px;
            }
        }

        @media (max-width: 480px) {
            .page-title {
                font-size: 1.8rem;
            }

            .page-subtitle {
                font-size: 1rem;
            }

            .section-title {
                font-size: 1.8rem;
            }

            .overview-card {
                padding: 20px 15px;
            }

            .card-title {
                font-size: 1.2rem;
            }

            .code-block {
                font-size: 10px;
                padding: 12px;
            }

            .performance-table {
                font-size: 0.8rem;
            }

            .performance-table th,
            .performance-table td {
                padding: 10px 6px;
            }

            .tree-node {
                width: 30px;
                height: 30px;
                font-size: 11px;
                margin: 5px;
            }

            .node-children {
                gap: 40px;
                top: 45px;
            }

            .legend-item {
                font-size: 0.85rem;
            }
        }
    </style>
</head>
<body>
    <nav class="nav-back">
        <a href="index.html">← 返回首页</a>
    </nav>

    <header class="page-header">
        <h1 class="page-title">深入理解红黑树</h1>
        <p class="page-subtitle">手撕红黑树的完整实现与性能分析</p>
        <div class="page-date">2025-01-15</div>
    </header>

    <main class="content-section">
        <section id="overview">
            <h2 class="section-title">概述</h2>
            
            <div class="history-section">
                <h3 class="card-title">历史背景</h3>
                <div class="blockquote">
                    红黑树是一种自平衡二叉查找树，最早由一位名叫Rudolf Bayer的德国计算机科学家于1972年发明。然而，最初的树形结构不是现在的红黑树，而是一种称为B树的结构，它是一种多叉树，可用于在磁盘上存储大量数据。
                    <br><br>
                    在1980年代早期，计算机科学家Leonard Adleman和Daniel Sleator推广了红黑树，并证明了它的自平衡性和高效性。从那时起，红黑树成为了最流行的自平衡二叉查找树之一，并被广泛应用于许多领域，如编译器、操作系统、数据库等。
                    <br><br>
                    红黑树的名字来源于红色节点和黑色节点的交替出现，它们的颜色是用来维护树的平衡性的关键。它们的颜色具有特殊的意义，黑色节点代表普通节点，而红色节点代表一个新添加的节点，它们必须满足一些特定的规则才能维持树的平衡性。
                </div>
                
                <p style="margin-top: 30px; font-size: 1.1rem; color: #555;">
                    红黑树也是一种自平衡的二叉搜索树，较之 AVL，插入和删除时旋转次数更少
                </p>
            </div>

            <h3 class="card-title" style="margin-top: 60px; text-align: center;">红黑树特性</h3>
            <div class="properties-list">
                <ol>
                    <li>所有节点都有两种颜色：红<span class="emoji">🔴</span>、黑<span class="emoji">⚫</span></li>
                    <li>所有 null 视为黑色<span class="emoji">⚫</span></li>
                    <li>红色<span class="emoji">🔴</span>节点不能相邻</li>
                    <li>根节点是黑色<span class="emoji">⚫</span></li>
                    <li>从根到任意一个叶子节点，路径中的黑色<span class="emoji">⚫</span>节点数一样</li>
                </ol>
            </div>
        </section>

        <section id="implementation">
            <h2 class="section-title">实现详解</h2>

            <div class="overview-grid">
                <div class="overview-card">
                    <h3 class="card-title">插入情况分析</h3>
                    <div class="card-content">
                        <p><strong>基本原则：</strong>插入节点均视为红色🔴</p>
                        <br>
                        <p><strong>Case 1：</strong>插入节点为根节点，将根节点变黑⚫</p>
                        <br>
                        <p><strong>Case 2：</strong>插入节点的父亲若为黑色⚫，树的红黑性质不变，无需调整</p>
                        <br>
                        <p><strong>Case 3：</strong>父亲为红色🔴，叔叔为红色🔴时，需要重新着色并递归调整</p>
                        <br>
                        <p><strong>Case 4：</strong>父亲为红色🔴，叔叔为黑色⚫时，需要通过旋转来平衡</p>
                    </div>
                </div>

                <div class="overview-card">
                    <h3 class="card-title">删除情况分析</h3>
                    <div class="card-content">
                        <p><strong>Case 0：</strong>如果删除节点有两个孩子，交换删除节点和后继节点的值，递归删除后继节点</p>
                        <br>
                        <p><strong>Case 1：</strong>删的是根节点，直接删除或用剩余节点替换</p>
                        <br>
                        <p><strong>Case 2：</strong>删黑色⚫剩红色🔴，剩下的红节点变黑⚫</p>
                        <br>
                        <p><strong>Case 3-5：</strong>删除黑色节点导致双黑失衡，需要通过兄弟节点的颜色和侄子节点的颜色来决定调整策略</p>
                    </div>
                </div>
            </div>

            <div class="code-section">
                <h3 class="card-title" style="text-align: center; margin: 60px 0 40px 0;">完整代码实现</h3>
                <div class="code-container">
                    <pre class="code-block"><code>package com.itheima.datastructure.redblacktree;

import static com.itheima.datastructure.redblacktree.RedBlackTree.Color.BLACK;
import static com.itheima.datastructure.redblacktree.RedBlackTree.Color.RED;

/**
 * &lt;h3&gt;红黑树&lt;/h3&gt;
 */
public class RedBlackTree {

    enum Color {
        RED, BLACK;
    }

    Node root;

    static class Node {
        int key;
        Object value;
        Node left;
        Node right;
        Node parent;        // 父节点
        Color color = RED;  // 颜色

        public Node(int key, Object value) {
            this.key = key;
            this.value = value;
        }

        public Node(int key) {
            this.key = key;
        }

        public Node(int key, Color color) {
            this.key = key;
            this.color = color;
        }

        public Node(int key, Color color, Node left, Node right) {
            this.key = key;
            this.color = color;
            this.left = left;
            this.right = right;
            if (left != null) {
                left.parent = this;
            }
            if (right != null) {
                right.parent = this;
            }
        }

        // 是否是左孩子
        boolean isLeftChild() {
            return parent != null && parent.left == this;
        }

        // 叔叔
        Node uncle() {
            if (parent == null || parent.parent == null) {
                return null;
            }
            if (parent.isLeftChild()) {
                return parent.parent.right;
            } else {
                return parent.parent.left;
            }
        }

        // 兄弟
        Node sibling() {
            if (parent == null) {
                return null;
            }
            if (this.isLeftChild()) {
                return parent.right;
            } else {
                return parent.left;
            }
        }
    }</code></pre>
                </div>
            </div>
        </section>

        <section id="visualization">
            <h2 class="section-title">红黑树可视化示例</h2>

            <div class="visualization-container">
                <div class="tree-container">
                    <div class="tree-node black-node root-node" data-value="10">
                        <span>10</span>
                        <div class="node-children">
                            <div class="tree-node red-node left-child" data-value="5">
                                <span>5</span>
                                <div class="node-children">
                                    <div class="tree-node black-node" data-value="3">
                                        <span>3</span>
                                    </div>
                                    <div class="tree-node black-node" data-value="7">
                                        <span>7</span>
                                    </div>
                                </div>
                            </div>
                            <div class="tree-node red-node right-child" data-value="15">
                                <span>15</span>
                                <div class="node-children">
                                    <div class="tree-node black-node" data-value="12">
                                        <span>12</span>
                                    </div>
                                    <div class="tree-node black-node" data-value="18">
                                        <span>18</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="legend">
                    <div class="legend-item">
                        <div class="legend-node black-node"></div>
                        <span>黑色节点 ⚫</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-node red-node"></div>
                        <span>红色节点 🔴</span>
                    </div>
                </div>
            </div>
        </section>

        <section id="performance">
            <h2 class="section-title">性能对比分析</h2>

            <div class="comparison-table">
                <table class="performance-table">
                    <thead>
                        <tr>
                            <th>维度</th>
                            <th>普通二叉搜索树</th>
                            <th>AVL树</th>
                            <th>红黑树</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><strong>查询</strong></td>
                            <td>平均O(logn)，最坏O(n)</td>
                            <td>O(logn)</td>
                            <td>O(logn)</td>
                        </tr>
                        <tr>
                            <td><strong>插入</strong></td>
                            <td>平均O(logn)，最坏O(n)</td>
                            <td>O(logn)</td>
                            <td>O(logn)</td>
                        </tr>
                        <tr>
                            <td><strong>删除</strong></td>
                            <td>平均O(logn)，最坏O(n)</td>
                            <td>O(logn)</td>
                            <td>O(logn)</td>
                        </tr>
                        <tr>
                            <td><strong>平衡性</strong></td>
                            <td>不平衡</td>
                            <td>严格平衡</td>
                            <td>近似平衡</td>
                        </tr>
                        <tr>
                            <td><strong>结构</strong></td>
                            <td>二叉树</td>
                            <td>自平衡的二叉树</td>
                            <td>具有红黑性质的自平衡二叉树</td>
                        </tr>
                        <tr>
                            <td><strong>查找效率</strong></td>
                            <td>低</td>
                            <td>高</td>
                            <td>高</td>
                        </tr>
                        <tr>
                            <td><strong>插入删除效率</strong></td>
                            <td>低</td>
                            <td>中等</td>
                            <td>高</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="summary-section">
                <h3 class="card-title">总结</h3>
                <div class="summary-content">
                    <p>普通二叉搜索树插入、删除、查询的时间复杂度与树的高度相关，因此在最坏情况下，时间复杂度为O(n)，而且容易退化成链表，查找效率低。</p>
                    <br>
                    <p>AVL树是一种高度平衡的二叉搜索树，其左右子树的高度差不超过1。因此，它能够在logn的平均时间内完成插入、删除、查询操作，但是在维护平衡的过程中，需要频繁地进行旋转操作，导致插入删除效率较低。</p>
                    <br>
                    <p>红黑树是一种近似平衡的二叉搜索树，它在保持高度平衡的同时，又能够保持较高的插入删除效率。红黑树通过节点着色和旋转操作来维护平衡。红黑树在维护平衡的过程中，能够进行较少的节点旋转操作，因此插入删除效率较高，并且查询效率也较高。</p>
                    <br>
                    <p><strong>综上所述，红黑树具有较高的综合性能，是一种广泛应用的数据结构。</strong></p>
                </div>
            </div>
        </section>
    </main>

    <footer class="footer-section">
        <div class="footer-content">
            <p>&copy; 2025 Java技术博客. 专注于算法与数据结构的深度解析.</p>
            <p>让复杂的数据结构变得简单易懂</p>
        </div>
    </footer>

    <button class="back-to-top" onclick="scrollToTop()" title="回到顶部">
        ↑
    </button>

    <script>
        // 平滑滚动到顶部功能
        function scrollToTop() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        }

        // 回到顶部按钮显示/隐藏
        window.addEventListener('scroll', function() {
            const backToTopBtn = document.querySelector('.back-to-top');
            if (window.scrollY > 300) {
                backToTopBtn.classList.add('visible');
            } else {
                backToTopBtn.classList.remove('visible');
            }
        });

        // 红黑树节点交互效果
        document.addEventListener('DOMContentLoaded', function() {
            const treeNodes = document.querySelectorAll('.tree-node');

            treeNodes.forEach(node => {
                node.addEventListener('click', function() {
                    const value = this.getAttribute('data-value');
                    const isRed = this.classList.contains('red-node');
                    const color = isRed ? '红色' : '黑色';

                    // 创建提示信息
                    const tooltip = document.createElement('div');
                    tooltip.className = 'node-tooltip';
                    tooltip.innerHTML = `节点值: ${value}<br>颜色: ${color}`;
                    tooltip.style.cssText = `
                        position: absolute;
                        background: rgba(0,0,0,0.8);
                        color: white;
                        padding: 10px 15px;
                        border-radius: 8px;
                        font-size: 14px;
                        z-index: 1000;
                        pointer-events: none;
                        top: -60px;
                        left: 50%;
                        transform: translateX(-50%);
                        white-space: nowrap;
                        opacity: 0;
                        transition: opacity 0.3s ease;
                    `;

                    this.style.position = 'relative';
                    this.appendChild(tooltip);

                    // 显示提示
                    setTimeout(() => {
                        tooltip.style.opacity = '1';
                    }, 10);

                    // 3秒后移除提示
                    setTimeout(() => {
                        tooltip.style.opacity = '0';
                        setTimeout(() => {
                            if (tooltip.parentNode) {
                                tooltip.parentNode.removeChild(tooltip);
                            }
                        }, 300);
                    }, 3000);
                });
            });
        });

        // 页面滚动动画
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        // 观察需要动画的元素
        document.addEventListener('DOMContentLoaded', function() {
            const animatedElements = document.querySelectorAll('.overview-card, .code-container, .performance-table, .tree-container');

            animatedElements.forEach(el => {
                el.style.opacity = '0';
                el.style.transform = 'translateY(30px)';
                el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
                observer.observe(el);
            });
        });
    </script>
</body>
</html>
