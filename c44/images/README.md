# AI 个人网页图片说明

## 需要的图片文件

请将以下图片文件放入此目录，并使用指定的文件名：

### 1. claude-4-release.jpg
- 用于：Claude 4.0 正式发布卡片
- 建议内容：Claude AI 相关的图片
- 链接：https://www.anthropic.com/claude

### 2. gpt4-multimodal.jpg  
- 用于：GPT-4多模态能力深度解析卡片
- 建议内容：GPT-4 或多模态AI相关图片
- 链接：https://openai.com/research/gpt-4

### 3. semantic-kernel.jpg
- 用于：微软Semantic Kernel框架详解卡片  
- 建议内容：微软 Semantic Kernel 相关图片
- 链接：https://github.com/microsoft/semantic-kernel

### 4. mcp-protocol.jpg
- 用于：MCP协议：AI应用的新标准卡片
- 建议内容：MCP 协议相关图片
- 链接：https://www.modelcontextprotocol.io/

### 5. effective-java-book.jpg
- 用于：Effective Java 书籍封面
- 建议内容：Effective Java 第三版书籍封面
- 链接：https://www.amazon.com/Effective-Java-Joshua-Bloch/dp/0134685997

### 6. spring-in-action-book.jpg
- 用于：Spring实战 书籍封面
- 建议内容：Spring in Action 书籍封面
- 链接：https://www.amazon.com/Spring-Action-Craig-Walls/dp/1617294942

### 7. clean-code-book.jpg
- 用于：代码整洁之道 书籍封面
- 建议内容：Clean Code 书籍封面
- 链接：https://www.amazon.com/Clean-Code-Handbook-Software-Craftsmanship/dp/0132350882

### 8. java-concurrency-book.jpg
- 用于：Java并发编程实战 书籍封面
- 建议内容：Java Concurrency in Practice 书籍封面
- 链接：https://www.amazon.com/Java-Concurrency-Practice-Brian-Goetz/dp/0321349601

## 图片要求
- 格式：JPG
- 建议尺寸：至少 800x600 像素
- 文件大小：建议不超过 2MB

## 已完成的修改
1. ✅ 第一个卡片：Claude 4.0 发布 - 链接和图片路径已更新
2. ✅ 第二个卡片：GPT-4多模态 - 链接和图片路径已更新，标题已更新
3. ✅ 第三个卡片：Semantic Kernel - 链接、图片路径、标题和描述已更新  
4. ✅ 第四个卡片：MCP协议 - 链接、图片路径和标题已更新

## 旋转Marquee按钮修改
✅ 已完成：
- 移除了点击跳转功能（从`<a>`标签改为`<div>`）
- 将所有"click to view all"文字改为AI相关中文术语：
  - AI技术探索
  - 智能未来
  - 机器学习
  - 深度学习
  - 神经网络
  - 自然语言处理
  - 计算机视觉
  - 大语言模型

## 滚动Marquee文字修改
✅ 已完成：
- **奶油色背景滚动条（左侧）**：
  - "500+ Commits Published" → "算法与数据结构"
  - "Over 10 Publishers" → "Java编程实践"

- **黑色背景滚动条（右侧）**：
  - "Contributing to Politics" → "人工智能技术"
  - "40+ Years" → "软件开发"

现在两个滚动条显示的内容为：
- 左侧：算法与数据结构 - Java编程实践 - 算法与数据结构 - Java编程实践...
- 右侧：人工智能技术 - 软件开发 - 人工智能技术 - 软件开发...

## Spring AI 介绍部分修改
✅ 已完成：
- **文字内容**：
  - 原文："He has been interviewed by many foreign news agencies including"
  - 新文："Spring AI 是一个强大的Java框架，专为构建AI驱动的应用程序而设计，支持多种"

- **支持的AI模型列表**：
  - 原文："BBC, ABC, Al Jazeera, DeutscheWelle, New York Public Radio, RAI, El Pais."
  - 新文："OpenAI、Azure OpenAI、Anthropic Claude、Google Gemini、Ollama 等主流AI模型。"

- **按钮修改**：
  - 按钮文字："Learn More" → "查看详情"
  - 链接地址："/about" → "https://spring.io/projects/spring-ai"
  - 添加了 target="_blank" 属性，在新窗口打开

## 书籍展示部分修改
✅ 已完成：
- **标题修改**：
  - "Discover Books That Define the Political Discourse" → "推荐书籍 Java编程 与技术成长"

- **书籍内容更新**：
  1. **第一本书**：
     - 标题："Fidel's Ethics of Violence..." → "Effective Java: 编写高质量Java代码的90个建议"
     - 图片：./images/effective-java-book.jpg
     - 链接：Amazon Effective Java 页面

  2. **第二本书**：
     - 标题："INTERVENTIONS: SELECTED POLITICAL WRITINGS 2024" → "Spring实战: 构建企业级Java应用程序"
     - 图片：./images/spring-in-action-book.jpg
     - 链接：Amazon Spring in Action 页面

  3. **第三本书**：
     - 标题："Sri Lanka: The Travails of a Democracy..." → "代码整洁之道: 编写可读、可维护、可扩展代码的艺术"
     - 图片：./images/clean-code-book.jpg
     - 链接：Amazon Clean Code 页面

  4. **第四本书**：
     - 标题："The Fall of Global Socialism..." → "Java并发编程实战: 构建高效、可扩展的并发应用"
     - 图片：./images/java-concurrency-book.jpg
     - 链接：Amazon Java Concurrency in Practice 页面

- **按钮文字修改**：
  - 所有"Read Book"按钮 → "阅读书籍"
  - "View all books"按钮 → "查看更多书籍"

## Footer 页脚修改
✅ 已完成：
- **主标题修改**：
  - "END OF PAGE" → "联系我"

- **描述文字更新**：
  - 原文：政治作家作品集网站描述
  - 新文：专注于Java开发、人工智能技术研究与分享。欢迎交流学习，共同探索技术前沿。

- **联系信息添加**：
  - 📧 邮箱: <EMAIL>（示例，需要替换）
  - 🐙 GitHub: github.com/yourusername（示例，需要替换）

- **社交媒体链接**：
  - 添加了可点击的邮箱链接（mailto:）
  - 添加了可点击的GitHub链接（新窗口打开）

- **导航文字修改**：
  - "GO TO NEXT PAGE" → "了解更多"
  - "About" → "关于我"

📝 **个人信息替换指南**：
请查看 `FOOTER_CUSTOMIZATION.md` 文件，了解如何将示例邮箱和GitHub地址替换为您的真实信息。

## 底部滚动特效修改
✅ 已完成：
- **网页标题更新**：
  - 原文："Dr. Dayan Jayatilleka | Sri Lanka's leading Software Engineer and AI Enthusiast"
  - 新文："Java开发工程师 | AI技术专家与软件架构师"

- **滚动身份标签替换**：
  - "Java Developer" → "Java Developer"（Java开发者）
  - "Software Engineer" → "Software Engineer"（软件工程师）
  - "AI Enthusiast" → "AI Enthusiast"（AI爱好者）
  - "Author" → "Tech Blogger"（技术博主）
  - "Tech Blogger" → "Code Architect"（代码架构师）

- **修改范围**：
  - 网页底部所有滚动marquee容器（约265个实例）
  - 网页标题和meta标签
  - 主页面hero区域的身份标签

现在底部滚动特效显示的内容为：
Java Developer - Software Engineer - AI Enthusiast - Tech Blogger - Code Architect

## 主要动画文字修改
✅ 已完成：
- **网页加载动画文字最终版**：
  - 原文："DR. DAYAN" + "JAYATILLEKA" (两行)
  - 新文："JAVA BLOG" (单行)

- **视觉效果设计**：
  - JAVA四个字母：黑色填充 (`is-fill` 类)
  - BLOG四个字母：白色描边 (无 `is-fill` 类)
  - 单行布局，简洁有力

- **导航栏Logo文字**：
  - 原文："Dr. Dayan Jayatilleka"
  - 新文："Java Developer"

- **修改范围**：
  - 桌面版主要动画文字（逐字母动画效果）
  - 移动端动画文字
  - 导航栏品牌标识

现在网页加载时将显示：
**单行：J-A-V-A B-L-O-G** (JAVA黑色，BLOG白色)

## 待完成
- 需要手动完成第二个和第四个卡片的描述文字修改（由于编码问题）
