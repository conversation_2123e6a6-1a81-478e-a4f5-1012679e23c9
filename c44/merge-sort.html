<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8"/>
    <title>稳定排序算法：归并排序的递归实现与时间复杂度分析 | Java技术博客</title>
    <meta content="深入解析归并排序算法，从分治思想到递归实现，包含时间复杂度分析和非递归优化方案。"
          name="description"/>
    <meta content="稳定排序算法：归并排序的递归实现与时间复杂度分析 | Java技术博客" property="og:title"/>
    <meta content="深入解析归并排序算法，从分治思想到递归实现，包含时间复杂度分析和非递归优化方案。"
          property="og:description"/>
    <meta content="width=device-width, initial-scale=1" name="viewport"/>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Microsoft YaHei', sans-serif;
            line-height: 1.8;
            color: #252321;
            background-color: #f6f4f1;
            overflow-x: hidden;
        }
        
        .nav-back {
            position: fixed;
            top: 30px;
            left: 30px;
            z-index: 1000;
        }
        
        .nav-back a {
            display: inline-flex;
            align-items: center;
            padding: 12px 24px;
            background: rgba(37, 35, 33, 0.9);
            color: #f6f4f1;
            text-decoration: none;
            border-radius: 30px;
            font-weight: 500;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            font-size: 14px;
        }
        
        .nav-back a:hover {
            background: #252321;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(37, 35, 33, 0.3);
        }
        
        .page-header {
            background: white;
            padding: 100px 20px 60px 20px;
            text-align: center;
            border-bottom: 1px solid rgba(188, 136, 74, 0.1);
        }
        
        .page-title {
            font-size: clamp(2rem, 4vw, 3rem);
            font-weight: 700;
            margin-bottom: 15px;
            color: #252321;
            line-height: 1.2;
        }
        
        .page-subtitle {
            font-size: clamp(1rem, 2vw, 1.2rem);
            color: #666;
            margin-bottom: 20px;
            font-weight: 400;
        }
        
        .page-date {
            font-size: 0.9rem;
            color: #bc884a;
            font-weight: 500;
            display: inline-block;
            padding: 8px 16px;
            background: rgba(188, 136, 74, 0.1);
            border-radius: 20px;
        }
        
        .content-section {
            max-width: 1000px;
            margin: 0 auto;
            padding: 80px 20px;
        }
        
        .section-title {
            font-size: 2.5rem;
            color: #252321;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
        }
        
        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #bc884a, #8b6914);
            border-radius: 2px;
        }
        
        .algorithm-section {
            background: linear-gradient(135deg, rgba(46, 204, 113, 0.05) 0%, rgba(39, 174, 96, 0.05) 100%);
            padding: 40px;
            border-radius: 20px;
            margin: 40px 0;
            border-left: 5px solid #2ecc71;
        }
        
        .complexity-section {
            background: linear-gradient(135deg, rgba(230, 126, 34, 0.05) 0%, rgba(211, 84, 0, 0.05) 100%);
            padding: 40px;
            border-radius: 20px;
            margin: 40px 0;
            border-left: 5px solid #e67e22;
        }
        
        .overview-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 40px;
            margin: 60px 0;
        }
        
        .overview-card {
            background: white;
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(37, 35, 33, 0.1);
            transition: all 0.3s ease;
            border: 1px solid rgba(188, 136, 74, 0.1);
        }
        
        .overview-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(37, 35, 33, 0.15);
        }
        
        .card-title {
            font-size: 1.5rem;
            color: #252321;
            margin-bottom: 20px;
            font-weight: 600;
        }
        
        .card-content {
            color: #666;
            line-height: 1.8;
        }
        
        .steps-list {
            list-style: none;
            counter-reset: step-counter;
        }
        
        .steps-list li {
            counter-increment: step-counter;
            margin: 20px 0;
            padding: 20px;
            background: rgba(46, 204, 113, 0.05);
            border-radius: 10px;
            position: relative;
            padding-left: 60px;
        }
        
        .steps-list li::before {
            content: counter(step-counter);
            position: absolute;
            left: 20px;
            top: 50%;
            transform: translateY(-50%);
            background: #2ecc71;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 14px;
        }
        
        .code-section {
            margin: 60px 0;
        }
        
        .code-container {
            background: #1e1e1e;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            margin: 30px 0;
        }
        
        .code-header {
            background: #2d2d2d;
            padding: 15px 20px;
            color: #ccc;
            font-size: 14px;
            font-weight: 500;
            border-bottom: 1px solid #444;
        }
        
        .code-block {
            margin: 0;
            padding: 30px;
            background: #1e1e1e;
            color: #d4d4d4;
            font-family: 'Fira Code', 'Monaco', 'Consolas', monospace;
            font-size: 14px;
            line-height: 1.6;
            overflow-x: auto;
            white-space: pre-wrap;
        }
        
        .highlight {
            background: rgba(188, 136, 74, 0.1);
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: 600;
            color: #bc884a;
        }
        
        .visual-demo {
            background: white;
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(37, 35, 33, 0.1);
            margin: 40px 0;
            text-align: center;
        }
        
        .merge-tree {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin: 30px 0;
        }
        
        .tree-level {
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 20px 0;
            gap: 20px;
        }
        
        .tree-node {
            background: #f8f9fa;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            padding: 10px 15px;
            font-weight: bold;
            min-width: 80px;
            text-align: center;
            transition: all 0.3s ease;
        }
        
        .tree-node.split {
            background: #e3f2fd;
            border-color: #2196f3;
            color: #1976d2;
        }
        
        .tree-node.merge {
            background: #e8f5e8;
            border-color: #4caf50;
            color: #2e7d32;
        }
        
        .tree-arrow {
            font-size: 20px;
            color: #666;
            margin: 10px 0;
        }
        
        .complexity-formula {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            font-family: 'Courier New', monospace;
            text-align: center;
            font-size: 1.1rem;
        }
        
        .back-to-top {
            position: fixed;
            bottom: 30px;
            right: 30px;
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #bc884a, #8b6914);
            color: white;
            border: none;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            box-shadow: 0 4px 12px rgba(188, 136, 74, 0.3);
            transition: all 0.3s ease;
            opacity: 0;
            visibility: hidden;
            z-index: 1000;
        }
        
        .back-to-top.visible {
            opacity: 1;
            visibility: visible;
        }
        
        .back-to-top:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 16px rgba(188, 136, 74, 0.4);
        }
        
        .footer-section {
            background: linear-gradient(135deg, #252321 0%, #3a3632 100%);
            color: white;
            text-align: center;
            padding: 60px 20px;
            margin-top: 80px;
        }
        
        .footer-content p {
            margin: 10px 0;
            opacity: 0.9;
        }
        
        .footer-content p:first-child {
            font-size: 1.1rem;
            font-weight: 500;
        }
        
        .footer-content p:last-child {
            font-size: 0.9rem;
            opacity: 0.7;
        }
        
        @media (max-width: 768px) {
            .nav-back {
                top: 15px;
                left: 15px;
            }
            
            .nav-back a {
                padding: 10px 18px;
                font-size: 13px;
            }
            
            .page-header {
                padding: 80px 15px 40px 15px;
            }
            
            .content-section {
                padding: 40px 15px;
            }
            
            .section-title {
                font-size: 2rem;
                margin-bottom: 25px;
            }
            
            .overview-grid {
                grid-template-columns: 1fr;
                gap: 25px;
                margin: 40px 0;
            }
            
            .overview-card {
                padding: 25px 20px;
            }
            
            .algorithm-section,
            .complexity-section {
                padding: 25px 20px;
                margin: 30px 0;
            }
            
            .code-block {
                font-size: 12px;
                padding: 20px;
            }
            
            .tree-level {
                gap: 10px;
            }
            
            .tree-node {
                min-width: 60px;
                padding: 8px 12px;
                font-size: 14px;
            }
            
            .back-to-top {
                bottom: 20px;
                right: 20px;
                width: 45px;
                height: 45px;
                font-size: 18px;
            }
        }
    </style>
</head>
<body>
    <nav class="nav-back">
        <a href="index.html">← 返回首页</a>
    </nav>

    <header class="page-header">
        <h1 class="page-title">稳定排序算法：归并排序</h1>
        <p class="page-subtitle">递归实现与时间复杂度分析</p>
        <div class="page-date">2025-01-06</div>
    </header>

    <main class="content-section">
        <section id="overview">
            <h2 class="section-title">归并排序核心概念</h2>
            
            <div class="overview-grid">
                <div class="overview-card">
                    <h3 class="card-title">算法特点</h3>
                    <div class="card-content">
                        <ul style="list-style-type: disc; margin-left: 20px;">
                            <li><span class="highlight">分治思想</span>：将大问题分解为小问题</li>
                            <li><span class="highlight">稳定排序</span>：相等元素保持相对位置不变</li>
                            <li><span class="highlight">时间复杂度</span>：O(n log n)，性能稳定</li>
                            <li><span class="highlight">空间复杂度</span>：O(n)，需要额外存储空间</li>
                        </ul>
                    </div>
                </div>
                
                <div class="overview-card">
                    <h3 class="card-title">分治三步骤</h3>
                    <div class="card-content">
                        <ol class="steps-list">
                            <li><span class="highlight">分（Divide）</span>：每次从中间切一刀，处理的数据少一半</li>
                            <li><span class="highlight">治（Conquer）</span>：当数据仅剩一个时可以认为有序</li>
                            <li><span class="highlight">合（Combine）</span>：两个有序的结果，可以进行合并排序</li>
                        </ol>
                    </div>
                </div>
            </div>
        </section>

        <section id="recursive-implementation">
            <h2 class="section-title">递归实现详解</h2>

            <div class="visual-demo">
                <h3 class="card-title">分治过程可视化</h3>
                <p style="margin-bottom: 20px;">以数组 [9, 3, 7, 2, 8, 5, 1, 4] 为例：</p>

                <div class="merge-tree">
                    <div class="tree-level">
                        <div class="tree-node split">[9, 3, 7, 2, 8, 5, 1, 4]</div>
                    </div>
                    <div class="tree-arrow">↓ 分</div>
                    <div class="tree-level">
                        <div class="tree-node split">[9, 3, 7, 2]</div>
                        <div class="tree-node split">[8, 5, 1, 4]</div>
                    </div>
                    <div class="tree-arrow">↓ 分</div>
                    <div class="tree-level">
                        <div class="tree-node split">[9, 3]</div>
                        <div class="tree-node split">[7, 2]</div>
                        <div class="tree-node split">[8, 5]</div>
                        <div class="tree-node split">[1, 4]</div>
                    </div>
                    <div class="tree-arrow">↓ 分</div>
                    <div class="tree-level">
                        <div class="tree-node">[9]</div>
                        <div class="tree-node">[3]</div>
                        <div class="tree-node">[7]</div>
                        <div class="tree-node">[2]</div>
                        <div class="tree-node">[8]</div>
                        <div class="tree-node">[5]</div>
                        <div class="tree-node">[1]</div>
                        <div class="tree-node">[4]</div>
                    </div>
                    <div class="tree-arrow">↑ 合</div>
                    <div class="tree-level">
                        <div class="tree-node merge">[3, 9]</div>
                        <div class="tree-node merge">[2, 7]</div>
                        <div class="tree-node merge">[5, 8]</div>
                        <div class="tree-node merge">[1, 4]</div>
                    </div>
                    <div class="tree-arrow">↑ 合</div>
                    <div class="tree-level">
                        <div class="tree-node merge">[2, 3, 7, 9]</div>
                        <div class="tree-node merge">[1, 4, 5, 8]</div>
                    </div>
                    <div class="tree-arrow">↑ 合</div>
                    <div class="tree-level">
                        <div class="tree-node merge">[1, 2, 3, 4, 5, 7, 8, 9]</div>
                    </div>
                </div>
            </div>

            <div class="algorithm-section">
                <h3 class="card-title">递归实现要点</h3>
                <ol class="steps-list">
                    <li><span class="highlight">分割策略</span>：每次从中间位置 m = (left + right) >>> 1 切分数组</li>
                    <li><span class="highlight">递归终止</span>：当 left == right 时，只有一个元素，认为已有序</li>
                    <li><span class="highlight">合并操作</span>：将两个有序子数组合并成一个有序数组</li>
                    <li><span class="highlight">临时数组</span>：使用额外空间存储合并结果，然后复制回原数组</li>
                </ol>
            </div>

            <div class="code-container">
                <div class="code-header">MergeSortTopDown.java - 递归实现</div>
                <pre class="code-block"><code>public class MergeSortTopDown {

    /*
        a1 原始数组
        i~iEnd 第一个有序范围
        j~jEnd 第二个有序范围
        a2 临时数组
     */
    public static void merge(int[] a1, int i, int iEnd, int j, int jEnd, int[] a2) {
        int k = i;
        while (i <= iEnd && j <= jEnd) {
            if (a1[i] < a1[j]) {
                a2[k] = a1[i];
                i++;
            } else {
                a2[k] = a1[j];
                j++;
            }
            k++;
        }
        if (i > iEnd) {
            System.arraycopy(a1, j, a2, k, jEnd - j + 1);
        }
        if (j > jEnd) {
            System.arraycopy(a1, i, a2, k, iEnd - i + 1);
        }
    }

    public static void sort(int[] a1) {
        int[] a2 = new int[a1.length];
        split(a1, 0, a1.length - 1, a2);
    }

    private static void split(int[] a1, int left, int right, int[] a2) {
        // 2. 治 - 递归终止条件
        if (left == right) {
            return;
        }
        // 1. 分 - 分割数组
        int m = (left + right) >>> 1;
        split(a1, left, m, a2);           // 递归处理左半部分
        split(a1, m + 1, right, a2);     // 递归处理右半部分
        // 3. 合 - 合并两个有序数组
        merge(a1, left, m, m + 1, right, a2);
        System.arraycopy(a2, left, a1, left, right - left + 1);
    }

    public static void main(String[] args) {
        int[] a = {9, 3, 7, 2, 8, 5, 1, 4};
        System.out.println(Arrays.toString(a));
        sort(a);
        System.out.println(Arrays.toString(a));
    }
}</code></pre>
            </div>
        </section>

        <section id="complexity-analysis">
            <h2 class="section-title">时间复杂度分析</h2>

            <div class="complexity-section">
                <h3 class="card-title">归并排序复杂度推导</h3>
                <div class="card-content">
                    <p>两个长度为 m 和 n 的数组合并，时间复杂度是 <span class="highlight">m + n</span></p>
                    <br>
                    <p>归并排序的递推关系：</p>
                    <div class="complexity-formula">
                        f(n) = 2f(n/2) + n, f(1) = c
                    </div>
                    <p>等价解：<span class="highlight">f(n) = n log₂n + cn</span></p>
                    <br>
                    <h4 style="color: #e67e22; margin: 20px 0 10px 0;">递推过程展示：</h4>
                    <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; font-family: monospace;">
                        <div>f(8) = 2f(4) + 8</div>
                        <div>f(4) = 2f(2) + 4</div>
                        <div>f(2) = 2f(1) + 2</div>
                        <div>f(1) = 1</div>
                        <br>
                        <div style="color: #e67e22; font-weight: bold;">
                            <div>f(8) = 8 + 24 = 32</div>
                            <div>f(4) = 4 + 8 = 12</div>
                            <div>f(2) = 2 + 2 = 4</div>
                            <div>f(1) = 1</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="overview-grid">
                <div class="overview-card">
                    <h3 class="card-title">归并排序 vs 逐一合并</h3>
                    <div class="card-content">
                        <h4 style="color: #2ecc71; margin-bottom: 15px;">🚀 归并排序：O(n log n)</h4>
                        <ul style="list-style-type: disc; margin-left: 20px; margin-bottom: 20px;">
                            <li>n = 16 时，结果约 80</li>
                            <li>n = 64 时，结果约 448</li>
                        </ul>

                        <h4 style="color: #e74c3c; margin-bottom: 15px;">🐌 逐一合并：O(n²)</h4>
                        <ul style="list-style-type: disc; margin-left: 20px;">
                            <li>n = 16 时，结果 136</li>
                            <li>n = 64 时，结果 2080</li>
                        </ul>
                    </div>
                </div>

                <div class="overview-card">
                    <h3 class="card-title">算法优势</h3>
                    <div class="card-content">
                        <ul style="list-style-type: disc; margin-left: 20px;">
                            <li><span class="highlight">稳定性</span>：相等元素相对位置不变</li>
                            <li><span class="highlight">可预测性</span>：最好、最坏、平均情况都是 O(n log n)</li>
                            <li><span class="highlight">适用性</span>：适合大数据量排序</li>
                            <li><span class="highlight">并行化</span>：天然支持并行处理</li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>

        <section id="iterative-implementation">
            <h2 class="section-title">非递归实现</h2>

            <div class="algorithm-section">
                <h3 class="card-title">自底向上的归并策略</h3>
                <div class="card-content">
                    <p>非递归实现采用<span class="highlight">自底向上</span>的策略，避免了递归调用的开销：</p>
                    <br>
                    <ol style="margin-left: 20px;">
                        <li>从最小的子数组开始（width = 1）</li>
                        <li>逐步增大合并的子数组大小（width *= 2）</li>
                        <li>每次合并相邻的两个子数组</li>
                        <li>直到整个数组有序</li>
                    </ol>
                    <br>
                    <div style="background: rgba(46, 204, 113, 0.1); padding: 20px; border-radius: 10px; margin-top: 20px;">
                        <p style="color: #27ae60; font-weight: 600; margin-bottom: 10px;">💡 优势</p>
                        <p>避免了递归调用栈的开销，对于大数据量更加稳定，不会出现栈溢出问题。</p>
                    </div>
                </div>
            </div>

            <div class="visual-demo">
                <h3 class="card-title">非递归执行过程</h3>
                <p style="margin-bottom: 20px;">以数组 [9, 3, 7, 2, 8, 5, 1, 4] 为例：</p>

                <div style="text-align: left; max-width: 600px; margin: 0 auto;">
                    <div style="margin: 20px 0; padding: 15px; background: #f8f9fa; border-radius: 8px;">
                        <strong>width = 1：</strong>合并相邻的单个元素
                        <div style="margin-top: 10px; font-family: monospace;">
                            [9] [3] → [3, 9]<br>
                            [7] [2] → [2, 7]<br>
                            [8] [5] → [5, 8]<br>
                            [1] [4] → [1, 4]<br>
                            结果：[3, 9, 2, 7, 5, 8, 1, 4]
                        </div>
                    </div>

                    <div style="margin: 20px 0; padding: 15px; background: #e3f2fd; border-radius: 8px;">
                        <strong>width = 2：</strong>合并相邻的两个元素
                        <div style="margin-top: 10px; font-family: monospace;">
                            [3, 9] [2, 7] → [2, 3, 7, 9]<br>
                            [5, 8] [1, 4] → [1, 4, 5, 8]<br>
                            结果：[2, 3, 7, 9, 1, 4, 5, 8]
                        </div>
                    </div>

                    <div style="margin: 20px 0; padding: 15px; background: #e8f5e8; border-radius: 8px;">
                        <strong>width = 4：</strong>合并相邻的四个元素
                        <div style="margin-top: 10px; font-family: monospace;">
                            [2, 3, 7, 9] [1, 4, 5, 8] → [1, 2, 3, 4, 5, 7, 8, 9]<br>
                            结果：[1, 2, 3, 4, 5, 7, 8, 9]
                        </div>
                    </div>
                </div>
            </div>

            <div class="code-container">
                <div class="code-header">MergeSortBottomUp.java - 非递归实现</div>
                <pre class="code-block"><code>public class MergeSortBottomUp {

    /*
        a1 原始数组
        i~iEnd 第一个有序范围
        j~jEnd 第二个有序范围
        a2 临时数组
     */
    public static void merge(int[] a1, int i, int iEnd, int j, int jEnd, int[] a2) {
        int k = i;
        while (i <= iEnd && j <= jEnd) {
            if (a1[i] < a1[j]) {
                a2[k] = a1[i];
                i++;
            } else {
                a2[k] = a1[j];
                j++;
            }
            k++;
        }
        if (i > iEnd) {
            System.arraycopy(a1, j, a2, k, jEnd - j + 1);
        }
        if (j > jEnd) {
            System.arraycopy(a1, i, a2, k, iEnd - i + 1);
        }
    }

    public static void sort(int[] a1) {
        int n = a1.length;
        int[] a2 = new int[n];

        // 自底向上的归并
        for (int width = 1; width < n; width *= 2) {
            for (int i = 0; i < n; i += 2 * width) {
                int m = Integer.min(i + width - 1, n - 1);
                int j = Integer.min(i + 2 * width - 1, n - 1);

                // 确保有两个子数组需要合并
                if (m < j) {
                    merge(a1, i, m, m + 1, j, a2);
                }
            }
            // 将结果复制回原数组
            System.arraycopy(a2, 0, a1, 0, n);
        }
    }

    public static void main(String[] args) {
        int[] a = {9, 3, 7, 2, 8, 5, 1, 4};
        System.out.println(Arrays.toString(a));
        sort(a);
        System.out.println(Arrays.toString(a));
    }
}</code></pre>
            </div>
        </section>

        <section id="summary">
            <h2 class="section-title">总结</h2>

            <div class="overview-card">
                <h3 class="card-title">归并排序的特点与应用</h3>
                <div class="card-content">
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 30px;">
                        <div>
                            <h4 style="color: #2ecc71; margin-bottom: 10px;">✅ 优势</h4>
                            <ul style="list-style-type: disc; margin-left: 20px;">
                                <li>稳定的 O(n log n) 时间复杂度</li>
                                <li>稳定排序，保持相等元素相对位置</li>
                                <li>适合大数据量排序</li>
                                <li>天然支持并行化处理</li>
                                <li>外部排序的理想选择</li>
                            </ul>
                        </div>
                        <div>
                            <h4 style="color: #e74c3c; margin-bottom: 10px;">⚠️ 注意事项</h4>
                            <ul style="list-style-type: disc; margin-left: 20px;">
                                <li>需要 O(n) 额外空间</li>
                                <li>对于小数组可能不如插入排序</li>
                                <li>递归实现可能导致栈溢出</li>
                                <li>常数因子相对较大</li>
                            </ul>
                        </div>
                    </div>
                    <br>
                    <p style="color: #bc884a; font-weight: 600; text-align: center; font-size: 1.1rem;">
                        归并排序是分治思想的经典应用，其稳定性和可预测性使其在实际应用中具有重要价值。
                    </p>
                </div>
            </div>
        </section>
    </main>

    <footer class="footer-section">
        <div class="footer-content">
            <p>&copy; 2025 Java技术博客. 专注于算法与数据结构的深度解析.</p>
            <p>让复杂的算法变得简单易懂</p>
        </div>
    </footer>

    <button class="back-to-top" onclick="scrollToTop()" title="回到顶部">
        ↑
    </button>

    <script>
        // 平滑滚动到顶部功能
        function scrollToTop() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        }

        // 回到顶部按钮显示/隐藏
        window.addEventListener('scroll', function() {
            const backToTopBtn = document.querySelector('.back-to-top');
            if (window.scrollY > 300) {
                backToTopBtn.classList.add('visible');
            } else {
                backToTopBtn.classList.remove('visible');
            }
        });

        // 页面滚动动画
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        // 观察需要动画的元素
        document.addEventListener('DOMContentLoaded', function() {
            const animatedElements = document.querySelectorAll('.overview-card, .code-container, .algorithm-section, .complexity-section, .visual-demo');
            
            animatedElements.forEach(el => {
                el.style.opacity = '0';
                el.style.transform = 'translateY(30px)';
                el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
                observer.observe(el);
            });
        });
    </script>
</body>
</html>
