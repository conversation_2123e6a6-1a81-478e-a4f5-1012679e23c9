html {
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
  font-family: sans-serif;
}

body {
  margin: 0;
}

article, aside, details, figcaption, figure, footer, header, hgroup, main, menu, nav, section, summary {
  display: block;
}

audio, canvas, progress, video {
  vertical-align: baseline;
  display: inline-block;
}

audio:not([controls]) {
  height: 0;
  display: none;
}

[hidden], template {
  display: none;
}

a {
  background-color: #0000;
}

a:active, a:hover {
  outline: 0;
}

abbr[title] {
  border-bottom: 1px dotted;
}

b, strong {
  font-weight: bold;
}

dfn {
  font-style: italic;
}

h1 {
  margin: .67em 0;
  font-size: 2em;
}

mark {
  color: #000;
  background: #ff0;
}

small {
  font-size: 80%;
}

sub, sup {
  vertical-align: baseline;
  font-size: 75%;
  line-height: 0;
  position: relative;
}

sup {
  top: -.5em;
}

sub {
  bottom: -.25em;
}

img {
  border: 0;
}

svg:not(:root) {
  overflow: hidden;
}

hr {
  box-sizing: content-box;
  height: 0;
}

pre {
  overflow: auto;
}

code, kbd, pre, samp {
  font-family: monospace;
  font-size: 1em;
}

button, input, optgroup, select, textarea {
  color: inherit;
  font: inherit;
  margin: 0;
}

button {
  overflow: visible;
}

button, select {
  text-transform: none;
}

button, html input[type="button"], input[type="reset"] {
  -webkit-appearance: button;
  cursor: pointer;
}

button[disabled], html input[disabled] {
  cursor: default;
}

button::-moz-focus-inner, input::-moz-focus-inner {
  border: 0;
  padding: 0;
}

input {
  line-height: normal;
}

input[type="checkbox"], input[type="radio"] {
  box-sizing: border-box;
  padding: 0;
}

input[type="number"]::-webkit-inner-spin-button, input[type="number"]::-webkit-outer-spin-button {
  height: auto;
}

input[type="search"] {
  -webkit-appearance: none;
}

input[type="search"]::-webkit-search-cancel-button, input[type="search"]::-webkit-search-decoration {
  -webkit-appearance: none;
}

legend {
  border: 0;
  padding: 0;
}

textarea {
  overflow: auto;
}

optgroup {
  font-weight: bold;
}

table {
  border-collapse: collapse;
  border-spacing: 0;
}

td, th {
  padding: 0;
}

@font-face {
  font-family: webflow-icons;
  src: url("data:application/x-font-ttf;charset=utf-8;base64,AAEAAAALAIAAAwAwT1MvMg8SBiUAAAC8AAAAYGNtYXDpP+a4AAABHAAAAFxnYXNwAAAAEAAAAXgAAAAIZ2x5ZmhS2XEAAAGAAAADHGhlYWQTFw3HAAAEnAAAADZoaGVhCXYFgQAABNQAAAAkaG10eCe4A1oAAAT4AAAAMGxvY2EDtALGAAAFKAAAABptYXhwABAAPgAABUQAAAAgbmFtZSoCsMsAAAVkAAABznBvc3QAAwAAAAAHNAAAACAAAwP4AZAABQAAApkCzAAAAI8CmQLMAAAB6wAzAQkAAAAAAAAAAAAAAAAAAAABEAAAAAAAAAAAAAAAAAAAAABAAADpAwPA/8AAQAPAAEAAAAABAAAAAAAAAAAAAAAgAAAAAAADAAAAAwAAABwAAQADAAAAHAADAAEAAAAcAAQAQAAAAAwACAACAAQAAQAg5gPpA//9//8AAAAAACDmAOkA//3//wAB/+MaBBcIAAMAAQAAAAAAAAAAAAAAAAABAAH//wAPAAEAAAAAAAAAAAACAAA3OQEAAAAAAQAAAAAAAAAAAAIAADc5AQAAAAABAAAAAAAAAAAAAgAANzkBAAAAAAEBIAAAAyADgAAFAAAJAQcJARcDIP5AQAGA/oBAAcABwED+gP6AQAABAOAAAALgA4AABQAAEwEXCQEH4AHAQP6AAYBAAcABwED+gP6AQAAAAwDAAOADQALAAA8AHwAvAAABISIGHQEUFjMhMjY9ATQmByEiBh0BFBYzITI2PQE0JgchIgYdARQWMyEyNj0BNCYDIP3ADRMTDQJADRMTDf3ADRMTDQJADRMTDf3ADRMTDQJADRMTAsATDSANExMNIA0TwBMNIA0TEw0gDRPAEw0gDRMTDSANEwAAAAABAJ0AtAOBApUABQAACQIHCQEDJP7r/upcAXEBcgKU/usBFVz+fAGEAAAAAAL//f+9BAMDwwAEAAkAABcBJwEXAwE3AQdpA5ps/GZsbAOabPxmbEMDmmz8ZmwDmvxmbAOabAAAAgAA/8AEAAPAAB0AOwAABSInLgEnJjU0Nz4BNzYzMTIXHgEXFhUUBw4BBwYjNTI3PgE3NjU0Jy4BJyYjMSIHDgEHBhUUFx4BFxYzAgBqXV6LKCgoKIteXWpqXV6LKCgoKIteXWpVSktvICEhIG9LSlVVSktvICEhIG9LSlVAKCiLXl1qal1eiygoKCiLXl1qal1eiygoZiEgb0tKVVVKS28gISEgb0tKVVVKS28gIQABAAABwAIAA8AAEgAAEzQ3PgE3NjMxFSIHDgEHBhUxIwAoKIteXWpVSktvICFmAcBqXV6LKChmISBvS0pVAAAAAgAA/8AFtgPAADIAOgAAARYXHgEXFhUUBw4BBwYHIxUhIicuAScmNTQ3PgE3NjMxOAExNDc+ATc2MzIXHgEXFhcVATMJATMVMzUEjD83NlAXFxYXTjU1PQL8kz01Nk8XFxcXTzY1PSIjd1BQWlJJSXInJw3+mdv+2/7c25MCUQYcHFg5OUA/ODlXHBwIAhcXTzY1PTw1Nk8XF1tQUHcjIhwcYUNDTgL+3QFt/pOTkwABAAAAAQAAmM7nP18PPPUACwQAAAAAANciZKUAAAAA1yJkpf/9/70FtgPDAAAACAACAAAAAAAAAAEAAAPA/8AAAAW3//3//QW2AAEAAAAAAAAAAAAAAAAAAAAMBAAAAAAAAAAAAAAAAgAAAAQAASAEAADgBAAAwAQAAJ0EAP/9BAAAAAQAAAAFtwAAAAAAAAAKABQAHgAyAEYAjACiAL4BFgE2AY4AAAABAAAADAA8AAMAAAAAAAIAAAAAAAAAAAAAAAAAAAAAAAAADgCuAAEAAAAAAAEADQAAAAEAAAAAAAIABwCWAAEAAAAAAAMADQBIAAEAAAAAAAQADQCrAAEAAAAAAAUACwAnAAEAAAAAAAYADQBvAAEAAAAAAAoAGgDSAAMAAQQJAAEAGgANAAMAAQQJAAIADgCdAAMAAQQJAAMAGgBVAAMAAQQJAAQAGgC4AAMAAQQJAAUAFgAyAAMAAQQJAAYAGgB8AAMAAQQJAAoANADsd2ViZmxvdy1pY29ucwB3AGUAYgBmAGwAbwB3AC0AaQBjAG8AbgBzVmVyc2lvbiAxLjAAVgBlAHIAcwBpAG8AbgAgADEALgAwd2ViZmxvdy1pY29ucwB3AGUAYgBmAGwAbwB3AC0AaQBjAG8AbgBzd2ViZmxvdy1pY29ucwB3AGUAYgBmAGwAbwB3AC0AaQBjAG8AbgBzUmVndWxhcgBSAGUAZwB1AGwAYQByd2ViZmxvdy1pY29ucwB3AGUAYgBmAGwAbwB3AC0AaQBjAG8AbgBzRm9udCBnZW5lcmF0ZWQgYnkgSWNvTW9vbi4ARgBvAG4AdAAgAGcAZQBuAGUAcgBhAHQAZQBkACAAYgB5ACAASQBjAG8ATQBvAG8AbgAuAAAAAwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==") format("truetype");
  font-weight: normal;
  font-style: normal;
}

[class^="w-icon-"], [class*=" w-icon-"] {
  speak: none;
  font-variant: normal;
  text-transform: none;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-style: normal;
  font-weight: normal;
  line-height: 1;
  font-family: webflow-icons !important;
}

.w-icon-slider-right:before {
  content: "";
}

.w-icon-slider-left:before {
  content: "";
}

.w-icon-nav-menu:before {
  content: "";
}

.w-icon-arrow-down:before, .w-icon-dropdown-toggle:before {
  content: "";
}

.w-icon-file-upload-remove:before {
  content: "";
}

.w-icon-file-upload-icon:before {
  content: "";
}

* {
  box-sizing: border-box;
}

html {
  height: 100%;
}

body {
  color: #333;
  background-color: #fff;
  min-height: 100%;
  margin: 0;
  font-family: Arial, sans-serif;
  font-size: 14px;
  line-height: 20px;
}

img {
  vertical-align: middle;
  max-width: 100%;
  display: inline-block;
}

html.w-mod-touch * {
  background-attachment: scroll !important;
}

.w-block {
  display: block;
}

.w-inline-block {
  max-width: 100%;
  display: inline-block;
}

.w-clearfix:before, .w-clearfix:after {
  content: " ";
  grid-area: 1 / 1 / 2 / 2;
  display: table;
}

.w-clearfix:after {
  clear: both;
}

.w-hidden {
  display: none;
}

.w-button {
  color: #fff;
  line-height: inherit;
  cursor: pointer;
  background-color: #3898ec;
  border: 0;
  border-radius: 0;
  padding: 9px 15px;
  text-decoration: none;
  display: inline-block;
}

input.w-button {
  -webkit-appearance: button;
}

html[data-w-dynpage] [data-w-cloak] {
  color: #0000 !important;
}

.w-code-block {
  margin: unset;
}

pre.w-code-block code {
  all: inherit;
}

.w-optimization {
  display: contents;
}

.w-webflow-badge, .w-webflow-badge > img {
  box-sizing: unset;
  width: unset;
  height: unset;
  max-height: unset;
  max-width: unset;
  min-height: unset;
  min-width: unset;
  margin: unset;
  padding: unset;
  float: unset;
  clear: unset;
  border: unset;
  border-radius: unset;
  background: unset;
  background-image: unset;
  background-position: unset;
  background-size: unset;
  background-repeat: unset;
  background-origin: unset;
  background-clip: unset;
  background-attachment: unset;
  background-color: unset;
  box-shadow: unset;
  transform: unset;
  direction: unset;
  font-family: unset;
  font-weight: unset;
  color: unset;
  font-size: unset;
  line-height: unset;
  font-style: unset;
  font-variant: unset;
  text-align: unset;
  letter-spacing: unset;
  -webkit-text-decoration: unset;
  text-decoration: unset;
  text-indent: unset;
  text-transform: unset;
  list-style-type: unset;
  text-shadow: unset;
  vertical-align: unset;
  cursor: unset;
  white-space: unset;
  word-break: unset;
  word-spacing: unset;
  word-wrap: unset;
  transition: unset;
}

.w-webflow-badge {
  white-space: nowrap;
  cursor: pointer;
  box-shadow: 0 0 0 1px #0000001a, 0 1px 3px #0000001a;
  visibility: visible !important;
  opacity: 1 !important;
  z-index: 2147483647 !important;
  color: #aaadb0 !important;
  overflow: unset !important;
  background-color: #fff !important;
  border-radius: 3px !important;
  width: auto !important;
  height: auto !important;
  margin: 0 !important;
  padding: 6px !important;
  font-size: 12px !important;
  line-height: 14px !important;
  text-decoration: none !important;
  display: inline-block !important;
  position: fixed !important;
  inset: auto 12px 12px auto !important;
  transform: none !important;
}

.w-webflow-badge > img {
  position: unset;
  visibility: unset !important;
  opacity: 1 !important;
  vertical-align: middle !important;
  display: inline-block !important;
}

h1, h2, h3, h4, h5, h6 {
  margin-bottom: 10px;
  font-weight: bold;
}

h1 {
  margin-top: 20px;
  font-size: 38px;
  line-height: 44px;
}

h2 {
  margin-top: 20px;
  font-size: 32px;
  line-height: 36px;
}

h3 {
  margin-top: 20px;
  font-size: 24px;
  line-height: 30px;
}

h4 {
  margin-top: 10px;
  font-size: 18px;
  line-height: 24px;
}

h5 {
  margin-top: 10px;
  font-size: 14px;
  line-height: 20px;
}

h6 {
  margin-top: 10px;
  font-size: 12px;
  line-height: 18px;
}

p {
  margin-top: 0;
  margin-bottom: 10px;
}

blockquote {
  border-left: 5px solid #e2e2e2;
  margin: 0 0 10px;
  padding: 10px 20px;
  font-size: 18px;
  line-height: 22px;
}

figure {
  margin: 0 0 10px;
}

figcaption {
  text-align: center;
  margin-top: 5px;
}

ul, ol {
  margin-top: 0;
  margin-bottom: 10px;
  padding-left: 40px;
}

.w-list-unstyled {
  padding-left: 0;
  list-style: none;
}

.w-embed:before, .w-embed:after {
  content: " ";
  grid-area: 1 / 1 / 2 / 2;
  display: table;
}

.w-embed:after {
  clear: both;
}

.w-video {
  width: 100%;
  padding: 0;
  position: relative;
}

.w-video iframe, .w-video object, .w-video embed {
  border: none;
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
}

fieldset {
  border: 0;
  margin: 0;
  padding: 0;
}

button, [type="button"], [type="reset"] {
  cursor: pointer;
  -webkit-appearance: button;
  border: 0;
}

.w-form {
  margin: 0 0 15px;
}

.w-form-done {
  text-align: center;
  background-color: #ddd;
  padding: 20px;
  display: none;
}

.w-form-fail {
  background-color: #ffdede;
  margin-top: 10px;
  padding: 10px;
  display: none;
}

label {
  margin-bottom: 5px;
  font-weight: bold;
  display: block;
}

.w-input, .w-select {
  color: #333;
  vertical-align: middle;
  background-color: #fff;
  border: 1px solid #ccc;
  width: 100%;
  height: 38px;
  margin-bottom: 10px;
  padding: 8px 12px;
  font-size: 14px;
  line-height: 1.42857;
  display: block;
}

.w-input::placeholder, .w-select::placeholder {
  color: #999;
}

.w-input:focus, .w-select:focus {
  border-color: #3898ec;
  outline: 0;
}

.w-input[disabled], .w-select[disabled], .w-input[readonly], .w-select[readonly], fieldset[disabled] .w-input, fieldset[disabled] .w-select {
  cursor: not-allowed;
}

.w-input[disabled]:not(.w-input-disabled), .w-select[disabled]:not(.w-input-disabled), .w-input[readonly], .w-select[readonly], fieldset[disabled]:not(.w-input-disabled) .w-input, fieldset[disabled]:not(.w-input-disabled) .w-select {
  background-color: #eee;
}

textarea.w-input, textarea.w-select {
  height: auto;
}

.w-select {
  background-color: #f3f3f3;
}

.w-select[multiple] {
  height: auto;
}

.w-form-label {
  cursor: pointer;
  margin-bottom: 0;
  font-weight: normal;
  display: inline-block;
}

.w-radio {
  margin-bottom: 5px;
  padding-left: 20px;
  display: block;
}

.w-radio:before, .w-radio:after {
  content: " ";
  grid-area: 1 / 1 / 2 / 2;
  display: table;
}

.w-radio:after {
  clear: both;
}

.w-radio-input {
  float: left;
  margin: 3px 0 0 -20px;
  line-height: normal;
}

.w-file-upload {
  margin-bottom: 10px;
  display: block;
}

.w-file-upload-input {
  opacity: 0;
  z-index: -100;
  width: .1px;
  height: .1px;
  position: absolute;
  overflow: hidden;
}

.w-file-upload-default, .w-file-upload-uploading, .w-file-upload-success {
  color: #333;
  display: inline-block;
}

.w-file-upload-error {
  margin-top: 10px;
  display: block;
}

.w-file-upload-default.w-hidden, .w-file-upload-uploading.w-hidden, .w-file-upload-error.w-hidden, .w-file-upload-success.w-hidden {
  display: none;
}

.w-file-upload-uploading-btn {
  cursor: pointer;
  background-color: #fafafa;
  border: 1px solid #ccc;
  margin: 0;
  padding: 8px 12px;
  font-size: 14px;
  font-weight: normal;
  display: flex;
}

.w-file-upload-file {
  background-color: #fafafa;
  border: 1px solid #ccc;
  flex-grow: 1;
  justify-content: space-between;
  margin: 0;
  padding: 8px 9px 8px 11px;
  display: flex;
}

.w-file-upload-file-name {
  font-size: 14px;
  font-weight: normal;
  display: block;
}

.w-file-remove-link {
  cursor: pointer;
  width: auto;
  height: auto;
  margin-top: 3px;
  margin-left: 10px;
  padding: 3px;
  display: block;
}

.w-icon-file-upload-remove {
  margin: auto;
  font-size: 10px;
}

.w-file-upload-error-msg {
  color: #ea384c;
  padding: 2px 0;
  display: inline-block;
}

.w-file-upload-info {
  padding: 0 12px;
  line-height: 38px;
  display: inline-block;
}

.w-file-upload-label {
  cursor: pointer;
  background-color: #fafafa;
  border: 1px solid #ccc;
  margin: 0;
  padding: 8px 12px;
  font-size: 14px;
  font-weight: normal;
  display: inline-block;
}

.w-icon-file-upload-icon, .w-icon-file-upload-uploading {
  width: 20px;
  margin-right: 8px;
  display: inline-block;
}

.w-icon-file-upload-uploading {
  height: 20px;
}

.w-container {
  max-width: 940px;
  margin-left: auto;
  margin-right: auto;
}

.w-container:before, .w-container:after {
  content: " ";
  grid-area: 1 / 1 / 2 / 2;
  display: table;
}

.w-container:after {
  clear: both;
}

.w-container .w-row {
  margin-left: -10px;
  margin-right: -10px;
}

.w-row:before, .w-row:after {
  content: " ";
  grid-area: 1 / 1 / 2 / 2;
  display: table;
}

.w-row:after {
  clear: both;
}

.w-row .w-row {
  margin-left: 0;
  margin-right: 0;
}

.w-col {
  float: left;
  width: 100%;
  min-height: 1px;
  padding-left: 10px;
  padding-right: 10px;
  position: relative;
}

.w-col .w-col {
  padding-left: 0;
  padding-right: 0;
}

.w-col-1 {
  width: 8.33333%;
}

.w-col-2 {
  width: 16.6667%;
}

.w-col-3 {
  width: 25%;
}

.w-col-4 {
  width: 33.3333%;
}

.w-col-5 {
  width: 41.6667%;
}

.w-col-6 {
  width: 50%;
}

.w-col-7 {
  width: 58.3333%;
}

.w-col-8 {
  width: 66.6667%;
}

.w-col-9 {
  width: 75%;
}

.w-col-10 {
  width: 83.3333%;
}

.w-col-11 {
  width: 91.6667%;
}

.w-col-12 {
  width: 100%;
}

.w-hidden-main {
  display: none !important;
}

@media screen and (max-width: 991px) {
  .w-container {
    max-width: 728px;
  }

  .w-hidden-main {
    display: inherit !important;
  }

  .w-hidden-medium {
    display: none !important;
  }

  .w-col-medium-1 {
    width: 8.33333%;
  }

  .w-col-medium-2 {
    width: 16.6667%;
  }

  .w-col-medium-3 {
    width: 25%;
  }

  .w-col-medium-4 {
    width: 33.3333%;
  }

  .w-col-medium-5 {
    width: 41.6667%;
  }

  .w-col-medium-6 {
    width: 50%;
  }

  .w-col-medium-7 {
    width: 58.3333%;
  }

  .w-col-medium-8 {
    width: 66.6667%;
  }

  .w-col-medium-9 {
    width: 75%;
  }

  .w-col-medium-10 {
    width: 83.3333%;
  }

  .w-col-medium-11 {
    width: 91.6667%;
  }

  .w-col-medium-12 {
    width: 100%;
  }

  .w-col-stack {
    width: 100%;
    left: auto;
    right: auto;
  }
}

@media screen and (max-width: 767px) {
  .w-hidden-main, .w-hidden-medium {
    display: inherit !important;
  }

  .w-hidden-small {
    display: none !important;
  }

  .w-row, .w-container .w-row {
    margin-left: 0;
    margin-right: 0;
  }

  .w-col {
    width: 100%;
    left: auto;
    right: auto;
  }

  .w-col-small-1 {
    width: 8.33333%;
  }

  .w-col-small-2 {
    width: 16.6667%;
  }

  .w-col-small-3 {
    width: 25%;
  }

  .w-col-small-4 {
    width: 33.3333%;
  }

  .w-col-small-5 {
    width: 41.6667%;
  }

  .w-col-small-6 {
    width: 50%;
  }

  .w-col-small-7 {
    width: 58.3333%;
  }

  .w-col-small-8 {
    width: 66.6667%;
  }

  .w-col-small-9 {
    width: 75%;
  }

  .w-col-small-10 {
    width: 83.3333%;
  }

  .w-col-small-11 {
    width: 91.6667%;
  }

  .w-col-small-12 {
    width: 100%;
  }
}

@media screen and (max-width: 479px) {
  .w-container {
    max-width: none;
  }

  .w-hidden-main, .w-hidden-medium, .w-hidden-small {
    display: inherit !important;
  }

  .w-hidden-tiny {
    display: none !important;
  }

  .w-col {
    width: 100%;
  }

  .w-col-tiny-1 {
    width: 8.33333%;
  }

  .w-col-tiny-2 {
    width: 16.6667%;
  }

  .w-col-tiny-3 {
    width: 25%;
  }

  .w-col-tiny-4 {
    width: 33.3333%;
  }

  .w-col-tiny-5 {
    width: 41.6667%;
  }

  .w-col-tiny-6 {
    width: 50%;
  }

  .w-col-tiny-7 {
    width: 58.3333%;
  }

  .w-col-tiny-8 {
    width: 66.6667%;
  }

  .w-col-tiny-9 {
    width: 75%;
  }

  .w-col-tiny-10 {
    width: 83.3333%;
  }

  .w-col-tiny-11 {
    width: 91.6667%;
  }

  .w-col-tiny-12 {
    width: 100%;
  }
}

.w-widget {
  position: relative;
}

.w-widget-map {
  width: 100%;
  height: 400px;
}

.w-widget-map label {
  width: auto;
  display: inline;
}

.w-widget-map img {
  max-width: inherit;
}

.w-widget-map .gm-style-iw {
  text-align: center;
}

.w-widget-map .gm-style-iw > button {
  display: none !important;
}

.w-widget-twitter {
  overflow: hidden;
}

.w-widget-twitter-count-shim {
  vertical-align: top;
  text-align: center;
  background: #fff;
  border: 1px solid #758696;
  border-radius: 3px;
  width: 28px;
  height: 20px;
  display: inline-block;
  position: relative;
}

.w-widget-twitter-count-shim * {
  pointer-events: none;
  -webkit-user-select: none;
  user-select: none;
}

.w-widget-twitter-count-shim .w-widget-twitter-count-inner {
  text-align: center;
  color: #999;
  font-family: serif;
  font-size: 15px;
  line-height: 12px;
  position: relative;
}

.w-widget-twitter-count-shim .w-widget-twitter-count-clear {
  display: block;
  position: relative;
}

.w-widget-twitter-count-shim.w--large {
  width: 36px;
  height: 28px;
}

.w-widget-twitter-count-shim.w--large .w-widget-twitter-count-inner {
  font-size: 18px;
  line-height: 18px;
}

.w-widget-twitter-count-shim:not(.w--vertical) {
  margin-left: 5px;
  margin-right: 8px;
}

.w-widget-twitter-count-shim:not(.w--vertical).w--large {
  margin-left: 6px;
}

.w-widget-twitter-count-shim:not(.w--vertical):before, .w-widget-twitter-count-shim:not(.w--vertical):after {
  content: " ";
  pointer-events: none;
  border: solid #0000;
  width: 0;
  height: 0;
  position: absolute;
  top: 50%;
  left: 0;
}

.w-widget-twitter-count-shim:not(.w--vertical):before {
  border-width: 4px;
  border-color: #75869600 #5d6c7b #75869600 #75869600;
  margin-top: -4px;
  margin-left: -9px;
}

.w-widget-twitter-count-shim:not(.w--vertical).w--large:before {
  border-width: 5px;
  margin-top: -5px;
  margin-left: -10px;
}

.w-widget-twitter-count-shim:not(.w--vertical):after {
  border-width: 4px;
  border-color: #fff0 #fff #fff0 #fff0;
  margin-top: -4px;
  margin-left: -8px;
}

.w-widget-twitter-count-shim:not(.w--vertical).w--large:after {
  border-width: 5px;
  margin-top: -5px;
  margin-left: -9px;
}

.w-widget-twitter-count-shim.w--vertical {
  width: 61px;
  height: 33px;
  margin-bottom: 8px;
}

.w-widget-twitter-count-shim.w--vertical:before, .w-widget-twitter-count-shim.w--vertical:after {
  content: " ";
  pointer-events: none;
  border: solid #0000;
  width: 0;
  height: 0;
  position: absolute;
  top: 100%;
  left: 50%;
}

.w-widget-twitter-count-shim.w--vertical:before {
  border-width: 5px;
  border-color: #5d6c7b #75869600 #75869600;
  margin-left: -5px;
}

.w-widget-twitter-count-shim.w--vertical:after {
  border-width: 4px;
  border-color: #fff #fff0 #fff0;
  margin-left: -4px;
}

.w-widget-twitter-count-shim.w--vertical .w-widget-twitter-count-inner {
  font-size: 18px;
  line-height: 22px;
}

.w-widget-twitter-count-shim.w--vertical.w--large {
  width: 76px;
}

.w-background-video {
  color: #fff;
  height: 500px;
  position: relative;
  overflow: hidden;
}

.w-background-video > video {
  object-fit: cover;
  z-index: -100;
  background-position: 50%;
  background-size: cover;
  width: 100%;
  height: 100%;
  margin: auto;
  position: absolute;
  inset: -100%;
}

.w-background-video > video::-webkit-media-controls-start-playback-button {
  -webkit-appearance: none;
  display: none !important;
}

.w-background-video--control {
  background-color: #0000;
  padding: 0;
  position: absolute;
  bottom: 1em;
  right: 1em;
}

.w-background-video--control > [hidden] {
  display: none !important;
}

.w-slider {
  text-align: center;
  clear: both;
  -webkit-tap-highlight-color: #0000;
  tap-highlight-color: #0000;
  background: #ddd;
  height: 300px;
  position: relative;
}

.w-slider-mask {
  z-index: 1;
  white-space: nowrap;
  height: 100%;
  display: block;
  position: relative;
  left: 0;
  right: 0;
  overflow: hidden;
}

.w-slide {
  vertical-align: top;
  white-space: normal;
  text-align: left;
  width: 100%;
  height: 100%;
  display: inline-block;
  position: relative;
}

.w-slider-nav {
  z-index: 2;
  text-align: center;
  -webkit-tap-highlight-color: #0000;
  tap-highlight-color: #0000;
  height: 40px;
  margin: auto;
  padding-top: 10px;
  position: absolute;
  inset: auto 0 0;
}

.w-slider-nav.w-round > div {
  border-radius: 100%;
}

.w-slider-nav.w-num > div {
  font-size: inherit;
  line-height: inherit;
  width: auto;
  height: auto;
  padding: .2em .5em;
}

.w-slider-nav.w-shadow > div {
  box-shadow: 0 0 3px #3336;
}

.w-slider-nav-invert {
  color: #fff;
}

.w-slider-nav-invert > div {
  background-color: #2226;
}

.w-slider-nav-invert > div.w-active {
  background-color: #222;
}

.w-slider-dot {
  cursor: pointer;
  background-color: #fff6;
  width: 1em;
  height: 1em;
  margin: 0 3px .5em;
  transition: background-color .1s, color .1s;
  display: inline-block;
  position: relative;
}

.w-slider-dot.w-active {
  background-color: #fff;
}

.w-slider-dot:focus {
  outline: none;
  box-shadow: 0 0 0 2px #fff;
}

.w-slider-dot:focus.w-active {
  box-shadow: none;
}

.w-slider-arrow-left, .w-slider-arrow-right {
  cursor: pointer;
  color: #fff;
  -webkit-tap-highlight-color: #0000;
  tap-highlight-color: #0000;
  -webkit-user-select: none;
  user-select: none;
  width: 80px;
  margin: auto;
  font-size: 40px;
  position: absolute;
  inset: 0;
  overflow: hidden;
}

.w-slider-arrow-left [class^="w-icon-"], .w-slider-arrow-right [class^="w-icon-"], .w-slider-arrow-left [class*=" w-icon-"], .w-slider-arrow-right [class*=" w-icon-"] {
  position: absolute;
}

.w-slider-arrow-left:focus, .w-slider-arrow-right:focus {
  outline: 0;
}

.w-slider-arrow-left {
  z-index: 3;
  right: auto;
}

.w-slider-arrow-right {
  z-index: 4;
  left: auto;
}

.w-icon-slider-left, .w-icon-slider-right {
  width: 1em;
  height: 1em;
  margin: auto;
  inset: 0;
}

.w-slider-aria-label {
  clip: rect(0 0 0 0);
  border: 0;
  width: 1px;
  height: 1px;
  margin: -1px;
  padding: 0;
  position: absolute;
  overflow: hidden;
}

.w-slider-force-show {
  display: block !important;
}

.w-dropdown {
  text-align: left;
  z-index: 900;
  margin-left: auto;
  margin-right: auto;
  display: inline-block;
  position: relative;
}

.w-dropdown-btn, .w-dropdown-toggle, .w-dropdown-link {
  vertical-align: top;
  color: #222;
  text-align: left;
  white-space: nowrap;
  margin-left: auto;
  margin-right: auto;
  padding: 20px;
  text-decoration: none;
  position: relative;
}

.w-dropdown-toggle {
  -webkit-user-select: none;
  user-select: none;
  cursor: pointer;
  padding-right: 40px;
  display: inline-block;
}

.w-dropdown-toggle:focus {
  outline: 0;
}

.w-icon-dropdown-toggle {
  width: 1em;
  height: 1em;
  margin: auto 20px auto auto;
  position: absolute;
  top: 0;
  bottom: 0;
  right: 0;
}

.w-dropdown-list {
  background: #ddd;
  min-width: 100%;
  display: none;
  position: absolute;
}

.w-dropdown-list.w--open {
  display: block;
}

.w-dropdown-link {
  color: #222;
  padding: 10px 20px;
  display: block;
}

.w-dropdown-link.w--current {
  color: #0082f3;
}

.w-dropdown-link:focus {
  outline: 0;
}

@media screen and (max-width: 767px) {
  .w-nav-brand {
    padding-left: 10px;
  }
}

.w-lightbox-backdrop {
  cursor: auto;
  letter-spacing: normal;
  text-indent: 0;
  text-shadow: none;
  text-transform: none;
  visibility: visible;
  white-space: normal;
  word-break: normal;
  word-spacing: normal;
  word-wrap: normal;
  color: #fff;
  text-align: center;
  z-index: 2000;
  opacity: 0;
  -webkit-user-select: none;
  -moz-user-select: none;
  -webkit-tap-highlight-color: transparent;
  background: #000000e6;
  outline: 0;
  font-family: Helvetica Neue, Helvetica, Ubuntu, Segoe UI, Verdana, sans-serif;
  font-size: 17px;
  font-style: normal;
  font-weight: 300;
  line-height: 1.2;
  list-style: disc;
  position: fixed;
  inset: 0;
  -webkit-transform: translate(0);
}

.w-lightbox-backdrop, .w-lightbox-container {
  -webkit-overflow-scrolling: touch;
  height: 100%;
  overflow: auto;
}

.w-lightbox-content {
  height: 100vh;
  position: relative;
  overflow: hidden;
}

.w-lightbox-view {
  opacity: 0;
  width: 100vw;
  height: 100vh;
  position: absolute;
}

.w-lightbox-view:before {
  content: "";
  height: 100vh;
}

.w-lightbox-group, .w-lightbox-group .w-lightbox-view, .w-lightbox-group .w-lightbox-view:before {
  height: 86vh;
}

.w-lightbox-frame, .w-lightbox-view:before {
  vertical-align: middle;
  display: inline-block;
}

.w-lightbox-figure {
  margin: 0;
  position: relative;
}

.w-lightbox-group .w-lightbox-figure {
  cursor: pointer;
}

.w-lightbox-img {
  width: auto;
  max-width: none;
  height: auto;
}

.w-lightbox-image {
  float: none;
  max-width: 100vw;
  max-height: 100vh;
  display: block;
}

.w-lightbox-group .w-lightbox-image {
  max-height: 86vh;
}

.w-lightbox-caption {
  text-align: left;
  text-overflow: ellipsis;
  white-space: nowrap;
  background: #0006;
  padding: .5em 1em;
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  overflow: hidden;
}

.w-lightbox-embed {
  width: 100%;
  height: 100%;
  position: absolute;
  inset: 0;
}

.w-lightbox-control {
  cursor: pointer;
  background-position: center;
  background-repeat: no-repeat;
  background-size: 24px;
  width: 4em;
  transition: all .3s;
  position: absolute;
  top: 0;
}

.w-lightbox-left {
  background-image: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9Ii0yMCAwIDI0IDQwIiB3aWR0aD0iMjQiIGhlaWdodD0iNDAiPjxnIHRyYW5zZm9ybT0icm90YXRlKDQ1KSI+PHBhdGggZD0ibTAgMGg1djIzaDIzdjVoLTI4eiIgb3BhY2l0eT0iLjQiLz48cGF0aCBkPSJtMSAxaDN2MjNoMjN2M2gtMjZ6IiBmaWxsPSIjZmZmIi8+PC9nPjwvc3ZnPg==");
  display: none;
  bottom: 0;
  left: 0;
}

.w-lightbox-right {
  background-image: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9Ii00IDAgMjQgNDAiIHdpZHRoPSIyNCIgaGVpZ2h0PSI0MCI+PGcgdHJhbnNmb3JtPSJyb3RhdGUoNDUpIj48cGF0aCBkPSJtMC0waDI4djI4aC01di0yM2gtMjN6IiBvcGFjaXR5PSIuNCIvPjxwYXRoIGQ9Im0xIDFoMjZ2MjZoLTN2LTIzaC0yM3oiIGZpbGw9IiNmZmYiLz48L2c+PC9zdmc+");
  display: none;
  bottom: 0;
  right: 0;
}

.w-lightbox-close {
  background-image: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9Ii00IDAgMTggMTciIHdpZHRoPSIxOCIgaGVpZ2h0PSIxNyI+PGcgdHJhbnNmb3JtPSJyb3RhdGUoNDUpIj48cGF0aCBkPSJtMCAwaDd2LTdoNXY3aDd2NWgtN3Y3aC01di03aC03eiIgb3BhY2l0eT0iLjQiLz48cGF0aCBkPSJtMSAxaDd2LTdoM3Y3aDd2M2gtN3Y3aC0zdi03aC03eiIgZmlsbD0iI2ZmZiIvPjwvZz48L3N2Zz4=");
  background-size: 18px;
  height: 2.6em;
  right: 0;
}

.w-lightbox-strip {
  white-space: nowrap;
  padding: 0 1vh;
  line-height: 0;
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  overflow: auto hidden;
}

.w-lightbox-item {
  box-sizing: content-box;
  cursor: pointer;
  width: 10vh;
  padding: 2vh 1vh;
  display: inline-block;
  -webkit-transform: translate3d(0, 0, 0);
}

.w-lightbox-active {
  opacity: .3;
}

.w-lightbox-thumbnail {
  background: #222;
  height: 10vh;
  position: relative;
  overflow: hidden;
}

.w-lightbox-thumbnail-image {
  position: absolute;
  top: 0;
  left: 0;
}

.w-lightbox-thumbnail .w-lightbox-tall {
  width: 100%;
  top: 50%;
  transform: translate(0, -50%);
}

.w-lightbox-thumbnail .w-lightbox-wide {
  height: 100%;
  left: 50%;
  transform: translate(-50%);
}

.w-lightbox-spinner {
  box-sizing: border-box;
  border: 5px solid #0006;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  margin-top: -20px;
  margin-left: -20px;
  animation: .8s linear infinite spin;
  position: absolute;
  top: 50%;
  left: 50%;
}

.w-lightbox-spinner:after {
  content: "";
  border: 3px solid #0000;
  border-bottom-color: #fff;
  border-radius: 50%;
  position: absolute;
  inset: -4px;
}

.w-lightbox-hide {
  display: none;
}

.w-lightbox-noscroll {
  overflow: hidden;
}

@media (min-width: 768px) {
  .w-lightbox-content {
    height: 96vh;
    margin-top: 2vh;
  }

  .w-lightbox-view, .w-lightbox-view:before {
    height: 96vh;
  }

  .w-lightbox-group, .w-lightbox-group .w-lightbox-view, .w-lightbox-group .w-lightbox-view:before {
    height: 84vh;
  }

  .w-lightbox-image {
    max-width: 96vw;
    max-height: 96vh;
  }

  .w-lightbox-group .w-lightbox-image {
    max-width: 82.3vw;
    max-height: 84vh;
  }

  .w-lightbox-left, .w-lightbox-right {
    opacity: .5;
    display: block;
  }

  .w-lightbox-close {
    opacity: .8;
  }

  .w-lightbox-control:hover {
    opacity: 1;
  }
}

.w-lightbox-inactive, .w-lightbox-inactive:hover {
  opacity: 0;
}

.w-richtext:before, .w-richtext:after {
  content: " ";
  grid-area: 1 / 1 / 2 / 2;
  display: table;
}

.w-richtext:after {
  clear: both;
}

.w-richtext[contenteditable="true"]:before, .w-richtext[contenteditable="true"]:after {
  white-space: initial;
}

.w-richtext ol, .w-richtext ul {
  overflow: hidden;
}

.w-richtext .w-richtext-figure-selected.w-richtext-figure-type-video div:after, .w-richtext .w-richtext-figure-selected[data-rt-type="video"] div:after, .w-richtext .w-richtext-figure-selected.w-richtext-figure-type-image div, .w-richtext .w-richtext-figure-selected[data-rt-type="image"] div {
  outline: 2px solid #2895f7;
}

.w-richtext figure.w-richtext-figure-type-video > div:after, .w-richtext figure[data-rt-type="video"] > div:after {
  content: "";
  display: none;
  position: absolute;
  inset: 0;
}

.w-richtext figure {
  max-width: 60%;
  position: relative;
}

.w-richtext figure > div:before {
  cursor: default !important;
}

.w-richtext figure img {
  width: 100%;
}

.w-richtext figure figcaption.w-richtext-figcaption-placeholder {
  opacity: .6;
}

.w-richtext figure div {
  color: #0000;
  font-size: 0;
}

.w-richtext figure.w-richtext-figure-type-image, .w-richtext figure[data-rt-type="image"] {
  display: table;
}

.w-richtext figure.w-richtext-figure-type-image > div, .w-richtext figure[data-rt-type="image"] > div {
  display: inline-block;
}

.w-richtext figure.w-richtext-figure-type-image > figcaption, .w-richtext figure[data-rt-type="image"] > figcaption {
  caption-side: bottom;
  display: table-caption;
}

.w-richtext figure.w-richtext-figure-type-video, .w-richtext figure[data-rt-type="video"] {
  width: 60%;
  height: 0;
}

.w-richtext figure.w-richtext-figure-type-video iframe, .w-richtext figure[data-rt-type="video"] iframe {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
}

.w-richtext figure.w-richtext-figure-type-video > div, .w-richtext figure[data-rt-type="video"] > div {
  width: 100%;
}

.w-richtext figure.w-richtext-align-center {
  clear: both;
  margin-left: auto;
  margin-right: auto;
}

.w-richtext figure.w-richtext-align-center.w-richtext-figure-type-image > div, .w-richtext figure.w-richtext-align-center[data-rt-type="image"] > div {
  max-width: 100%;
}

.w-richtext figure.w-richtext-align-normal {
  clear: both;
}

.w-richtext figure.w-richtext-align-fullwidth {
  text-align: center;
  clear: both;
  width: 100%;
  max-width: 100%;
  margin-left: auto;
  margin-right: auto;
  display: block;
}

.w-richtext figure.w-richtext-align-fullwidth > div {
  padding-bottom: inherit;
  display: inline-block;
}

.w-richtext figure.w-richtext-align-fullwidth > figcaption {
  display: block;
}

.w-richtext figure.w-richtext-align-floatleft {
  float: left;
  clear: none;
  margin-right: 15px;
}

.w-richtext figure.w-richtext-align-floatright {
  float: right;
  clear: none;
  margin-left: 15px;
}

.w-nav {
  z-index: 1000;
  background: #ddd;
  position: relative;
}

.w-nav:before, .w-nav:after {
  content: " ";
  grid-area: 1 / 1 / 2 / 2;
  display: table;
}

.w-nav:after {
  clear: both;
}

.w-nav-brand {
  float: left;
  color: #333;
  text-decoration: none;
  position: relative;
}

.w-nav-link {
  vertical-align: top;
  color: #222;
  text-align: left;
  margin-left: auto;
  margin-right: auto;
  padding: 20px;
  text-decoration: none;
  display: inline-block;
  position: relative;
}

.w-nav-link.w--current {
  color: #0082f3;
}

.w-nav-menu {
  float: right;
  position: relative;
}

[data-nav-menu-open] {
  text-align: center;
  background: #c8c8c8;
  min-width: 200px;
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  overflow: visible;
  display: block !important;
}

.w--nav-link-open {
  display: block;
  position: relative;
}

.w-nav-overlay {
  width: 100%;
  display: none;
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  overflow: hidden;
}

.w-nav-overlay [data-nav-menu-open] {
  top: 0;
}

.w-nav[data-animation="over-left"] .w-nav-overlay {
  width: auto;
}

.w-nav[data-animation="over-left"] .w-nav-overlay, .w-nav[data-animation="over-left"] [data-nav-menu-open] {
  z-index: 1;
  top: 0;
  right: auto;
}

.w-nav[data-animation="over-right"] .w-nav-overlay {
  width: auto;
}

.w-nav[data-animation="over-right"] .w-nav-overlay, .w-nav[data-animation="over-right"] [data-nav-menu-open] {
  z-index: 1;
  top: 0;
  left: auto;
}

.w-nav-button {
  float: right;
  cursor: pointer;
  -webkit-tap-highlight-color: #0000;
  tap-highlight-color: #0000;
  -webkit-user-select: none;
  user-select: none;
  padding: 18px;
  font-size: 24px;
  display: none;
  position: relative;
}

.w-nav-button:focus {
  outline: 0;
}

.w-nav-button.w--open {
  color: #fff;
  background-color: #c8c8c8;
}

.w-nav[data-collapse="all"] .w-nav-menu {
  display: none;
}

.w-nav[data-collapse="all"] .w-nav-button, .w--nav-dropdown-open, .w--nav-dropdown-toggle-open {
  display: block;
}

.w--nav-dropdown-list-open {
  position: static;
}

@media screen and (max-width: 991px) {
  .w-nav[data-collapse="medium"] .w-nav-menu {
    display: none;
  }

  .w-nav[data-collapse="medium"] .w-nav-button {
    display: block;
  }
}

@media screen and (max-width: 767px) {
  .w-nav[data-collapse="small"] .w-nav-menu {
    display: none;
  }

  .w-nav[data-collapse="small"] .w-nav-button {
    display: block;
  }

  .w-nav-brand {
    padding-left: 10px;
  }
}

@media screen and (max-width: 479px) {
  .w-nav[data-collapse="tiny"] .w-nav-menu {
    display: none;
  }

  .w-nav[data-collapse="tiny"] .w-nav-button {
    display: block;
  }
}

.w-tabs {
  position: relative;
}

.w-tabs:before, .w-tabs:after {
  content: " ";
  grid-area: 1 / 1 / 2 / 2;
  display: table;
}

.w-tabs:after {
  clear: both;
}

.w-tab-menu {
  position: relative;
}

.w-tab-link {
  vertical-align: top;
  text-align: left;
  cursor: pointer;
  color: #222;
  background-color: #ddd;
  padding: 9px 30px;
  text-decoration: none;
  display: inline-block;
  position: relative;
}

.w-tab-link.w--current {
  background-color: #c8c8c8;
}

.w-tab-link:focus {
  outline: 0;
}

.w-tab-content {
  display: block;
  position: relative;
  overflow: hidden;
}

.w-tab-pane {
  display: none;
  position: relative;
}

.w--tab-active {
  display: block;
}

@media screen and (max-width: 479px) {
  .w-tab-link {
    display: block;
  }
}

.w-ix-emptyfix:after {
  content: "";
}

@keyframes spin {
  0% {
    transform: rotate(0);
  }

  100% {
    transform: rotate(360deg);
  }
}

.w-dyn-empty {
  background-color: #ddd;
  padding: 10px;
}

.w-dyn-hide, .w-dyn-bind-empty, .w-condition-invisible {
  display: none !important;
}

.wf-layout-layout {
  display: grid;
}

@font-face {
  font-family: Mango Grotesque Vf;
  src: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/6787c57c1a41c0d74fbdd703_Mango%20Grotesque-VF.ttf") format("truetype");
  font-weight: 100 900;
  font-style: normal;
  font-display: swap;
}

:root {
  --white: #f6f4f1;
  --grey: #66615a;
  --cream: #ede7df;
  --black: #252321;
  --brown: #bc884a;
  --transparent: #fff0;
  --light-cream: #ded9d0;
  --another-grey: #cec8bd;
  --lightest-cream: var(--white);
}

.w-pagination-wrapper {
  flex-wrap: wrap;
  justify-content: center;
  display: flex;
}

.w-pagination-previous {
  color: #333;
  background-color: #fafafa;
  border: 1px solid #ccc;
  border-radius: 2px;
  margin-left: 10px;
  margin-right: 10px;
  padding: 9px 20px;
  font-size: 14px;
  display: block;
}

.w-pagination-previous-icon {
  margin-right: 4px;
}

.w-pagination-next {
  color: #333;
  background-color: #fafafa;
  border: 1px solid #ccc;
  border-radius: 2px;
  margin-left: 10px;
  margin-right: 10px;
  padding: 9px 20px;
  font-size: 14px;
  display: block;
}

.w-pagination-next-icon {
  margin-left: 4px;
}

body {
  background-color: var(--white);
  color: var(--grey);
  font-family: Instrument Serif, sans-serif;
  font-size: 1vw;
  font-weight: 400;
  line-height: 1.2;
}

h1 {
  color: #252321;
  text-transform: uppercase;
  margin-top: 0;
  margin-bottom: 0;
  font-family: Mango Grotesque Vf, Arial, sans-serif;
  font-size: 6.45em;
  font-weight: 600;
  line-height: .6;
}

h2 {
  color: #252321;
  text-transform: uppercase;
  margin-top: 20px;
  margin-bottom: 10px;
  font-family: Mango Grotesque Vf, Arial, sans-serif;
  font-size: 4.5em;
  font-weight: 600;
  line-height: .9;
}

h3 {
  color: #252321;
  text-transform: uppercase;
  margin-top: 0;
  margin-bottom: 0;
  font-family: Mango Grotesque Vf, Arial, sans-serif;
  font-size: 3em;
  font-weight: 600;
  line-height: .9;
}

h4 {
  text-transform: uppercase;
  margin-top: 10px;
  margin-bottom: 10px;
  font-family: Oswald, sans-serif;
  font-size: 1.75em;
  font-weight: 500;
  line-height: 1.1;
}

.navigation {
  z-index: 1000;
  width: 100%;
  position: fixed;
  inset: auto 0% 0%;
  overflow: hidden;
}

.navigation.bg-white {
  z-index: 1000;
  flex-flow: column;
  width: 100%;
  display: flex;
  position: fixed;
  inset: auto 0% 0%;
}

.container-lg {
  flex-flow: column;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  display: flex;
  position: relative;
}

.container-lg.flex-sb {
  justify-content: space-between;
  align-items: center;
  padding-left: 3em;
  padding-right: 3em;
  display: flex;
}

.container-lg.div-hide.bg-cream {
  background-color: var(--cream);
}

.container-lg.flex-v {
  grid-column-gap: 3em;
  grid-row-gap: 3em;
  flex-flow: column;
  justify-content: flex-start;
  align-items: stretch;
  display: flex;
}

.container-lg.flex-v.is-articles {
  grid-column-gap: 0em;
  grid-row-gap: 0em;
  border-top: 2px dashed var(--black);
  border-bottom: 2px none var(--black);
  background-color: var(--white);
  padding-top: 10em;
  padding-bottom: 0;
}

.container-lg.flex-v.is-articles.is-all {
  border-top-style: none;
  padding-top: 0;
  padding-bottom: 0;
}

.container-lg.padded {
  padding-left: 4.5em;
  padding-right: 4.5em;
}

.container-lg.spaced {
  flex-flow: column;
  display: flex;
}

.container-lg.is-nav {
  border-top: 2px solid var(--black);
  background-color: var(--white);
  flex-flow: row;
  justify-content: space-between;
  padding: .75em 3em;
}

.container-lg.h-padded {
  padding-left: 3em;
  padding-right: 3em;
}

.container-lg.is-about-heading {
  align-items: stretch;
  padding-bottom: 10em;
}

.container-lg.is-padded {
  padding-left: 3em;
  padding-right: 3em;
}

.container-lg.is-load {
  height: auto;
  padding-left: 3em;
  padding-right: 3em;
}

.container-lg.is-middle {
  z-index: 3;
  margin-top: 6em;
  padding-bottom: 5em;
}

.container-lg.is-mobile {
  padding-left: 2.5em;
  padding-right: 2.5em;
}

.container-lg.interviews {
  border-bottom: 2px dashed var(--black);
  background-color: var(--white);
  padding-bottom: 0;
}

.container-lg.is-interview-heading {
  background-color: var(--cream);
  justify-content: center;
  align-items: flex-start;
  height: auto;
  padding: 4em 3em 3em;
}

.container-lg.ov-hidden {
  overflow: hidden;
}

.nav-menu {
  grid-column-gap: 2em;
  grid-row-gap: 2em;
  justify-content: center;
  align-items: center;
  padding-left: .2em;
  padding-right: .25em;
  display: flex;
}

.nav-link {
  grid-column-gap: .25em;
  grid-row-gap: .25em;
  color: var(--black);
  text-transform: uppercase;
  cursor: pointer;
  justify-content: flex-start;
  align-items: center;
  padding: .25em .125em .125em;
  text-decoration: none;
  display: flex;
  position: relative;
  overflow: hidden;
}

.nav-link.first-link {
  grid-column-gap: .5em;
  grid-row-gap: .5em;
  border-right-style: none;
  justify-content: flex-start;
  align-items: flex-start;
}

.nav-link.hidden {
  display: none;
}

.nav-text {
  color: var(--black);
  letter-spacing: .4px;
  text-transform: uppercase;
  font-family: Instrument Serif, sans-serif;
  font-size: 1.125em;
  font-style: italic;
  font-weight: 400;
  line-height: .7;
  position: relative;
}

.nav-text.pos-ab {
  position: absolute;
  bottom: -1.5em;
}

.nav-text.pg-top {
  font-size: 1.125em;
}

.nav-text.pg-top.text-white {
  margin-bottom: 0;
}

.nav-text.end-of-site {
  z-index: 12;
  position: absolute;
  inset: auto 3em 4em auto;
}

.nav-text.end-of-site.left {
  left: 3em;
  right: auto;
}

.nav-text.is-tip {
  color: var(--white);
  font-size: 1em;
}

.div-block {
  background-color: #fee;
  border: 1px solid #000;
  padding: 3em 4.5em;
}

.body {
  background-color: #0000;
  width: 100%;
  font-size: 1vw;
}

.heading {
  line-height: 1em;
}

.heading-lg.max-70 {
  max-width: 70%;
}

.heading-lg.max-80 {
  max-width: 80%;
}

.heading-lg.clipped {
  overflow: hidden;
}

.heading-lg.flex-text-v {
  flex-flow: column;
  display: flex;
}

.heading-md {
  margin-top: 0;
  margin-bottom: 0;
}

.heading-md.max-70 {
  max-width: 70%;
}

.heading-md.max-60 {
  max-width: 60%;
}

.heading-md.text-white {
  color: var(--white);
}

.heading-md.max-50 {
  max-width: 50%;
}

.heading-3, .heading-4 {
  line-height: 1;
}

.heading-sm {
  font-weight: 600;
  line-height: 1.1;
}

.heading-sm.max-80 {
  width: 80%;
}

.body-2 {
  font-size: 1vw;
  line-height: 1;
}

.logo-container {
  justify-content: center;
  align-items: center;
  text-decoration: none;
  display: flex;
}

.logo-container.w--current {
  padding-top: .4em;
}

.bg-black {
  background-color: #252321;
}

.nav-hover-box {
  background-color: var(--black);
  width: 100%;
  height: 100%;
  position: absolute;
  inset: auto 0% 0%;
}

.hero-section {
  height: auto;
  margin-top: 0;
  padding: 10em 4.5em;
}

.hero-section.bg-cream {
  background-color: var(--cream);
}

.hero-section.bg-cream.relative {
  position: relative;
}

.hero-section.events {
  flex-flow: column;
  padding-top: 6em;
  padding-bottom: 0;
  display: flex;
}

.hero-section.article-page {
  padding-top: 9.5em;
  padding-right: 0;
}

.hero-section.book-page {
  padding-bottom: 6em;
}

.header-wrapper {
  grid-column-gap: 0px;
  grid-row-gap: 0px;
  justify-content: flex-start;
  align-items: flex-start;
  display: flex;
}

.header-wrapper.spaced {
  display: flex;
}

.heading-5 {
  font-weight: 500;
  line-height: 1.2;
}

.marquee-section.about {
  margin-bottom: 6em;
  display: flex;
}

.marquee-img-container {
  border: 1px solid #000;
  justify-content: center;
  align-items: center;
  width: 25%;
  height: 32em;
  padding: .75em;
  display: flex;
}

.marquee-img {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/6787c53671a97b8708dd7011_Mask-group-min.webp");
  background-position: 50%;
  background-repeat: no-repeat;
  background-size: 336px;
  width: 100%;
  height: 100%;
}

.marquee-img.mq-img1 {
  background-size: cover;
}

.marquee-img.mq-img-2 {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/6787c53671a97b8708dd704f_DJ%20-%201-min.jpg");
  background-size: cover;
}

.marquee-img.mq-img-3 {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/6787c53671a97b8708dd7010_Mask-group-2-min.webp");
  background-size: cover;
}

.marquee-img.mq-img-4 {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/6787c53671a97b8708dd7013_Mask-group-3-min.webp");
  background-size: cover;
}

.flex-h {
  display: flex;
}

.flex-h._150-percent {
  min-width: 150%;
}

.flex-h.center {
  justify-content: center;
  align-items: center;
}

.flex-h.center.article-link {
  grid-column-gap: .5em;
  grid-row-gap: .5em;
  height: auto;
  display: none;
}

.h-anim-wrap, .h-anim-wrap-2, .h-anim-wrap-3, .h-anim-wrap-4, .h-anim-wrap-5 {
  width: 100%;
  position: relative;
}

.img-marquee-container {
  flex-flow: row;
  align-items: center;
  width: 100%;
  display: flex;
  overflow: hidden;
}

.img-marquee-container.bg-cream {
  background-color: var(--cream);
}

.marquee-mov {
  width: 252em;
  padding-top: 1.5em;
  padding-bottom: 1.5em;
  display: flex;
}

.marquee-mov.is-gal-marq {
  width: 448em;
}

.marquee-mov.is-white {
  background-color: var(--black);
  position: absolute;
}

.marquee-mov.is-home-int {
  width: 132em;
  padding-top: .25em;
  padding-bottom: .25em;
}

.section {
  grid-column-gap: 4em;
  grid-row-gap: 4em;
  background-color: var(--white);
  color: var(--black);
  flex-flow: column;
  justify-content: center;
  align-items: center;
  width: 100%;
  padding: 12.5em 3em;
  display: flex;
  position: relative;
}

.section.events {
  padding-top: 6em;
  padding-bottom: 6em;
}

.section.left-nopad {
  padding-top: 6em;
  padding-bottom: 0;
  padding-left: 0;
}

.section.mini {
  padding-bottom: 6em;
}

.section.top-lesspad {
  padding-top: 8em;
}

.section.top-nopad {
  padding-top: 0;
}

.section.interviews {
  grid-column-gap: 0em;
  grid-row-gap: 0em;
  background-color: #f6f4f100;
  padding: 0;
}

.section.no-h-pad {
  grid-column-gap: 6em;
  grid-row-gap: 6em;
  padding-left: 0;
  padding-right: 0;
}

.section.no-h-pad.bg-cream {
  border-top: 2px dashed var(--black);
}

.section.is-hero {
  padding-top: 8em;
}

.section.is-hero.is-books {
  padding-bottom: 2em;
}

.section.is-hero.is-about-hero {
  z-index: 1;
  padding: 6em 0 0;
}

.section.no-btm-pad {
  grid-column-gap: 6em;
  grid-row-gap: 6em;
  padding-bottom: 0;
}

.section.no-btm-pad.is-rel-high {
  z-index: 3;
}

.section.is-articles {
  z-index: 3;
  grid-column-gap: 0em;
  grid-row-gap: 0em;
  border-top: 2px none var(--black);
  background-color: #f6f4f100;
  padding: 0;
}

.section.no-pad {
  padding-left: 0;
  padding-right: 0;
}

.section.no-pad.big-btm {
  padding-bottom: 24em;
}

.section.is-books {
  padding-bottom: 0;
}

.section.top-margin {
  background-color: var(--cream);
  padding-top: 18em;
}

.section.is-2 {
  background-color: #f6f4f100;
  background-image: linear-gradient(#f6f4f100, #fff);
  padding-left: 0;
  padding-right: 0;
}

.section.is-2.is-home {
  background-image: linear-gradient(#f6f4f100, #fff 90%);
  padding-top: 0;
  padding-bottom: 0;
}

.section.is-footer {
  padding-bottom: 0;
}

.section.home-int-wrapper {
  justify-content: flex-start;
  align-items: center;
  height: 600vh;
  padding: 0;
}

.section.banner-wrapper {
  height: 75vh;
  padding: 0;
}

.section.is-h-articles {
  grid-column-gap: 0em;
  grid-row-gap: 0em;
  border-top: 2px dashed var(--black);
  padding-bottom: 0;
  padding-left: 0;
  padding-right: 0;
}

.section.is-h-books {
  padding: 12.5em 0 0;
}

.text-span, .text-span-2 {
  color: var(--brown);
}

.img-parent {
  perspective: 1000px;
  border-radius: 4px;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  text-decoration: none;
  display: flex;
  position: relative;
  overflow: hidden;
}

.img-parent.min-60 {
  min-width: 60%;
}

.img-parent.max-25 {
  width: 25%;
}

.img-parent.max-0 {
  width: 50%;
}

.img-parent.border.border-white {
  border-color: var(--white);
}

.img-parent.border.border-white.split-card {
  height: 100%;
}

.img-parent.border.small-gap {
  padding: .5em;
}

.img-parent.border.small-gap.h-scroll {
  height: 100%;
}

.img-parent.border.int-img {
  width: 50%;
}

.img-parent.hero {
  width: auto;
  height: 100%;
}

.img-parent.overflow-hidden {
  width: 100%;
  overflow: hidden;
}

.img-parent.overflow-hidden.max-90 {
  width: 70%;
}

.img-parent.stretched {
  width: 100%;
}

.img-parent.solo-img {
  height: 100%;
}

.img-parent.is-banner {
  background-image: none;
}

.img-child {
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  display: flex;
  position: relative;
}

.img-child.banner {
  height: 70vh;
}

.img-child.move {
  height: 130%;
}

.img-child.hero {
  width: auto;
  overflow: hidden;
}

.img-child.home {
  background-size: cover;
  height: 30em;
}

.img-child.img-event-banner {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/6787c53671a97b8708dd7015_Hero-min.webp");
  height: 32.5em;
}

.img-child.split-card {
  height: 100%;
}

.img-child.img-article-hero {
  background-image: none;
}

.img-child.books {
  background-image: url("https://d3e54v103j8qbb.cloudfront.net/img/background-image.svg");
  background-position: 50%;
  background-repeat: no-repeat;
  background-size: cover;
}

.img-child.contact {
  background-image: none;
}

.img-child.is-h-1 {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/6787c53671a97b8708dd7052_Dj-min.jpg");
}

.img-child.is-a-2 {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/678a2fcbdb81b26adbd281b8_DJ-awww-about-1-min.jpg");
}

.img-child.is-a-3 {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/678a2fcc3e8bd36901f7559d_DJ-awww-about-2-min.jpg");
}

.img-child.is-a-4 {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/678a2fcccc02a4661950a232_DJ-awww-about-min.jpg");
}

.img-child.is-a-5 {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/678a2fcc1191bd3ac449e916_DJ-awww-about-3-min.jpg");
}

.img-child.is-a-6 {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/682aa4d7e7c891e1e8f956ed_About%20Banner-min%20(1).jpg");
  background-position: 50%;
  background-repeat: no-repeat;
  background-size: cover;
}

.img-child.is-h-2 {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/6787c53671a97b8708dd702a_dj-home-2-min.webp");
}

.img-child.is-a-6-ab {
  z-index: 1;
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/682aa4d7d7d614e5dbeb021f_About%20Banner-1-min.jpg");
  background-position: 50%;
  background-repeat: no-repeat;
  background-size: cover;
  position: absolute;
  overflow: hidden;
}

.img-child.is-int-hov-1, .img-child.is-int-hov-2 {
  background-image: url("https://d3e54v103j8qbb.cloudfront.net/img/background-image.svg");
  background-position: 50%;
  background-repeat: no-repeat;
  background-size: cover;
}

.text-para {
  color: var(--grey);
  margin-bottom: 0;
  font-size: 1.25em;
  line-height: 1.6;
}

.text-para.max-50 {
  max-width: 50%;
}

.text-para.max-30 {
  max-width: 30%;
}

.text-para.max-30.margin-top {
  width: 25%;
  margin-top: 2em;
}

.text-para.text-white {
  color: var(--white);
}

.text-para.max-80 {
  max-width: 80%;
}

.text-para.article-date {
  z-index: 1;
  color: var(--brown);
  white-space: nowrap;
  font-size: 1.25em;
  font-weight: 400;
  line-height: 1;
  position: relative;
}

.text-para.max-80 {
  width: 80%;
  max-width: 80%;
}

.text-para.max-60 {
  width: 60%;
}

.text-para.max-40 {
  width: 40%;
}

.text-para.d-50 {
  width: 34%;
}

.flex-split {
  justify-content: space-between;
  display: flex;
}

.flex-split.is-highlight {
  align-items: flex-start;
  width: 100%;
}

.flex-split.is-highlight.is-last {
  padding-bottom: 0;
}

.flex-split.is-gallery {
  width: 100%;
  padding-top: 1.25em;
  padding-left: 2.5em;
  padding-right: 2.5em;
  position: absolute;
  inset: 0% 0% auto;
}

.flex-split.is-btm {
  justify-content: space-between;
  align-items: flex-end;
  padding-left: 3em;
  padding-right: 3em;
}

.div-left-padding {
  width: 40%;
  padding-left: 4.5em;
}

.image {
  width: 100%;
  height: 440px;
}

.bg-cream {
  background-color: var(--cream);
}

.bg-white {
  background-color: var(--white);
}

.flex-left {
  grid-column-gap: 1.5em;
  grid-row-gap: 1.5em;
  width: 100%;
  display: flex;
}

.flex-left.is-l-pad-17 {
  padding-left: 17.2em;
}

.flex-left.is-pad {
  padding-left: 2.5em;
  padding-right: 2.5em;
}

.flex-left.is-sm-gap {
  grid-column-gap: 1em;
  grid-row-gap: 1em;
  justify-content: flex-start;
  align-items: center;
  width: auto;
}

.flex-left.is-pagination {
  grid-column-gap: .5em;
  grid-row-gap: .5em;
  justify-content: flex-start;
  align-items: center;
  font-size: 14px;
}

.flex-left.is-auto {
  grid-column-gap: .25em;
  grid-row-gap: .25em;
  width: auto;
}

.cell {
  grid-column-gap: 5em;
  grid-row-gap: 5em;
  padding-top: 3em;
  padding-left: 4em;
}

.quick-stack {
  padding: 0;
}

.about-img-2 {
  width: 100%;
  height: 37.5em;
}

.about-img-3 {
  width: 100%;
  height: 24em;
}

.text-span-3, .text-span-4 {
  color: var(--brown);
}

.about-img-4 {
  width: 100%;
  height: 20em;
}

.cell-2 {
  padding-left: 3.5em;
}

.about-stack-2, .about-stack-3 {
  padding: 0;
}

.cell-3 {
  margin-left: auto;
  margin-right: auto;
  padding-left: 14.25em;
  display: block;
}

.stack-wrapper {
  grid-column-gap: 5em;
  grid-row-gap: 5em;
  flex-flow: column;
  display: flex;
}

.text-span-5, .text-span-6 {
  color: var(--brown);
}

.border {
  border: 1px solid var(--black);
  padding: .75em;
}

.page {
  z-index: 2;
  width: 100%;
  position: relative;
}

.event-container {
  grid-column-gap: 1.5em;
  grid-row-gap: 1.5em;
  border-top: 2px dashed var(--black);
  flex-flow: row;
  justify-content: space-between;
  width: 100%;
  padding: 6em 3em;
  display: flex;
  position: relative;
  overflow: hidden;
}

.event-container.border-btm {
  border-bottom: 1px solid var(--brown);
}

.heading-container.bg-white.flex-split {
  justify-content: space-between;
  align-items: center;
  padding: 1.75em 2.5em;
}

.heading-container.bg-white.flex-split.border {
  overflow: hidden;
}

.description-container {
  padding-top: 3em;
  padding-bottom: 3em;
  padding-left: 2.5em;
}

.description-container.border-btm {
  border-bottom: 1px solid var(--brown);
}

.description-container.flex-h {
  grid-column-gap: 4em;
  grid-row-gap: 4em;
}

.description-container.flex-h.interview {
  padding-top: 0;
  padding-bottom: 3em;
}

.date-location-wrapper {
  display: flex;
}

.date-wrapper {
  border-right: 1px solid var(--black);
  padding-right: 2em;
  overflow: hidden;
}

.location-wrapper {
  padding-left: 2em;
  overflow: hidden;
}

.int-heading-wrapper {
  z-index: 1;
  cursor: default;
  width: 28em;
  position: relative;
}

.int-cont-wrapper {
  z-index: 1;
  cursor: default;
  flex-flow: column;
  width: 28em;
  display: flex;
  position: relative;
}

.event-img-wrapper {
  display: flex;
}

.event-img {
  width: 100%;
}

.collection-list {
  grid-column-gap: 1.5em;
  grid-row-gap: 1.5em;
  display: flex;
}

.collection-list-2 {
  grid-column-gap: 5em;
  grid-row-gap: 5em;
  flex-flow: column;
  display: flex;
}

.span-heading {
  color: var(--brown);
  display: inline-block;
}

.h1-wrapper {
  padding-top: 3em;
  padding-bottom: 4em;
  position: relative;
}

.divider {
  background-color: var(--black);
  height: 1px;
  display: none;
}

.divider.ab-btm {
  z-index: 1;
  position: absolute;
  inset: auto 0% 0%;
}

.footer {
  grid-column-gap: 4em;
  grid-row-gap: 4em;
  border-top: 2px dashed var(--black);
  background-color: var(--white);
  flex-flow: row;
  justify-content: space-between;
  align-items: flex-end;
  width: 100%;
  padding: 7.5em 3em;
  display: flex;
  position: relative;
  overflow: hidden;
}

.footer-menu {
  grid-column-gap: 0px;
  grid-row-gap: 0px;
  grid-template-rows: auto;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  grid-auto-columns: 1fr;
  width: 100%;
  padding-left: 2em;
  padding-right: 2em;
  display: flex;
}

.footer-link {
  border-left: 1px solid var(--black);
  justify-content: center;
  align-items: center;
  width: 100%;
  padding: .75em 2.5em .25em;
  text-decoration: none;
  display: flex;
  position: relative;
  overflow: hidden;
}

.footer-link.last {
  border-right: 1px solid var(--black);
}

.footer-link.last.hidden {
  display: none;
}

.footer-text {
  color: var(--black);
  text-align: center;
  letter-spacing: .4px;
  text-transform: uppercase;
  font-family: Mango Grotesque Vf, Arial, sans-serif;
  font-size: 2em;
  font-weight: 600;
  line-height: .6;
}

.footer-text.text-ab {
  color: var(--white);
  display: none;
  position: absolute;
}

.footer-text-wrap {
  flex-flow: column;
  justify-content: center;
  align-items: center;
  width: 50%;
  display: flex;
  position: relative;
  overflow: hidden;
}

.footer-hover-block {
  background-color: var(--black);
  width: 100%;
  height: 100%;
  position: absolute;
  inset: 0% auto 0% 0%;
}

.footer-marquee {
  width: 100%;
  position: relative;
  overflow: hidden;
}

.moving-wrapper {
  color: var(--white);
  white-space: nowrap;
  object-fit: fill;
  justify-content: flex-start;
  align-items: center;
  width: 324em;
  display: flex;
}

.moving-wrapper.inner {
  object-fit: contain;
}

.moving-wrapper.ab {
  position: absolute;
}

.moving-wrapper.logo {
  grid-column-gap: 4.8em;
  grid-row-gap: 4.8em;
  padding-top: 2em;
  padding-bottom: 2em;
}

.footer-super-text {
  color: var(--cream);
  letter-spacing: 1px;
  text-transform: uppercase;
  text-shadow: 1px 1px 0 var(--black), -1px 1px 0 var(--black), -1px -1px 0 var(--black), 1px -1px 0 var(--black);
  font-family: Mango Grotesque Vf, Arial, sans-serif;
  font-size: 12em;
  font-weight: 700;
  line-height: .6;
}

.footer-super-text-wrap {
  white-space: nowrap;
  justify-content: center;
  align-items: center;
  width: 72em;
  padding-top: 1.75em;
  padding-left: 4em;
  padding-right: 4em;
  display: flex;
}

.star-img {
  width: 9em;
  height: 9em;
}

.set {
  white-space: nowrap;
  justify-content: flex-start;
  align-items: center;
  width: auto;
  display: flex;
}

.hl-label-container {
  border-top: 1px solid var(--black);
  background-color: var(--white);
  justify-content: center;
  align-items: center;
  padding-top: .4em;
  padding-bottom: .4em;
  display: flex;
  position: absolute;
  inset: auto 0% 0%;
}

.text-block {
  color: var(--black);
  font-size: 1em;
  font-weight: 400;
  text-decoration: none;
}

.title-wrapper {
  grid-column-gap: 5em;
  grid-row-gap: 5em;
  flex-flow: column;
  display: flex;
}

.title-wrapper.small {
  grid-column-gap: 1.5em;
  grid-row-gap: 1.5em;
  width: 100%;
}

.text-span-8 {
  color: var(--brown);
}

.h-scroll-section {
  z-index: 5;
  width: 100%;
  padding-bottom: 6em;
  position: relative;
}

.h-scroll-section.highlights {
  background-color: var(--cream);
  padding-top: 10em;
  padding-bottom: 10em;
}

.article-hero-content-wrapper {
  border-top: 1px solid #000;
  justify-content: space-between;
  margin-top: 4em;
  display: flex;
}

.image-2 {
  max-width: 100%;
}

.img-article-main {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/6787c53671a97b8708dd704d_Home%20Banner%20Img-min.webp");
  background-position: 50%;
  background-repeat: no-repeat;
  background-size: cover;
  width: 56em;
  height: 30em;
}

.text-span-9 {
  color: var(--brown);
}

.div-block-2 {
  grid-column-gap: 1.5em;
  grid-row-gap: 1.5em;
  grid-template-rows: auto;
  grid-template-columns: 1fr 1fr;
  grid-auto-columns: 1fr;
  display: grid;
}

._2-col-heading-wrapper {
  padding: 3.6em 3em;
}

._2-col-heading-wrapper.bg-cream {
  position: relative;
}

.grid-row-1 {
  grid-column-gap: 16px;
  grid-row-gap: 16px;
  grid-template-rows: auto;
  grid-template-columns: 1fr 1fr;
  grid-auto-columns: 1fr;
  display: grid;
}

.grid-row-1.hidden {
  display: none;
}

.flex-v {
  grid-column-gap: 1.5em;
  grid-row-gap: 1.5em;
  flex-flow: column;
  width: 100%;
  display: flex;
}

.flex-v.border-nopad {
  grid-column-gap: 0em;
  grid-row-gap: 0em;
  border: 1px solid var(--black);
}

.flex-v.padded {
  padding: 4em 3em;
}

.flex-v.not-stretch {
  justify-content: flex-start;
  align-items: flex-start;
}

.flex-v.interview-content {
  justify-content: flex-start;
  align-items: flex-start;
  width: 50%;
}

.flex-v.is-load-gap {
  grid-column-gap: 1em;
  grid-row-gap: 1em;
}

._2-col-content-block {
  grid-column-gap: 2em;
  grid-row-gap: 2em;
  flex-flow: column;
  justify-content: flex-start;
  height: 100%;
  padding: 2.5em 3em;
  display: flex;
}

.btn-inner-frame {
  grid-column-gap: .5em;
  grid-row-gap: .5em;
  text-transform: uppercase;
  border-radius: 200px;
  justify-content: center;
  align-items: center;
  padding: .5em 1em .5em .6em;
  font-family: Oswald, sans-serif;
  text-decoration: none;
  display: flex;
  position: relative;
  overflow: hidden;
}

.btn-inner-frame.form {
  padding-top: .8em;
  padding-bottom: .8em;
  font-size: 1.4em;
}

.btn-inner-frame.bg-cream {
  background-color: var(--cream);
}

.btn-inner-frame.small, .btn-inner-frame.small.mobile {
  display: none;
}

.btn-inner-frame.secondary {
  border: 2px solid var(--black);
  background-color: var(--cream);
}

.btn-inner-frame.is-small {
  padding: .6em 1.25em .6em .6em;
}

.btn-inner-frame.is-keep-scroll {
  border: 1px solid var(--black);
  background-color: var(--white);
  padding-top: .75em;
  padding-bottom: .75em;
  padding-right: 1em;
}

.btn-inner-frame.is-pag {
  justify-content: center;
  align-items: center;
  width: 100%;
  padding-bottom: .5em;
  padding-right: .6em;
}

.btn-inner-frame.is-filters {
  padding-top: .6em;
  padding-bottom: .6em;
}

.btn-inner-frame.is-big {
  padding: 1em 1.2em;
}

.text-block-2 {
  text-transform: uppercase;
  font-family: Oswald, sans-serif;
  font-size: 1.5em;
  font-weight: 500;
}

.btn-text {
  color: var(--black);
  letter-spacing: .4px;
  text-transform: uppercase;
  white-space: nowrap;
  font-family: Instrument Serif, sans-serif;
  font-size: 1.125em;
  font-style: italic;
  line-height: .8;
}

.btn-text.text-white {
  color: var(--white);
  margin-bottom: 0;
}

.btn-text.ab {
  position: absolute;
}

.btn-text.text-black {
  color: var(--black);
}

.btn-text.is-gallery {
  font-size: 1em;
}

.btn-text.is-small {
  font-size: 1em;
  line-height: .7;
}

.btn-text.is-large {
  font-size: 1.5em;
}

.btn-text.is-largest {
  font-size: 1.75em;
}

.btn-text-wrapper {
  grid-column-gap: .5em;
  grid-row-gap: .5em;
  flex-flow: column;
  justify-content: center;
  align-items: center;
  display: flex;
  position: relative;
}

.grid-wrapper {
  grid-column-gap: 1.5em;
  grid-row-gap: 1.5em;
  flex-flow: column;
  display: flex;
}

.btm-borderline {
  background-color: var(--black);
  height: 1px;
  position: absolute;
  inset: auto 0% 0%;
}

.section-no-hpad {
  padding-top: 10em;
  padding-bottom: 10em;
}

.section-no-hpad.about {
  padding-bottom: 4.5em;
}

.arrow-img {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/678a500652edd795dea58d3e_DJ-Marq%20Arrow.svg");
  background-position: 50%;
  background-repeat: no-repeat;
  background-size: cover;
  width: 100%;
  height: 100%;
}

.arrow-img.is-btn {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/678cda4cd7dac0a3e027676e_white%20btn%20arrow.svg");
  background-size: contain;
}

.marq-inner {
  white-space: nowrap;
  width: 100%;
  display: flex;
  position: relative;
  overflow: hidden;
}

.marq-inner.bg-black {
  z-index: 1;
}

.marq-inner.border-nopad {
  border-top: 2px dashed var(--black);
  border-bottom: 2px dashed var(--black);
  background-color: var(--white);
  display: none;
}

.marq-inner.ab {
  position: absolute;
  inset: 0% auto auto 0%;
}

.marq-inner.ab.bg-cream {
  z-index: 2;
  inset: 0% auto auto 0%;
}

.arrow {
  width: 2em;
  height: 2em;
}

.marquee-text-wrapper {
  padding-top: .3em;
}

.marquee-text {
  color: var(--cream);
  text-transform: uppercase;
  font-family: Oswald, sans-serif;
  font-size: 2.5em;
  font-weight: 500;
}

.marquee-text.text-black {
  color: var(--black);
}

.marquee-text.is-italic {
  color: var(--black);
  font-family: Instrument Serif, sans-serif;
  font-size: 3em;
  font-style: italic;
  font-weight: 400;
  line-height: .7;
}

.marquee-text.is-italic.is-white {
  color: var(--white);
}

.arrow-black {
  width: 2em;
  height: 2em;
}

.marquee-wrapper {
  border-top: 2px dashed var(--black);
  border-bottom: 2px dashed var(--black);
  color: var(--black);
  width: 100%;
  padding-top: .5em;
  padding-bottom: .5em;
  text-decoration: none;
  position: relative;
  overflow: hidden;
}

.marquee-wrapper.is-load {
  border-top-style: none;
  border-bottom-style: none;
  padding-top: 0;
  padding-bottom: 0;
}

.marquee-wrapper.is-about {
  margin-top: 1em;
  margin-bottom: 1em;
}

._2-col-hero {
  grid-column-gap: 0px;
  grid-row-gap: 0px;
  grid-template-rows: auto;
  grid-template-columns: 1fr 1fr;
  grid-auto-columns: 1fr;
  display: grid;
}

.form-column {
  grid-column-gap: 3em;
  grid-row-gap: 3em;
  background-color: var(--cream);
  flex-flow: column;
  padding: 5em 4.5em;
  display: flex;
}

.form-column.h-scroll {
  grid-column-gap: 1.5em;
  grid-row-gap: 1.5em;
  justify-content: space-between;
  align-items: stretch;
  width: 32vw;
  height: 100%;
  padding: 3em 2.5em 2.5em;
}

.form-column.h-scroll.bg-white {
  background-color: var(--white);
}

.form-column.h-scroll.bg-white.border-no-pad {
  border: 1px solid var(--black);
  flex: none;
}

.form-column.contact-page {
  padding-right: 7.5em;
}

.form-column.space-btw {
  justify-content: space-between;
}

.form-column.padded {
  padding: 4em 3em;
}

.form-block {
  grid-column-gap: 1em;
  grid-row-gap: 1em;
  border: 1px solid var(--black);
  background-color: var(--white);
  flex-flow: column;
  margin-bottom: 0;
  padding: 1.5em;
  display: flex;
}

.form-block.hscroll {
  background-color: #fffaf5;
  border-style: none;
  justify-content: space-between;
  width: 100%;
  padding: 0;
}

.form-block.no-fill {
  background-color: #fffaf500;
}

.text-field {
  color: var(--black);
  text-transform: uppercase;
  background-color: #f8ede000;
  border: 1px solid #000;
  padding: 1.4em .8em;
  font-family: Oswald, sans-serif;
  font-size: 1.2em;
}

.text-field.hscroll {
  padding-top: 1.2em;
  padding-bottom: 1.2em;
}

.textarea {
  text-transform: uppercase;
  background-color: #fff0;
  border: 1px solid #000;
  min-height: 9em;
  margin-bottom: 1.5em;
  padding: 1em .8em 1.4em;
  font-family: Oswald, sans-serif;
  font-size: 1.2em;
}

.textarea.hscroll {
  margin-bottom: 1em;
  padding-bottom: 1em;
}

.contact-form {
  flex-flow: column;
  display: flex;
}

.contact-img {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/6787c53671a97b8708dd701c_Contact%20Img-min.jpg");
  background-position: 50%;
  background-repeat: no-repeat;
  background-size: cover;
  height: 100%;
}

.div-block-3 {
  width: 100%;
}

.div-hide {
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  display: flex;
  position: relative;
  overflow: hidden;
}

.div-hide.flex-top {
  justify-content: center;
  align-items: flex-start;
}

.text-span-10 {
  color: var(--brown);
}

.img-holder {
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 25em;
  display: flex;
  position: relative;
}

.footer-text-ab {
  color: var(--white);
  letter-spacing: .4px;
  text-transform: uppercase;
  font-family: Mango Grotesque Vf, Arial, sans-serif;
  font-size: 2em;
  font-weight: 400;
  line-height: .6;
  position: absolute;
}

.book-hscroll-container {
  display: flex;
}

.book-hscroll-wrapper {
  grid-column-gap: 4em;
  grid-row-gap: 4em;
  align-items: center;
  width: auto;
  display: flex;
}

.hscroll-height {
  width: 100%;
  height: 200vw;
  position: relative;
}

.hscroll-height.about {
  height: 160vw;
}

.sticky-element {
  flex-flow: column;
  justify-content: center;
  align-items: stretch;
  width: 100%;
  height: 100vh;
  padding: 6em 3em;
  display: flex;
  position: sticky;
  top: 0;
  overflow: hidden;
}

.sticky-element.flex-v.highlights {
  grid-column-gap: 2em;
  grid-row-gap: 2em;
  background-color: var(--cream);
  justify-content: center;
  height: 100vh;
  padding: 6em 3em;
}

.sticky-element.flex-v.books {
  grid-column-gap: 3em;
  grid-row-gap: 3em;
  background-color: #f6f4f100;
  padding-top: 3em;
  padding-bottom: 3em;
}

.book-card {
  flex-flow: row;
  justify-content: space-between;
  width: 42em;
  display: flex;
}

.book-card.home {
  grid-column-gap: 1.5em;
  grid-row-gap: 1.5em;
  flex-flow: column;
  align-items: flex-start;
  width: 100%;
  height: 100%;
  text-decoration: none;
}

.book-title {
  color: var(--black);
  text-transform: uppercase;
  font-family: Mango Grotesque Vf, Arial, sans-serif;
  font-size: 2.75em;
  font-weight: 600;
  line-height: .9;
}

.collection-item {
  height: 100%;
}

.collection-list-wrapper {
  grid-column-gap: 1.5em;
  grid-row-gap: 1.5em;
}

.top-wrapper {
  grid-column-gap: 1.5em;
  grid-row-gap: 1.5em;
  flex-flow: column;
  height: 100%;
  display: flex;
}

.h-track {
  grid-column-gap: 1.5em;
  grid-row-gap: 1.5em;
  flex: none;
  display: flex;
}

.h-track.highlights {
  flex: 0 auto;
  align-items: flex-start;
  height: auto;
  margin-bottom: 1em;
  padding-left: 0;
  padding-right: 0;
}

.track-flex {
  grid-column-gap: 1.5em;
  grid-row-gap: 1.5em;
  height: 100%;
  margin-right: -100vw;
  padding-top: 2em;
  padding-bottom: 2em;
  display: flex;
}

.track-flex.highlights-card {
  grid-column-gap: 1em;
  grid-row-gap: 1em;
  border-style: none;
  flex-flow: row;
  align-items: stretch;
  width: 175.5em;
  height: auto;
  margin-top: 1em;
  padding-top: 0;
  padding-bottom: 0;
}

.image-3 {
  object-fit: fill;
  height: 100%;
}

.btn-text-ab {
  z-index: 2;
  color: var(--cream);
  letter-spacing: .4px;
  font-size: 1.25em;
  font-weight: 400;
  position: absolute;
}

.btn-text-ab.text-black {
  color: var(--black);
}

.btn-bg-hover {
  width: 100%;
  height: 100%;
  position: absolute;
  inset: auto 0% 0%;
}

.btn-bg-hover.bg-brown {
  z-index: 1;
  background-color: var(--brown);
  inset: auto 0% 0%;
}

.div-block-4 {
  height: 100%;
}

.btm-wrapper {
  grid-column-gap: 1.5em;
  grid-row-gap: 1.5em;
  flex-flow: column;
  height: 100%;
  display: flex;
}

.book-scroll-slide-wrapper {
  display: flex;
}

.book-wrap {
  width: 100%;
}

.book-img-1 {
  height: 100%;
}

.seagal-img {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/6787c53671a97b8708dd7058_Video%20Img2-min.jpg");
  background-position: 50%;
  background-repeat: no-repeat;
  background-size: cover;
  width: 100%;
  height: 100%;
}

.logo-text {
  color: var(--black);
  letter-spacing: .2px;
  text-transform: uppercase;
  font-family: Instrument Serif, sans-serif;
  font-size: 1.25em;
  font-style: italic;
  font-weight: 500;
  line-height: .6;
}

.nav-text-ab {
  z-index: 1;
  color: var(--white);
  font-family: Mango Grotesque Vf, Arial, sans-serif;
  font-size: 1.5em;
  font-weight: 500;
  line-height: .6;
  position: absolute;
}

.home-hero-container {
  background-color: var(--cream);
  justify-content: space-between;
  height: 120vh;
  display: flex;
}

.home-hero-column {
  width: 50%;
  height: 100%;
  position: relative;
}

.home-hero-img {
  height: 100%;
}

.hero-content {
  z-index: 1;
  grid-column-gap: 2.5em;
  grid-row-gap: 2.5em;
  flex-flow: column;
  width: 100%;
  height: 100%;
  padding-top: 6.5em;
  padding-left: 3em;
  padding-right: 3em;
  display: flex;
  position: relative;
}

.action {
  grid-column-gap: .5em;
  grid-row-gap: .5em;
  justify-content: flex-start;
  align-items: center;
  height: 4.4em;
  padding-top: .2em;
  padding-bottom: .2em;
  display: flex;
  overflow: hidden;
}

.action.is-padded {
  padding-left: 5em;
  padding-right: 5em;
}

.action.is-home {
  z-index: 4;
  height: auto;
  margin-bottom: -1.5em;
}

.text-span-11, .text-span-12, .text-span-13 {
  color: var(--brown);
}

.home-about-wrapper {
  grid-column-gap: 1.5em;
  grid-row-gap: 1.5em;
  flex-flow: column;
  align-items: flex-start;
  width: 60%;
  padding-left: 4.5em;
  padding-right: 4.5em;
  display: flex;
}

.img-drop {
  width: 100%;
  position: relative;
}

.img-appear-parent {
  z-index: 20;
  justify-content: center;
  align-items: center;
  width: 16em;
  height: 24em;
  display: flex;
  position: absolute;
  overflow: hidden;
}

.img-appear-child {
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  display: flex;
  overflow: hidden;
}

.img-appear {
  min-width: 115%;
  height: 115%;
}

.logo-img-1, .logo-img-2, .logo-img-3 {
  height: 48px;
}

.logo-img-4 {
  height: 40px;
}

.logo-img-5 {
  height: 44px;
}

.logo-img-6 {
  height: 46px;
}

.logo-img-7 {
  height: 48px;
}

.logo-img-8 {
  height: 42px;
}

.book-card-wrapper {
  grid-column-gap: 1em;
  grid-row-gap: 1em;
  width: 100%;
  margin-bottom: 8em;
  display: flex;
}

.book-col-home {
  grid-column-gap: 1.5em;
  grid-row-gap: 1.5em;
  align-items: stretch;
  width: 100%;
  height: 100%;
  display: flex;
}

.collection-item-2 {
  width: 100%;
  height: 100%;
}

.book-collection-wrapper {
  width: 100%;
}

.space-btw {
  justify-content: space-between;
  align-items: center;
  display: flex;
}

.break {
  background-color: var(--brown);
  width: 100%;
  height: 80px;
}

.text-span-14 {
  color: var(--brown);
}

.highlights-card {
  grid-column-gap: .5em;
  grid-row-gap: .5em;
  flex-flow: column;
  width: 28em;
  display: flex;
}

.highlight-card-title {
  background-color: var(--white);
  border: 1px solid #000;
  border-radius: 4px;
  padding: 1em 1.5em .75em;
}

.date-text {
  color: var(--black);
  letter-spacing: .2px;
  text-transform: uppercase;
  font-family: Mango Grotesque Vf, Arial, sans-serif;
  font-size: 2em;
  font-weight: 600;
  line-height: .8;
}

.hlight-card-body {
  border: 2px dashed var(--black);
  border-radius: 4px;
  height: 100%;
  padding: 1.5em;
}

.text-block-4 {
  color: var(--black);
  letter-spacing: .4px;
  text-transform: uppercase;
  font-family: Oswald, sans-serif;
  font-size: 1.4em;
  line-height: 1.2;
}

.heading-6 {
  letter-spacing: -1px;
}

.image-4 {
  width: 100%;
}

.text-span-15, .text-span-16 {
  color: var(--brown);
}

.hero-span-1 {
  display: flex;
  overflow: hidden;
}

.hero-span {
  flex-flow: row;
  display: flex;
  overflow: hidden;
}

.hero-span._4, .hero-span._5 {
  color: var(--brown);
}

.heading-wrap {
  justify-content: center;
  align-items: center;
  position: relative;
  overflow: hidden;
}

.heading-cover {
  width: 100%;
  height: 100%;
  position: absolute;
  inset: 0% 0% auto;
}

.heading-cover.bg-white {
  inset: 0% 0% auto;
}

.body-3 {
  font-family: Inter, sans-serif;
  font-size: 1vw;
  line-height: 1;
}

.card-whipe-btm {
  z-index: 1;
  background-color: var(--white);
  width: 100%;
  height: 100%;
  position: absolute;
  inset: auto 0% 0%;
}

.card-whipe-btm.bg-cream {
  background-color: var(--cream);
}

.card-whipe-btm.bg-cream.ab-low {
  z-index: -1;
}

.card-whipe-top {
  z-index: 1;
  background-color: var(--white);
  width: 100%;
  height: 100%;
  position: absolute;
  inset: 0% 0% auto;
}

.intro-parent {
  z-index: 10;
  background-color: var(--cream);
  flex-flow: column;
  justify-content: space-between;
  height: 100vh;
  padding-bottom: 2em;
  display: none;
  position: fixed;
  inset: 0% 0% auto;
}

.btm-header-wrapper {
  flex-flow: column;
  display: flex;
}

.h1-supertext {
  font-size: 11em;
  line-height: 1;
}

.h1-supertext.large {
  font-size: 14em;
  line-height: .9;
}

.supertext-wrap {
  padding-left: 4.5em;
  padding-right: 4.5em;
}

.supertext-wrap.two {
  justify-content: center;
  align-items: center;
}

.supertext-wrap.right-all {
  justify-content: flex-end;
  align-items: flex-start;
}

.horrizontal-line {
  background-color: var(--black);
  width: 100%;
  height: 2px;
}

.article-wrapper {
  grid-column-gap: 2.5em;
  grid-row-gap: 2.5em;
  background-color: var(--white);
  border-bottom: 2px dashed #000;
  justify-content: flex-start;
  align-items: center;
  padding: 2em 3em;
  text-decoration: none;
  display: flex;
  position: relative;
}

.article-column-1 {
  justify-content: space-between;
  align-items: center;
  width: 100%;
  height: auto;
  display: flex;
}

.read-article {
  color: var(--black);
  letter-spacing: .2px;
  text-transform: uppercase;
  font-family: Oswald, sans-serif;
  font-size: 1.25em;
}

.div-block-5 {
  display: flex;
}

.image-5 {
  width: 6px;
  height: 12px;
}

.pagination {
  flex-flow: row;
  justify-content: space-between;
  margin-top: 2em;
  padding-left: 3em;
  padding-right: 3em;
}

.dropdown-wrapper {
  display: flex;
}

.article-heading {
  z-index: 2;
  color: var(--black);
  width: 100%;
  margin-top: 0;
  margin-bottom: 0;
  font-family: Mango Grotesque Vf, Arial, sans-serif;
  font-size: 2.85em;
  font-weight: 500;
  position: relative;
}

.article-heading.is-ab {
  color: var(--black);
  font-weight: 400;
  position: absolute;
  top: auto;
  bottom: 0%;
  right: auto;
}

.burger-menu {
  grid-column-gap: .4em;
  grid-row-gap: .4em;
  flex-flow: column;
  justify-content: center;
  align-items: center;
  height: 2.4em;
  display: none;
}

.burger-line-1, .burger-line-2, .border-line-3 {
  background-color: var(--black);
  width: 20px;
  height: 2px;
}

.top-section-wrapper {
  grid-column-gap: 2em;
  grid-row-gap: 2em;
  flex-flow: column;
  display: flex;
}

.article-list-wrapper-home {
  border-top: 2px dashed var(--black);
  width: 100%;
  margin-top: 4em;
}

.card-whipe-right {
  background-color: var(--white);
  width: 100%;
  position: absolute;
  inset: 0% 0% 0% auto;
}

.img-fade {
  justify-content: center;
  align-items: center;
  width: 100%;
  display: flex;
  position: relative;
}

.img-banner {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/6787c53671a97b8708dd704d_Home%20Banner%20Img-min.webp");
  background-position: 50%;
  background-repeat: no-repeat;
  background-size: cover;
  width: 100%;
  height: 35em;
}

.img-banner.banner-about {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/6787c53671a97b8708dd7054_dj-ban-about-min.jpg");
}

.mobile-menu {
  z-index: 900;
  background-color: var(--white);
  flex-flow: column;
  width: 100%;
  padding: 4em 4.5em 2em;
  display: none;
}

.mobile-nav-link {
  justify-content: flex-end;
  width: 100%;
  padding-top: 3em;
  padding-bottom: 3em;
  text-decoration: none;
  display: flex;
}

.mobile-nav-link.hidden {
  display: none;
}

.text-block-5 {
  color: var(--black);
  text-transform: uppercase;
  font-family: Oswald, sans-serif;
  font-size: 3em;
  font-weight: 500;
}

.main-container {
  z-index: 2;
  flex-flow: column;
  width: 100%;
  display: flex;
  position: relative;
}

.column-v-split {
  flex-flow: column;
  justify-content: space-around;
  display: flex;
}

.column-v-split.padded {
  padding: 4em 3em;
}

.column-v-split.padded.bg-black {
  grid-column-gap: 3em;
  grid-row-gap: 3em;
  padding-bottom: 2.5em;
}

.about-img {
  width: 100%;
  height: 22.5em;
}

.about-img.ai-6 {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/6787c53671a97b8708dd7050_DJ%20-%203-min.jpg");
  background-position: 50%;
  background-repeat: no-repeat;
  background-size: cover;
  height: 20em;
}

.about-img.ai-5 {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/6787c53671a97b8708dd704e_DJ%20-2-min.jpg");
  background-position: 50%;
  background-repeat: no-repeat;
  background-size: cover;
  height: 26em;
}

.about-img.ai-4 {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/6787c53671a97b8708dd7051_Dj-1-min.jpg");
  background-position: 50%;
  background-repeat: no-repeat;
  background-size: cover;
  height: 25em;
}

.about-img.ai-3 {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/6787c53671a97b8708dd7052_Dj-min.jpg");
  background-position: 0 0;
  background-repeat: no-repeat;
  background-size: cover;
  height: 37.5em;
}

.about-img.ai-2 {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/6787c53671a97b8708dd7014_Column.png");
  background-position: 50%;
  background-repeat: no-repeat;
  background-size: cover;
  height: 27.5em;
}

.page-count {
  color: var(--brown);
  width: auto;
  font-size: 1.5em;
}

.book-cta-img {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/6787c53671a97b8708dd704b_DJ%20Book-min.webp");
  background-position: 50%;
  background-repeat: no-repeat;
  background-size: cover;
  height: 14em;
}

.book-cta-img.bi-2 {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/6787c53671a97b8708dd7049_DJ%20Book-1-min.webp");
}

.book-cta-img.bi-3 {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/6787c53671a97b8708dd704a_DJ%20Book-2-min.webp");
}

.image-7 {
  height: 100%;
}

.interview-img {
  z-index: 1;
  background-image: url("https://d3e54v103j8qbb.cloudfront.net/img/background-image.svg");
  background-position: 50%;
  background-repeat: no-repeat;
  background-size: cover;
  width: 100%;
  min-width: 110%;
  height: 110%;
  position: absolute;
}

.hero-100 {
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 160vh;
  display: flex;
  overflow: hidden;
}

.hero-100.is-gallery {
  justify-content: center;
  align-items: center;
  height: 100vh;
  position: sticky;
  top: 0;
  overflow: hidden;
}

.hero-100.is-articles {
  height: 140vh;
  position: relative;
}

.hero-100.is-books, .hero-100.is-home {
  height: 120vh;
}

.hero-100.is-flex-v-c {
  flex-flow: column;
  height: 100vh;
}

.hero-100.is-flex-v-c.is-sticky {
  grid-column-gap: 1em;
  grid-row-gap: 1em;
  position: sticky;
  top: 0;
}

.h-span {
  font-family: Instrument Serif, sans-serif;
  font-size: .98em;
  font-style: italic;
  font-weight: 400;
  display: inline-block;
}

.hero-h-wrapper {
  grid-column-gap: .5em;
  grid-row-gap: .5em;
  flex-flow: column;
  width: 100%;
  display: flex;
}

.hero-h-wrapper.is-80 {
  grid-column-gap: .2em;
  grid-row-gap: .2em;
  cursor: default;
  flex-flow: column;
  width: 80%;
  display: flex;
}

.hero-h-wrapper.is-gallery {
  justify-content: center;
  align-items: center;
  width: 100vw;
  height: 100vh;
  position: fixed;
  inset: 0% auto auto 0%;
}

.hero-h-wrapper.is-gallery.is-2 {
  z-index: 2;
  position: relative;
}

.hero-h-wrapper.is-gallery.is-1 {
  z-index: 1;
  perspective: 2000px;
}

.hero-h-wrapper.is-gallery-end {
  position: absolute;
}

.hero-h-wrapper.is-about.is-mobile, .hero-h-wrapper.is-mobile {
  display: none;
}

.hero-h-wrapper.is-pad {
  padding-left: 3em;
  padding-right: 3em;
}

.hero-h-wrapper.is-tablet {
  display: none;
}

.btn-circle {
  z-index: 2;
  background-color: var(--black);
  border-radius: 100px;
  justify-content: center;
  align-items: center;
  width: .75em;
  height: .75em;
  display: flex;
  position: relative;
}

.btn-circle.is-ab {
  background-color: var(--black);
  position: absolute;
  inset: 10% auto auto 10%;
}

.btn-circle.is-ab.is-transition {
  width: 1.5em;
  height: 1.5em;
}

.btn-circle.is-white {
  background-color: var(--white);
}

.btn-circle.is-footer {
  width: 1em;
  height: 1em;
}

.btn-text-wrap {
  z-index: 2;
  padding-top: .1em;
  position: relative;
}

.btn-text-wrap.is-pag {
  padding-top: .05em;
}

.btn-outer-frame {
  border: 2px dashed var(--black);
  border-radius: 200px;
  justify-content: center;
  align-items: center;
  padding: .2em;
  text-decoration: none;
  display: flex;
  position: relative;
}

.btn-outer-frame.is-interview {
  margin-top: 2.5em;
}

.btn-outer-frame.is-book {
  margin-top: 2.5em;
  display: flex;
  position: static;
}

.btn-outer-frame.is-desktop.is-white {
  border-color: var(--white);
}

.btn-outer-frame.is-mobile {
  display: none;
}

.btn-outer-frame.is-ab {
  border-color: var(--white);
  position: absolute;
  inset: auto 1.5em 1.5em auto;
}

.btn-outer-frame.is-ab-center {
  z-index: 2;
  position: absolute;
}

.hero-img-wrapper {
  perspective: 1000px;
  width: 100%;
  height: 100%;
  position: fixed;
  inset: 0% 0% auto;
}

.hero-img {
  background-image: url("../shou.png");
  background-position: 100% 0;
  background-repeat: no-repeat;
  background-size: cover;
  background-attachment: fixed;
  width: 100%;
  height: 100%;
}

.hero-img.is-books {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/678cf603684d4ed853629ae4_Books%20-%20Hero-min%20(1).jpg");
  background-position: 100% 30%;
}

.hero-img.is-articles {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/685cd3dcc707148ee0cd84f4_Hero%20Articles-min.jpg");
}

.hero-img.is-articles.is-mobile {
  display: none;
}

.hero-img.is-articles.is-top {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/682f05db5c039692f5b54e53_Int%20color%20hero-min.jpg");
}

.hero-img.is-btm {
  background-image: url("../shou.png");
}

.hero-img.is-btm.is-interviews {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/67c08d80cb3dffffdd891243_article%20hero-min.jpg");
}

.hero-img.is-top {
  z-index: 1;
  background-image: url("../shou.png");
  display: none;
  position: absolute;
}

.hero-img.is-top.is-interviews, .hero-img.is-interview.is-top {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/682f05db5c039692f5b54e53_Int%20color%20hero-min.jpg");
}

.hero-img.is-interview.is-btm {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/67c08d80cb3dffffdd891243_article%20hero-min.jpg");
}

._3-col-section {
  padding-top: 12.5em;
  padding-left: 3em;
  padding-right: 3em;
  display: flex;
}

.flex-v-l {
  flex-flow: column;
  justify-content: flex-start;
  align-items: flex-start;
  display: flex;
}

.text {
  color: var(--black);
  margin-bottom: 0;
  font-size: 1.375em;
  line-height: 1.3;
}

.text.text-white {
  color: var(--white);
}

.text.is-center {
  text-align: center;
}

.h3-bold {
  font-family: Mango Grotesque Vf, Arial, sans-serif;
  font-size: 3.5em;
  font-weight: 600;
  line-height: .8;
}

.d-400 {
  width: 40%;
}

.d-25 {
  width: 25%;
}

.h-divider {
  border-bottom: 2px dashed var(--brown);
  width: 100%;
}

.d-30 {
  width: 30%;
}

.d-30.is-rel-high {
  z-index: 2;
  position: relative;
}

.d-29 {
  width: 29%;
}

.ab-img-wrapper {
  border: 2px solid var(--brown);
  background-color: var(--white);
  border-radius: 8px;
  width: 27em;
  height: 38em;
  padding: .4em;
  position: absolute;
}

.image-8 {
  width: 100%;
  height: 100%;
}

.image-child {
  background-image: url("https://d3e54v103j8qbb.cloudfront.net/img/background-image.svg");
  background-position: 50%;
  background-repeat: no-repeat;
  background-size: cover;
  width: 110%;
  height: 130%;
  position: absolute;
}

.image-child.is-about {
  min-width: 100%;
  height: 120%;
}

.image-child.a-4 {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/682ae5d14ca602e04e0c3ce2_DJ%20About-1-min.jpg");
  background-position: 50%;
  background-repeat: no-repeat;
  background-size: cover;
  width: 106%;
  height: 120%;
}

.image-child.a-3 {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/682ae5d022a7c5b09eeea7dc_DJ%20About-min.jpg");
}

.image-child.a-2 {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/682aec73a46d07cc1742cdeb_a-3-min%20(1).jpg");
}

.image-child.a-1 {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/682aee1986fa532a41adf8e6_a-1-min%20(4).jpg");
}

.highlight-text {
  letter-spacing: -2px;
  margin-bottom: -.1em;
  font-family: Instrument Serif, sans-serif;
  font-size: 4.75em;
  font-style: italic;
  font-weight: 400;
  line-height: .7;
}

.btn-bg {
  background-color: var(--black);
  opacity: 0;
  width: 110%;
  height: 100%;
  position: absolute;
}

.btn-bg.is-white {
  background-color: var(--white);
}

.flex-split-btm {
  justify-content: space-between;
  align-items: flex-end;
  width: 100%;
  display: flex;
}

.art-heading-wrapper {
  justify-content: flex-start;
  align-items: center;
  width: 80%;
  padding-top: .5em;
  display: flex;
  position: relative;
  overflow: hidden;
}

.paper-div {
  z-index: 1;
  border-top: 2px dashed var(--black);
  border-bottom: 2px solid var(--cream);
  border-left: 2px dashed var(--black);
  background-color: var(--cream);
  justify-content: center;
  align-items: center;
  width: 5em;
  height: 5em;
  display: flex;
  position: absolute;
  inset: auto 0% -2% auto;
  overflow: hidden;
}

.paper-div.is-transition {
  width: 100vw;
  height: 100vh;
}

.paper-div.is-int {
  background-color: var(--white);
  bottom: 0%;
}

.triangle {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/6797ac1747c0f3f6835be89b_dj-triangle.svg");
  background-position: 50%;
  background-repeat: no-repeat;
  background-size: cover;
  width: 5em;
  height: 5em;
}

.triangle.is-transition {
  opacity: 1;
  width: 100vw;
  height: 100vh;
}

.h-row {
  grid-column-gap: .9em;
  grid-row-gap: .9em;
  display: flex;
  position: relative;
}

.h-row.neg-top-margin {
  margin-top: -.6em;
}

.h-row.is-center {
  justify-content: center;
  align-items: center;
}

.h-row.is-under-special {
  margin-top: -.7em;
}

.h-row.is-cta-center {
  z-index: 5;
  position: absolute;
}

.h-row.is-mobile {
  display: none;
}

.h-row.is---action {
  grid-column-gap: 2em;
  grid-row-gap: 2em;
  justify-content: flex-start;
  align-items: flex-end;
}

.h-hide {
  padding-top: 1.4em;
  padding-bottom: .3em;
  position: relative;
  overflow: hidden;
}

.h-hide.is-load {
  padding-top: .1em;
}

.h-hide.is-h2-super {
  padding-top: 1.6em;
}

.h-hide.is-h2-super.is-below-special {
  margin-top: -1.6em;
}

.h-hide.contact-form {
  padding-top: 1.25em;
  padding-bottom: .5em;
}

.h-hide.italic {
  padding: 1.25em .75em .75em;
}

.h-hide.italic.is-super {
  padding-top: .4em;
}

.heading-7 {
  line-height: .85;
}

.h1-italic {
  letter-spacing: -3px;
  cursor: default;
  font-family: Instrument Serif, sans-serif;
  font-size: 6.125em;
  font-style: italic;
  font-weight: 400;
  line-height: .8;
  position: static;
}

.h1-italic.cta {
  color: var(--white);
  -webkit-text-stroke-width: 1px;
  -webkit-text-stroke-color: var(--black);
}

.h1-italic.is-super {
  font-size: 10em;
}

.h1-italic.is-super.is-center {
  z-index: 2;
  text-align: center;
  position: relative;
}

.h-hide-italic {
  padding-top: .35em;
  padding-left: .4em;
  padding-right: .4em;
  position: relative;
  overflow: hidden;
}

.h-hide-italic.is-load {
  padding-top: .1em;
  padding-bottom: 3.5em;
  padding-left: 2.5em;
}

.h-hide-italic.is-h3 {
  padding-top: 0;
  padding-bottom: .5em;
}

.h-hide-italic.is-h2-super {
  padding-top: 0;
  padding-left: 1em;
  padding-right: 1em;
}

.h-hide-italic.is-special {
  padding-bottom: .7em;
}

.h-hide-italic.is-starting {
  margin-left: -.5em;
  padding-left: 0;
}

.h-hide-italic.is-right {
  padding-right: .6em;
}

.whipe-r-white {
  background-color: var(--white);
  width: 100%;
  height: 100%;
  position: absolute;
  inset: 0% 0% 0% auto;
}

.whipe-r-brown {
  background-color: var(--black);
  width: 100%;
  height: 100%;
  position: absolute;
  inset: 0% auto 0% 0%;
}

.book-img-wrapper {
  border: 2px dashed var(--black);
  border-radius: 2px;
  height: 30em;
  padding: .375em;
}

.footer-move {
  width: 162em;
  display: flex;
}

.about-h-row {
  justify-content: center;
  align-items: center;
  padding: 3em;
  display: flex;
  position: relative;
}

.about-h-row.is-last {
  border-bottom-style: none;
}

.h-no {
  font-size: 3em;
  font-style: italic;
  line-height: .9;
}

.h-no.is-mobile {
  display: none;
}

.flex-split-top {
  justify-content: space-between;
  align-items: flex-start;
  width: 100%;
  display: flex;
}

.scroll-bar-wrapper {
  justify-content: flex-start;
  align-items: center;
  display: flex;
}

.bg-bar {
  background-color: var(--black);
  border-radius: 100px;
  width: 100%;
  height: .25em;
}

.scroll-bar {
  border: 1px solid var(--black);
  background-color: var(--white);
  border-radius: 100px;
  width: 12.5em;
  height: .5em;
  position: absolute;
}

.flex-center {
  justify-content: center;
  align-items: flex-start;
  width: 100%;
  display: flex;
  position: relative;
}

.flex-center.is-v {
  flex-flow: column;
}

.flex-center.is-v.is-home {
  grid-column-gap: 2em;
  grid-row-gap: 2em;
  padding-top: 3em;
  padding-left: 6em;
  padding-right: 16em;
}

.flex-center.pagination {
  grid-column-gap: 1em;
  grid-row-gap: 1em;
  justify-content: center;
  align-items: center;
  margin-top: 0;
}

.d-80 {
  width: 80%;
}

.about-img-wrapper {
  border: 2px dashed #000;
  border-radius: 4px;
  width: 100%;
  height: 23.75em;
  padding: .375em;
}

.about-img-wrapper.is-a-3 {
  height: 36em;
}

.about-img-wrapper.is-a-4 {
  height: 28em;
}

.about-img-wrapper.is-a-2 {
  width: 47.5em;
  height: 26em;
}

.about-img-wrapper.is-a-5 {
  width: 48.75em;
  height: 27em;
}

.about-img-wrapper.is-a-6 {
  width: 50%;
  height: 50%;
}

.para-wrapper.is-a-1 {
  width: 26.75em;
  padding-left: 4em;
}

.para-wrapper.is-a-2 {
  width: 60%;
  margin-bottom: 5em;
}

.para-wrapper.is-a-4 {
  width: 30%;
}

.d-35 {
  width: 35%;
}

.d-55 {
  width: 55%;
}

.flex-v-left {
  flex-flow: column;
  justify-content: flex-start;
  align-items: flex-start;
  width: 100%;
  display: flex;
}

.flex-v-left.is-l-pad-3 {
  padding-left: 3em;
}

.d-40 {
  width: 40%;
}

.sticky-section {
  background-color: var(--white);
  height: 240vh;
  margin-top: -50vh;
  padding-bottom: 0;
  padding-left: 3em;
  padding-right: 3em;
  position: relative;
}

.sticky-section.is-home {
  margin-top: -56vh;
}

.sticky-section.is-img {
  margin-top: -45vh;
  padding-left: 2.5em;
  padding-right: 2.5em;
}

.collection-list-wrapper-3 {
  width: 100%;
}

.int-img-wrapper {
  z-index: 2;
  border: 2px dashed var(--black);
  background-color: var(--white);
  border-radius: 4px;
  width: 24em;
  height: 32em;
  padding: .375em;
  position: relative;
}

.int-img-wrapper.is-hov-1, .int-img-wrapper.is-hov-2 {
  z-index: 1;
  width: 18em;
  height: 24.5em;
  position: absolute;
  top: 10em;
  left: 41em;
}

.flex-v-split {
  flex-flow: column;
  justify-content: space-between;
  align-items: flex-start;
  height: 100%;
  display: flex;
}

.flex-v-split.is-right {
  justify-content: space-between;
  align-items: flex-end;
}

.flex-v-split.is-home-int {
  grid-column-gap: 1em;
  grid-row-gap: 1em;
  justify-content: flex-start;
  width: 100%;
}

.flex-v-split.is-home-int.final-cta {
  width: 100%;
  padding-top: 3em;
  padding-bottom: 3em;
}

.pagination-top {
  grid-column-gap: .375em;
  grid-row-gap: .375em;
  border: 2px dashed var(--brown);
  border-radius: 100px;
  padding: .5em;
  display: flex;
}

.pagination-no {
  border: 1px solid var(--black);
  color: var(--black);
  border-radius: 100%;
  justify-content: center;
  align-items: center;
  width: 2.5em;
  height: 2.5em;
  font-family: Mango Grotesque Vf, Arial, sans-serif;
  font-size: 1em;
  font-weight: 600;
  text-decoration: none;
  display: flex;
}

.pagination-no.w--current {
  background-color: var(--black);
  color: var(--white);
}

.pag-no {
  margin-top: .25em;
  font-family: Mango Grotesque Vf, Arial, sans-serif;
  font-size: 1.25em;
  font-weight: 600;
  line-height: .6;
}

.previous, .next {
  display: none;
}

.book-col-img-wrapper {
  border: 2px dashed var(--black);
  border-radius: 8px;
  width: 24em;
  height: 70vh;
  padding: .375em;
}

.book-content-wrapper {
  flex-flow: column;
  justify-content: space-between;
  align-items: flex-start;
  width: 22em;
  padding: 2em 1.5em 0;
  display: flex;
}

.sticky-container {
  justify-content: flex-end;
  align-items: flex-end;
  width: 100%;
  height: 100vh;
  padding-bottom: 2em;
  display: flex;
  position: sticky;
  top: 10px;
}

.flex-c-btm {
  justify-content: flex-start;
  align-items: flex-end;
  padding-left: 17.25em;
  padding-right: 17.25em;
  display: flex;
}

.marq-column {
  justify-content: flex-start;
  align-items: center;
  width: 128em;
  display: flex;
}

.marq-column.is-gal-marq {
  width: 224em;
}

.text-caps {
  text-transform: uppercase;
  font-size: 1.75em;
  line-height: .8;
}

.text-caps.is-gal-marq {
  color: var(--black);
  white-space: nowrap;
  font-size: 2.5em;
  font-style: italic;
}

.text-caps.is-italic {
  font-size: 2em;
  font-style: italic;
}

.text-caps.is-italic.text-white {
  margin-bottom: 0;
}

.text-caps.is-home-int {
  font-size: 4em;
  font-style: italic;
}

.text-cap-wrap {
  justify-content: center;
  align-items: center;
  width: 11em;
  padding-top: .2em;
  display: flex;
}

.text-cap-wrap.is-gal-marq {
  white-space: nowrap;
  width: 22em;
  padding-top: .4em;
}

.text-cap-wrap.is-home-int {
  width: 27em;
}

.marquee-circle {
  background-color: var(--black);
  border-radius: 100%;
  width: 1em;
  height: 1em;
}

.marquee-circle.is-gal-dash {
  background-color: var(--black);
  width: 2em;
  height: 2px;
}

.marquee-circle.bg-white {
  background-color: var(--white);
}

.marq-set {
  grid-column-gap: 2em;
  grid-row-gap: 2em;
  justify-content: flex-start;
  align-items: center;
  width: 32em;
  padding-left: 2em;
  display: flex;
}

.marq-set.is-gal-marq {
  width: 56em;
}

.marq-set.is-home-int {
  width: 66em;
}

.h-italic-wrapper.is-hero.is-btm-gap {
  padding-bottom: 1em;
  padding-left: .6em;
}

.h-italic-wrapper.is-h3 {
  padding-top: .4em;
}

.h-italic-wrapper.is-h3.is-special {
  padding-bottom: 1em;
}

.h-italic-wrapper.is-h2-super {
  padding-top: 1em;
  padding-bottom: .6em;
}

.h-italic-wrapper.is-h2-super.is-special {
  padding-bottom: 2.4em;
}

.h-italic-wrapper.is-h2-super.is-sp-hor {
  padding-right: 2em;
}

.btn-arrow-div {
  width: 1.25em;
  height: .625em;
}

.bg-row {
  width: 100%;
  display: flex;
  position: relative;
}

.hover-block {
  background-color: var(--brown);
  width: 2em;
  height: 2em;
  display: inline-block;
}

.bg-holder {
  z-index: 999;
  width: 100vw;
  height: 100vh;
  display: block;
  position: fixed;
  inset: 0% 0% auto;
}

.load-100 {
  z-index: 998;
  width: 100vw;
  height: 100vh;
  display: none;
  position: fixed;
}

.load-split {
  z-index: 2;
  flex-flow: column;
  justify-content: space-between;
  align-items: stretch;
  width: 100%;
  height: 100%;
  padding-top: 2em;
  padding-bottom: .5em;
  display: flex;
  position: relative;
}

.load-marq-move {
  grid-column-gap: 1.5em;
  grid-row-gap: 1.5em;
  width: 60.2em;
  padding-right: 1.5em;
  display: flex;
}

.load-marq-text {
  color: var(--black);
  text-transform: uppercase;
  font-family: Mango Grotesque Vf, Arial, sans-serif;
  font-size: 2.5em;
  font-weight: 500;
  line-height: .6;
}

.load-marq-text-wrap {
  padding-top: .5em;
}

.marquee-track {
  width: 240.8em;
  display: flex;
}

.marquee-track.is-home-cream {
  border-top: 1px solid var(--black);
  border-bottom: 1px solid var(--black);
  background-color: var(--cream);
  width: 252.8em;
  padding-top: 2.5em;
  padding-bottom: 2.5em;
}

.marquee-track.is-home-black {
  background-color: var(--black);
  justify-content: flex-end;
  width: 210em;
  padding-top: 2.5em;
  padding-bottom: 2.5em;
}

.load-img-wrapper {
  width: 100%;
  height: 100%;
  position: absolute;
  inset: 0%;
}

.load-img {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/6787db45232a0f099aa07a84_Hero-min%20(1).jpg");
  background-position: 50%;
  background-repeat: no-repeat;
  background-size: cover;
  width: 100%;
  height: 100%;
}

.heading-super {
  color: var(--black);
  text-transform: uppercase;
  font-family: Mango Grotesque Vf, Arial, sans-serif;
  font-size: 16em;
  font-weight: 600;
  line-height: .56;
}

.h-super-wrapper {
  justify-content: center;
  align-items: center;
  padding-top: 2.5em;
  display: flex;
}

.counter-wrapper {
  padding-top: .5em;
  padding-bottom: 3.5em;
  display: flex;
}

.counter {
  color: var(--black);
  font-size: 6em;
  font-style: italic;
  line-height: .7;
}

.heading-super-italic {
  color: var(--black);
  letter-spacing: -6px;
  font-family: Instrument Serif, sans-serif;
  font-size: 15.5em;
  font-style: italic;
  font-weight: 400;
  line-height: .7;
}

.h-super-italic-wrapper {
  justify-content: center;
  align-items: center;
  padding-top: .8em;
  display: flex;
}

.rotating-star-wrap {
  width: 11.8em;
  height: 11.8em;
}

.rotating-star-wrap.is-gallery {
  width: 8em;
  height: 8em;
  display: none;
  position: absolute;
  inset: 2.5em auto auto 2.5em;
}

.rotating-star {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/67982f3c2716bc5bf11847dc_DJ-Black%20Star.svg");
  background-position: 50%;
  background-repeat: no-repeat;
  background-size: cover;
  width: 100%;
  height: 100%;
}

.rotating-star.is-black {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/67982f3c2716bc5bf11847dc_DJ-Black%20Star.svg");
}

.whipe-l-white {
  background-color: var(--white);
  width: 100%;
  height: 100%;
  position: absolute;
  inset: 0% auto 0% 0%;
}

.whipe-l-white.load {
  background-color: var(--black);
}

.percent {
  color: var(--black);
  font-size: 6em;
  line-height: .7;
}

.trans-100 {
  z-index: 1000;
  width: 100vw;
  height: 100vh;
  display: none;
  position: fixed;
}

.shutter {
  background-color: var(--white);
  width: 100%;
  height: 100%;
}

.h1-hero.cta {
  color: var(--white);
  -webkit-text-stroke-width: 1px;
  -webkit-text-stroke-color: var(--black);
}

.home-hero-section {
  flex-flow: column;
  height: 140vh;
  display: flex;
  position: relative;
}

.home-hero-section.is-books, .home-hero-section.is-home {
  height: 120vh;
}

.highlight-wrap {
  grid-column-gap: .4em;
  grid-row-gap: .4em;
  flex-flow: column;
  display: flex;
}

.h-light-text-wrap {
  grid-column-gap: .6em;
  grid-row-gap: .6em;
  flex-flow: column;
  margin-bottom: 1em;
  display: flex;
}

.h-light-text-wrap.is-1 {
  margin-bottom: 0;
  padding-left: 32em;
  padding-right: 2em;
}

.h-light-text-wrap.is-2 {
  padding-left: 12em;
  padding-right: 10em;
}

.all-caps-bold {
  text-transform: uppercase;
  font-family: Mango Grotesque Vf, Arial, sans-serif;
  font-size: 2em;
  font-weight: 600;
  line-height: 1.2;
}

.d-60 {
  width: 60%;
}

.d-60.is-flex-l {
  grid-column-gap: 1em;
  grid-row-gap: 1em;
  flex-flow: column;
  justify-content: flex-start;
  align-items: flex-start;
  display: flex;
}

.h-wrapper {
  grid-column-gap: .25em;
  grid-row-gap: .25em;
  flex-flow: column;
  display: flex;
}

.h-wrapper.is-pad {
  padding-left: 3em;
  padding-right: 3em;
}

.h-wrapper.is-center {
  grid-column-gap: 1em;
  grid-row-gap: 1em;
  background-color: var(--cream);
  justify-content: center;
  align-items: center;
  height: 100vh;
  margin-bottom: 2em;
  position: sticky;
  top: 0;
}

.d-36 {
  width: 36%;
}

.whipe-l-black {
  background-color: var(--black);
  width: 100%;
  height: 100%;
  position: absolute;
  inset: 0% auto 0% 0%;
}

.flex-right {
  justify-content: flex-end;
  width: 100%;
  display: flex;
}

.flex-right.d-60 {
  width: 60%;
}

.h2-super-italic {
  color: var(--black);
  font-family: Instrument Serif, sans-serif;
  font-size: 9em;
  font-style: italic;
  font-weight: 400;
}

.h2-super-italic.is-white {
  color: var(--white);
}

.h2-super-italic.is-brown {
  color: var(--brown);
}

.h2-super-italic.is-black {
  color: var(--white);
  -webkit-text-stroke-width: 1px;
  -webkit-text-stroke-color: var(--black);
  font-weight: 400;
}

.h2-super {
  color: var(--black);
  font-size: 10em;
  font-weight: 700;
}

.h2-super.is-white {
  color: var(--white);
}

.h2-super.is-black {
  color: var(--white);
  text-shadow: -1px -1px 0 var(--black), 1px -1px 0 var(--black), -1px 1px 0 var(--black), 1px 1px 0 var(--black);
}

.sticky-hero {
  justify-content: center;
  align-items: center;
  width: 120vw;
  height: 120vw;
  display: flex;
  position: sticky;
  top: 0;
  overflow: hidden;
}

.white-container {
  background-color: var(--white);
  justify-content: center;
  align-items: center;
  width: 120vw;
  height: 120vw;
  display: flex;
  position: relative;
}

.white-container.is-z-1 {
  z-index: 10;
  position: absolute;
}

.black-container {
  background-color: var(--cream);
  justify-content: center;
  align-items: center;
  width: 120vw;
  height: 120vw;
  display: flex;
}

.container-hide {
  z-index: 10;
  border: 2px dashed var(--black);
  border-radius: 100%;
  justify-content: center;
  align-items: center;
  width: 120vw;
  height: 120vw;
  display: flex;
  position: absolute;
  overflow: hidden;
}

.hero-200 {
  z-index: 3;
  background-color: var(--black);
  width: 100%;
  height: 400vh;
  position: relative;
}

.gallery-wrapper {
  grid-column-gap: 0em;
  grid-row-gap: 0em;
  background-color: var(--cream);
  flex-flow: column;
  width: 375vw;
  height: 300vh;
  display: flex;
  position: relative;
}

.gallery {
  justify-content: flex-start;
  align-items: flex-start;
  width: 100%;
  height: 100vh;
  display: flex;
  position: sticky;
  top: 0;
  overflow: hidden;
}

.gallery-row {
  grid-column-gap: 30vw;
  grid-row-gap: 30vw;
  justify-content: flex-start;
  align-items: center;
  width: 375vw;
  height: 100vh;
  padding-left: 15vw;
  padding-right: 15vw;
  display: flex;
  position: relative;
}

.gallery-frame {
  z-index: 2;
  grid-column-gap: 1em;
  grid-row-gap: 1em;
  flex-flow: column;
  justify-content: flex-start;
  align-items: flex-start;
  display: flex;
  position: relative;
}

.gallery-img-wrapper {
  z-index: 2;
  width: 45vw;
  height: 27em;
  position: relative;
  overflow: hidden;
}

.gallery-img-wrapper.is-mobile {
  display: none;
}

.gallery-img {
  width: 100%;
  height: 100%;
}

.d-70 {
  width: 70%;
}

.d-70.is-hidden {
  width: 80%;
  overflow: hidden;
}

.text-l {
  font-size: 1.75em;
}

.text-l.text-white {
  color: var(--white);
}

.gallery-container {
  width: 100%;
  height: 3200vh;
}

.gal-text {
  color: var(--black);
  font-size: 1.5em;
}

.gal-line {
  background-color: var(--grey);
  width: 100%;
  height: 1px;
  position: absolute;
  inset: 44% 0% 50%;
}

.grid {
  grid-column-gap: 0px;
  grid-row-gap: 0px;
  flex-flow: column;
  grid-template-rows: auto auto auto auto auto auto auto auto;
  grid-template-columns: 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr;
  grid-auto-columns: 1fr;
  justify-content: space-between;
  align-items: stretch;
  width: 100%;
  height: 100%;
  display: flex;
  position: absolute;
  inset: 0%;
}

.grid.is-interviews {
  z-index: 1;
}

.div-block-6 {
  border-right: 1px solid var(--white);
}

.grid-row {
  color: var(--transparent);
  justify-content: space-between;
  width: 100%;
  height: 100%;
  display: flex;
}

.grid-row.is-mobile {
  display: none;
}

.grid-box {
  border-right: 2px dashed #ded9d0;
  border-bottom: 2px dashed var(--light-cream);
  color: #2c2b2b00;
  text-transform: uppercase;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  font-size: 0;
  font-style: italic;
  display: flex;
}

.grid-box.letter {
  text-transform: uppercase;
  font-size: 3em;
  font-style: italic;
}

.gallery-main-frame {
  z-index: 1;
  width: 100%;
  height: 100vh;
  padding-top: 200vh;
  position: absolute;
  inset: 0% auto auto 0%;
}

.hero-gradient {
  background-image: radial-gradient(circle farthest-side at 60% 100%, #f6f4f100 71%, #f6f4f1b3);
  width: 100%;
  height: 100%;
  position: absolute;
  inset: 0% 0% auto;
}

.article-flex {
  grid-column-gap: 2.5em;
  grid-row-gap: 2.5em;
  perspective: 1000px;
  justify-content: flex-start;
  align-items: flex-start;
  width: 92%;
  padding-left: 0;
  display: flex;
  position: relative;
}

.hero-sub-wrapper {
  width: 35%;
  margin-top: 2em;
}

.footer-inner-wrapper {
  grid-column-gap: 4em;
  grid-row-gap: 4em;
  background-color: var(--cream);
  flex-flow: column;
  padding-top: 3.5em;
  display: flex;
}

.keep-scrolling-div {
  border: 2px dashed var(--white);
  border-radius: 100px;
  justify-content: center;
  align-items: center;
  padding: .25em;
  display: flex;
}

.div-block-7 {
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  display: none;
  position: absolute;
}

.gal-inner-transition {
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  display: flex;
  position: absolute;
  overflow: hidden;
}

.bg-white-circle {
  background-color: var(--white);
  border-radius: 100%;
  width: 150vw;
  height: 150vw;
  position: absolute;
}

.strike-through {
  background-color: var(--brown);
  width: 100%;
  height: 2px;
  position: absolute;
  left: 0%;
}

.text-block-6 {
  color: var(--brown);
  letter-spacing: .2px;
  font-size: 1.25em;
  font-style: italic;
  font-weight: 400;
  line-height: .8;
  position: absolute;
  bottom: 25%;
  left: 40%;
  transform: rotate(-45deg);
}

.keep-scrolling-wrapper {
  z-index: 100;
  width: 100%;
  height: 100%;
  display: none;
  position: fixed;
  inset: 0% 0% auto;
}

.gallery-no {
  color: var(--white);
  text-shadow: 0 1px 1px var(--grey);
  font-size: 20em;
  font-style: italic;
  position: absolute;
  top: -30%;
  left: -10%;
}

.big-gal-text {
  opacity: .1;
  color: var(--white);
  text-transform: uppercase;
  font-size: 16em;
  font-style: italic;
  position: absolute;
  top: -.5em;
  left: 3em;
  right: auto;
}

.big-gal-text.is-1 {
  opacity: .05;
  display: none;
  top: auto;
  bottom: -.6em;
}

.gb-out {
  border-bottom: 2px dashed #51504e;
  border-right: 2px dashed #51504e;
  width: 100%;
  height: 100%;
}

.nav-line {
  background-color: var(--black);
  width: 1px;
  height: 1.25em;
  transform: rotate(30deg);
}

.nav-no {
  color: var(--black);
  text-transform: uppercase;
  font-family: Mango Grotesque Vf, Arial, sans-serif;
  font-size: 1.125em;
  font-weight: 600;
  line-height: .6;
}

.nav-no.pg-top {
  font-size: 1.25em;
}

.drop-pop {
  background-color: var(--white);
  border: 1px solid #000;
  width: 14em;
  padding: .4em .4em .5em;
  position: absolute;
}

.drop-pop.is-1 {
  position: absolute;
  bottom: -9em;
  right: 36em;
}

.drop-pop.is-2 {
  bottom: -9em;
  right: 32em;
}

.drop-pop.is-4 {
  bottom: -9em;
  right: 8em;
}

.drop-pop.is-3 {
  bottom: -9em;
  right: 18em;
}

.drop-pop.is-5 {
  bottom: -9em;
  right: 4em;
}

.drop-pop-img {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/678a2fcc1191bd3ac449e916_DJ-awww-about-3-min.jpg");
  background-position: 50%;
  background-repeat: no-repeat;
  background-size: cover;
  height: 7em;
}

.drop-pop-img.is-1 {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/67ea2aed78a3d80ab5734e2b_Nav%20img-1-min.jpg");
}

.drop-pop-img.is-2 {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/67ea2c8130d76b0505b8c49e_Nav%20img%20-%204-min.jpg");
}

.drop-pop-img.is-3 {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/67ea2c80f6c2d761159d7b07_Nav%20img%20-%205-min.jpg");
}

.drop-pop-img.is-4 {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/67ea2aed1f3a04d62bba28a4_Nav%20img-min.jpg");
}

.drop-pop-img.is-5 {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/67ea2c80568b179d8f002238_Nav%20img%20-%203-min.jpg");
}

.drop-pop-wrapper {
  z-index: 4;
  flex-flow: column;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 30vh;
  display: flex;
  position: fixed;
  inset: auto 0% 0%;
}

.page-bar {
  z-index: 1000;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding-top: 1em;
  padding-left: 3em;
  padding-right: 3em;
  display: flex;
  position: fixed;
  inset: 0% 0% auto;
  overflow: hidden;
}

.h-scroll-bar {
  grid-column-gap: .75em;
  grid-row-gap: .75em;
  justify-content: flex-start;
  align-items: center;
  display: flex;
}

.scroll-point {
  border: 2px solid var(--black);
  border-radius: 100%;
  width: .65em;
  height: .65em;
}

.scroll-point.is-1 {
  background-color: var(--black);
}

.sroll-line {
  background-color: var(--black);
  border-radius: 10px;
  width: 1em;
  height: 1px;
}

.section-part-2 {
  z-index: 2;
  width: 100%;
  position: relative;
  overflow: hidden;
}

.section-part-2.is-articles {
  position: relative;
  overflow: hidden;
}

.section-part-2.is-sp, .section-part-2.is-books, .section-part-2.is-overflow {
  overflow: visible;
}

.section-part-3 {
  width: 100%;
}

.section-part-3._140 {
  z-index: 2;
  margin-top: 140vh;
  position: relative;
}

.section-part-5 {
  position: relative;
}

.section-part-5.is-home {
  z-index: 10;
  overflow: hidden;
}

.footer-big-txt {
  color: var(--black);
  text-transform: uppercase;
  font-family: Mango Grotesque Vf, Arial, sans-serif;
  font-size: 9em;
  font-weight: 600;
  line-height: .5;
}

.footer-big-txt-wrap {
  width: 100%;
  padding-top: .6em;
}

.footer-flex {
  z-index: 1;
  grid-column-gap: .75em;
  grid-row-gap: .75em;
  flex-flow: column;
  justify-content: flex-start;
  align-items: flex-start;
  width: 66.6667%;
  display: flex;
  position: relative;
}

.footer-right {
  z-index: 5;
  grid-column-gap: 0em;
  grid-row-gap: 0em;
  flex-flow: column;
  justify-content: flex-start;
  align-items: flex-end;
  display: flex;
  position: relative;
}

.gtnp-text {
  color: var(--black);
  font-family: Mango Grotesque Vf, Arial, sans-serif;
  font-size: 3em;
  font-weight: 600;
  line-height: .6;
}

.np-text {
  color: var(--black);
  text-transform: uppercase;
  font-size: 1.75em;
  font-style: italic;
  font-weight: 400;
  text-decoration: none;
}

.np-text:hover {
  color: var(--brown);
}

.circle-cta-wrapper {
  z-index: 2;
  background-color: var(--cream);
  border-top: 2px dashed #000;
  justify-content: center;
  align-items: center;
  width: 100vw;
  height: 100vh;
  display: flex;
  position: relative;
  inset: 0% 0% 0;
  overflow: hidden;
}

.circle-cta-wrapper.is-home {
  width: 100%;
  height: 84em;
}

.inner-circle-cream {
  z-index: 1;
  background-color: var(--white);
  border-radius: 100%;
  width: 22.5em;
  height: 22.5em;
  position: absolute;
}

.inner-circle-cream.is-home {
  width: 32em;
  height: 32em;
}

.inner-circle-cream.is-articles {
  background-color: var(--cream);
  width: 40em;
  height: 40em;
}

.inner-circle-white {
  background-color: var(--cream);
  border-radius: 100%;
  width: 45em;
  height: 45em;
  position: absolute;
}

.inner-circle-white.is-home {
  background-color: #ede7df00;
}

.div-block-10 {
  z-index: 10;
  grid-column-gap: 1em;
  grid-row-gap: 1em;
  flex-flow: column;
  justify-content: center;
  align-items: center;
  display: flex;
  position: absolute;
}

.outer-dotted {
  z-index: 0;
  border: 2px dashed var(--black);
  border-radius: 100%;
  width: 88em;
  height: 88em;
  position: absolute;
}

.rotate-interviews {
  z-index: 3;
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/67dec4d7d88e614e5ddd8218_Interview%20rotate.svg");
  background-position: 50%;
  background-repeat: no-repeat;
  background-size: cover;
  width: 62em;
  height: 62em;
  position: absolute;
}

.rotate-interviews.is-black {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/67dec4d7d88e614e5ddd8218_Interview%20rotate.svg");
}

.rotate-interviews.is-black.is-articles {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/67eab7b8b1d2e082c9527cb9_Rotate-articles.svg");
}

.rotate-interviews.is-white {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/67ded77b5bd12b628786f57b_Linked%20Path%20Group%202.svg");
}

.rotate-interviews.is-white.is-articles {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/67eab7b84096103d400411d9_Rotate-articles-1.svg");
}

.outer-circle-cream {
  z-index: 1;
  background-color: var(--white);
  border-radius: 100%;
  width: 74em;
  height: 74em;
  position: absolute;
}

.inner-dotted {
  border: 2px dashed var(--black);
  border-radius: 100%;
  width: 34em;
  height: 34em;
  position: absolute;
}

.inner-dotted.is-home {
  width: 64em;
  height: 64em;
}

.rot-img-wrapper {
  z-index: 1;
  border-radius: 100%;
  justify-content: center;
  align-items: center;
  width: 48em;
  height: 48em;
  display: flex;
  position: absolute;
}

.rot-img-wrapper.is-home {
  perspective: 1000px;
  width: 74em;
  height: 74em;
}

.rot-int-img {
  background-image: url("https://d3e54v103j8qbb.cloudfront.net/img/background-image.svg");
  background-position: 50%;
  background-repeat: no-repeat;
  background-size: cover;
  width: 8.5em;
  height: 11.25em;
  position: absolute;
  top: 0%;
  bottom: auto;
}

.rot-int-img.is-1 {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/67decb886ff9ec4a82c91619_Int-rot-img.jpg");
}

.rot-int-img.is-2 {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/67decb8881ae5431776bd46c_Int-rot-img-2.jpg");
  top: auto;
  bottom: 0%;
}

.rot-int-img.is-3 {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/67decb88c4238ec5251e04ba_Int-rot-img-1.jpg");
  top: auto;
  left: 1.25em;
  right: 0%;
  transform: rotate(-90deg);
}

.rot-int-img.is-4 {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/67decca25b1d1dd831b4730c_Int-rot-img4.jpg");
  top: auto;
  left: auto;
  right: 1.25em;
  transform: rotate(90deg);
}

.rot-int-img.is-gal {
  width: 100%;
  height: 100%;
  position: static;
}

.rot-int-img.is-gal.is-one {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/679a25a2ad251a4f12a17838_Mask%20group-12-min.jpg");
}

.rot-int-img.is-gal.is-two {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/679a25a13790d9c776663f58_Mask%20group-min.jpg");
}

.rot-int-img.is-gal.is-three {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/67ea2c80568b179d8f002238_Nav%20img%20-%203-min.jpg");
}

.rot-int-img.is-gal.is-four {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/679a25a2b7122504696f2a8d_Mask%20group-2-min.jpg");
}

.rot-int-img.is-gal.is-five {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/679a25a167429b5d0c6486a2_Mask%20group-4-min.jpg");
}

.rot-int-img.is-gal.is-six {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/679a25a188a87b6fd8abe47b_Mask%20group-3-min.jpg");
}

.rot-int-img.is-gal.is-seven {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/679a25a14ba4d3036a9d6ff6_Mask%20group-11-min.jpg");
}

.rot-int-img.is-gal.is-eight {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/679a25076c5b006addc89cdf_Mask%20group-9-min.jpg");
}

.inner-set {
  z-index: 3;
  border-radius: 100%;
  justify-content: center;
  align-items: center;
  width: 48em;
  height: 48em;
  display: flex;
  position: absolute;
}

.rot-int-wrapper {
  z-index: 2;
  border-radius: 100%;
  justify-content: center;
  align-items: center;
  width: 74em;
  height: 74em;
  text-decoration: none;
  display: flex;
  position: absolute;
}

.hov-circle {
  z-index: 1;
  background-color: var(--black);
  border-radius: 100%;
  width: 100%;
  height: 100%;
  position: absolute;
  inset: 0%;
}

.height-160 {
  width: 100%;
  height: 160vh;
}

.int-img-panel {
  width: 18em;
  height: 100%;
  position: absolute;
}

.int-img-panel.is-left {
  z-index: 1;
  justify-content: center;
  align-items: center;
  display: none;
  inset: 0% auto 0% 0%;
}

.int-img-panel.is-right {
  z-index: 1;
  justify-content: center;
  align-items: center;
  display: none;
  inset: 0% 0% 0% auto;
}

.int-img-div {
  z-index: 2;
  background-image: url("https://d3e54v103j8qbb.cloudfront.net/img/background-image.svg");
  background-position: 50%;
  background-repeat: no-repeat;
  background-size: cover;
  width: 7em;
  height: 8.5em;
  position: absolute;
}

.int-img-div.is-1 {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/67e01d6d686d1187210f36d0_Mask%20group-1-min%20(8).jpg");
  top: 6em;
}

.int-img-div.is-2 {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/67e01d6d6d470ef86752d6e3_Mask%20group-min%20(8).jpg");
  left: 2.5em;
}

.int-img-div.is-3 {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/67e01d6d35d3823cefa278ef_Mask%20group-2-min%20(4).jpg");
  bottom: 6em;
}

.int-img-div.is-4 {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/67e0219da8c55a4c86408820_cta-min.jpg");
  top: 6em;
}

.int-img-div.is-5 {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/67e0219daefc55f74998f570_cta-2-min.jpg");
  right: 2.5em;
}

.int-img-div.is-6 {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/67e0219d40ba14c0f26de9bf_cta-1-min.jpg");
  bottom: 6em;
}

.article-date-wrap {
  width: 6.5%;
  margin-top: .3em;
  padding-top: .1em;
  position: relative;
}

.whipe-cream {
  background-color: var(--cream);
  width: 100%;
  height: 100%;
  position: absolute;
  inset: 0% auto 0% 0%;
}

.transition {
  z-index: 1002;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  min-height: 100vh;
  max-height: 100vh;
  display: none;
  position: fixed;
  inset: 0% 0% auto;
  overflow: hidden;
}

.trans-box {
  border: 2px dashed var(--black);
  background-color: var(--cream);
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  display: flex;
}

.trans-box.is-intro {
  justify-content: center;
  align-items: center;
  display: flex;
}

.trans-flex {
  background-color: var(--cream);
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  padding: 1em;
  display: flex;
  position: absolute;
}

.trans-flex.is-intro {
  inset: 0%;
}

.transition-intro {
  z-index: 1001;
  cursor: default;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100vh;
  display: none;
  position: fixed;
  inset: 0% 0% auto;
  overflow: hidden;
}

.page-intro-text {
  color: var(--white);
  letter-spacing: -14px;
  -webkit-text-stroke-width: 1px;
  -webkit-text-stroke-color: var(--black);
  font-size: 20em;
  font-style: italic;
  line-height: .7;
}

.page-intro-text.is-sm {
  font-size: 18em;
}

.page-intro-text-wrap {
  z-index: 1;
  white-space: nowrap;
  padding: 1.125em 2em .25em;
  position: relative;
  overflow: hidden;
}

.page-intro-text-wrap.more-right {
  padding-right: 3em;
}

.page-intro-text-wrap.is-gallery {
  padding-right: 4em;
}

.page-intro-text-wrap.is-sp-home {
  padding-bottom: 4.5em;
  padding-left: 3.5em;
  display: none;
}

.page-intro-text-wrap.is-sp-home.is-m-hidden {
  display: flex;
}

.page-intro-text-wrap.is-sp-home-top {
  margin-top: 4em;
  display: none;
}

.page-intro-text-wrap.is-sp-home-top.is-m-hidden {
  display: flex;
}

.span-text {
  display: inline-block;
}

.span-text.is-1.is-fill, .span-text.is-2.is-fill, .span-text.is-3.is-fill, .span-text.is-4.is-fill, .span-text.is-5.is-fill {
  color: var(--black);
  -webkit-text-stroke-width: 0px;
}

.span-text.is-6.is-sp {
  margin-left: -.05em;
  margin-right: -.05em;
}

.book-intro-img-wrapper {
  width: 16em;
  height: 23.5em;
  position: absolute;
}

.book-intro-img-wrapper.is-1 {
  inset: -3em auto auto 4.5em;
  transform: rotate(-15deg);
}

.book-intro-img-wrapper.is-2 {
  bottom: -6em;
  left: 24em;
  transform: rotate(35deg);
}

.book-intro-img-wrapper.is-3 {
  top: 4em;
  right: 24em;
  transform: rotate(15deg);
}

.book-intro-img-wrapper.is-4 {
  bottom: -2em;
  right: -2em;
  transform: rotate(-25deg);
}

.book-intro-img {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/67e1433cadd06bf77f357ed2_Book%20Intro%20img-2.jpg");
  background-position: 50%;
  background-repeat: no-repeat;
  background-size: cover;
  width: 100%;
  height: 100%;
}

.book-intro-img.is-2 {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/67e1433c1f94f1aba0218c7c_Book%20Intro%20img-1.jpg");
}

.book-intro-img.is-3 {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/67e1433cd34a31e7957c9139_Book%20Intro%20img.jpg");
}

.book-intro-img.is-4 {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/6835b004177815a656ca8aa8_Book%20intro%20images-min.jpg");
}

.trans-box-out {
  width: 100%;
  height: 100%;
  padding: 1em;
}

.trans-box-in {
  border: 2px dashed var(--black);
  background-color: var(--cream);
  flex-flow: column;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  display: flex;
}

.article-intro-div {
  border: 1px solid var(--black);
  width: 25em;
  padding: 2.5em;
  position: absolute;
}

.article-intro-div.is-white {
  background-color: var(--white);
  position: absolute;
}

.article-intro-div.is-white.is-1 {
  display: flex;
  top: 6em;
  left: -4.5em;
  transform: rotate(-15deg);
}

.article-intro-div.is-white.is-2 {
  bottom: 2.5em;
  right: -2.5em;
  transform: rotate(-10deg);
}

.article-intro-div.is-white.is-cta {
  width: 16em;
  padding: 1.5em;
  top: 6em;
  left: 2.5em;
}

.article-intro-div.is-white.is-cta.is-1 {
  transform: none;
}

.article-intro-div.is-white.is-cta.is-2 {
  top: auto;
  bottom: auto;
  left: -.5em;
  transform: none;
}

.article-intro-div.is-white.is-cta.is-3 {
  top: auto;
  bottom: 6em;
}

.article-intro-div.is-white.is-ab-1 {
  bottom: 6em;
  left: -1em;
  transform: rotate(-10deg);
}

.article-intro-div.is-white.is-ab-2 {
  top: 0;
  right: 32em;
  transform: rotate(15deg);
}

.article-intro-div.is-white.is-ab-3 {
  display: none;
  bottom: 4em;
  right: 4em;
  transform: rotate(-15deg);
}

.article-intro-div.is-white.is-home-1 {
  justify-content: center;
  align-items: center;
  width: 22em;
  padding: 2em 2em 1.6em;
  display: none;
  inset: 4em auto auto -2%;
  transform: rotate(20deg);
}

.article-intro-div.is-white.is-home-3 {
  padding-bottom: 1.6em;
  display: none;
  top: 16em;
}

.article-intro-div.is-white.is-home-4 {
  justify-content: center;
  align-items: center;
  width: 22em;
  padding: 2em 2em 1.5em;
  display: flex;
  bottom: 2em;
  left: 24em;
  transform: rotate(-20deg);
}

.article-intro-div.is-white.is-home-5 {
  justify-content: center;
  align-items: center;
  width: 22em;
  padding: 2em 2em 1.5em;
  display: none;
  bottom: 4em;
  left: 23em;
  transform: rotate(-15deg);
}

.article-intro-div.is-black {
  background-color: var(--black);
}

.article-intro-div.is-black.is-1 {
  position: absolute;
  bottom: 4em;
  left: 10em;
  transform: rotate(20deg);
}

.article-intro-div.is-black.is-2 {
  position: absolute;
  top: 1em;
  right: 20em;
  transform: rotate(-30deg);
}

.article-intro-div.is-black.is-home-2 {
  justify-content: center;
  align-items: center;
  width: 22em;
  padding: 2em 2em 1.6em;
  display: none;
  top: 4em;
  left: 45%;
  right: 55%;
  transform: rotate(-10deg);
}

.article-intro-div.is-black.is-home-4 {
  justify-content: center;
  align-items: center;
  width: 22em;
  padding: 2em 2em 1.5em;
  display: none;
  bottom: 1em;
  right: 4em;
  transform: rotate(20deg);
}

.article-intro-div.is-black.is-home-6 {
  justify-content: center;
  align-items: center;
  width: 22em;
  padding: 2em 2em 1.5em;
  display: none;
  bottom: 18em;
  left: -3em;
  transform: rotate(-10deg);
}

.article-intro-div.is-home-3 {
  justify-content: center;
  align-items: center;
  width: 22em;
  padding: 2em;
  display: flex;
  inset: 20em -4em auto auto;
  transform: rotate(-30deg);
}

.article-intro-div-text {
  color: var(--black);
  text-transform: uppercase;
  font-family: Mango Grotesque Vf, Arial, sans-serif;
  font-size: 3em;
  font-weight: 500;
  line-height: .8;
  text-decoration: none;
}

.article-intro-div-text.is-white {
  color: var(--white);
}

.int-intro-img-div {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/67e2e103ba624336e62cfd0a_interview%20intro-min.jpg");
  background-position: 50%;
  background-repeat: no-repeat;
  background-size: cover;
  width: 22.5em;
  height: 15em;
  position: absolute;
}

.int-intro-img-div.is-1 {
  top: 2.5em;
  left: 0;
  transform: rotate(-15deg);
}

.int-intro-img-div.is-2 {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/67e2e104fe0c2f72822b968d_interview%20intro-3-min.jpg");
  bottom: 0;
  left: 28em;
  transform: rotate(20deg);
}

.int-intro-img-div.is-3 {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/67e2e1049837f511ada86646_interview%20intro-2-min.jpg");
  top: -2.5em;
  right: 25em;
  transform: rotate(-30deg);
}

.int-intro-img-div.is-4 {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/67e2e1046490eb5371e28867_interview%20intro-1-min.jpg");
  bottom: 6em;
  right: -1.5em;
  transform: rotate(-10deg);
}

.int-intro-img-div.is-gallery-1 {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/681560b97aaf9053037c7f2b_Mask%20group-3-min%20(4).jpg");
  top: 0;
  left: 16em;
  transform: rotate(15deg);
}

.int-intro-img-div.is-gallery-3 {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/681560b9db3750d2f4dfdff4_Mask%20group-2-min%20(6).jpg");
  top: 10em;
  right: -3em;
  transform: rotate(-25deg);
}

.int-intro-img-div.is-gallery-2 {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/681560b843994c9b1ddb7b0b_Mask%20group-min%20(10).jpg");
  bottom: 6em;
  left: -2em;
  transform: rotate(-10deg);
}

.int-intro-img-div.is-gallery-4 {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/681560b92b549680ddaa6040_Mask%20group-1-min%20(10).jpg");
  bottom: -1em;
  right: 24em;
  transform: rotate(30deg);
}

.int-intro-img-div.is-ab-1 {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/685ccb7ec3ad7bda22872dc5_About%20Intro%20DJ-2-min.jpg");
  top: 2em;
  left: 4em;
  transform: rotate(-20deg);
}

.int-intro-img-div.is-ab-2 {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/685ccb7ecb8ca049ecdb9420_Video%20Img-min.jpg");
  bottom: -2em;
  right: 32em;
  transform: rotate(25deg);
}

.int-intro-img-div.is-ab-3 {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/685ccb7e7085fc436a6f0a87_About%20Intro%20DJ-min.jpg");
  top: 14em;
  right: -2em;
  transform: rotate(5deg);
}

.cta-text-container {
  z-index: 3;
  grid-column-gap: 1.5em;
  grid-row-gap: 1.5em;
  background-color: var(--brown);
  flex-flow: column;
  justify-content: center;
  align-items: center;
  height: 16em;
  display: flex;
  position: absolute;
}

.transing-out-box {
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  padding: 1em;
  display: flex;
  position: static;
}

.circle-cta-container {
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.div-block-11 {
  z-index: 4;
  grid-column-gap: .3em;
  grid-row-gap: .3em;
  flex-flow: row;
  justify-content: flex-start;
  align-items: center;
  display: flex;
}

.div-block-11.is-home {
  grid-column-gap: 0em;
  grid-row-gap: 0em;
  flex-flow: column;
}

.circle-cta-text {
  color: var(--white);
  -webkit-text-stroke-width: 1px;
  -webkit-text-stroke-color: var(--black);
  text-transform: uppercase;
  font-family: Mango Grotesque Vf, Arial, sans-serif;
  font-size: 6.25em;
  font-weight: 600;
  line-height: .5;
}

.circle-cta-text.is-italic {
  letter-spacing: -5px;
  font-family: Instrument Serif, sans-serif;
  font-size: 6em;
  font-style: italic;
  font-weight: 400;
}

.div-block-12 {
  grid-column-gap: 1.25em;
  grid-row-gap: 1.25em;
  flex-flow: column;
  justify-content: center;
  align-items: center;
  display: flex;
  position: absolute;
}

.btn-outer-cta {
  z-index: 5;
  border: 2px dashed var(--black);
  cursor: pointer;
  border-radius: 100px;
  justify-content: center;
  align-items: center;
  padding: .2em;
  text-decoration: none;
  display: flex;
  position: relative;
}

.btn-outer-cta.is-big {
  padding: .3em;
}

.section-100 {
  flex-flow: column;
  width: 100%;
  height: 105vh;
  display: flex;
  position: sticky;
  top: 0;
  overflow: hidden;
}

.interview-frame-wrapper {
  width: 100%;
  height: 100%;
  display: none;
  overflow: hidden;
}

.home-int-div {
  grid-column-gap: 3em;
  grid-row-gap: 3em;
  border: 2px dashed var(--black);
  background-color: var(--white);
  justify-content: space-between;
  width: 75em;
  height: 100%;
  padding: 1.5em 3em 1.5em 1.5em;
  display: flex;
}

.home-int-div.is-1 {
  grid-column-gap: 2em;
  grid-row-gap: 2em;
  flex-flow: column;
  justify-content: flex-start;
  align-items: stretch;
  width: 25em;
  height: auto;
  padding: 1em 1em 2em;
  text-decoration: none;
}

.home-int-div.is-2 {
  border-left: 2px dashed var(--black);
  border-right-width: 0;
}

.home-int-div.is-3 {
  border-left: 2px dashed var(--black);
  border-right-style: none;
  border-right-width: 0;
}

.home-int-div.is-4 {
  border-left: 2px dashed var(--black);
  border-right-style: none;
}

.home-int-img-wrapper {
  border-radius: 2px;
  width: 100%;
  height: 16em;
}

.int-set {
  z-index: 6;
  background-color: var(--white);
  height: 100%;
  position: relative;
}

.int-set.is-1 {
  z-index: 11;
  background-color: var(--transparent);
  width: 25em;
  height: auto;
  display: flex;
  position: absolute;
  inset: 3em auto auto 3em;
}

.int-set.is-2 {
  z-index: 11;
  width: 25em;
  height: auto;
  display: flex;
  position: absolute;
  inset: 40em 12em auto auto;
}

.int-set.is-3 {
  z-index: 12;
  width: 25em;
  height: auto;
  display: flex;
  position: absolute;
  top: 72em;
  left: 16em;
}

.int-set.is-4 {
  z-index: 11;
  width: 25em;
  height: auto;
  display: flex;
  position: absolute;
  inset: 114em 3em auto auto;
}

.int-set.is-5 {
  z-index: 7;
  background-color: var(--transparent);
  width: 50.5em;
}

.h3-iitalic {
  font-family: Instrument Serif, sans-serif;
  font-style: italic;
  font-weight: 400;
}

.d-50 {
  width: 50%;
}

.marquee-cont {
  justify-content: flex-start;
  align-items: center;
  width: 100%;
  height: 32em;
  display: flex;
  position: relative;
}

.marquee-cont.is-left {
  height: 28em;
  margin-top: 0;
}

.marquee-cont.is-right {
  justify-content: flex-end;
  align-items: center;
  overflow: hidden;
}

.marquee-cream {
  position: absolute;
  transform: translate(-4em, -24em)rotate(-13deg);
}

.marquee-div {
  grid-column-gap: 2em;
  grid-row-gap: 2em;
  justify-content: flex-start;
  align-items: center;
  padding-left: 2em;
  display: flex;
}

.marquee-div.is-cream {
  width: 126.4em;
}

.marquee-div.is-black {
  justify-content: flex-start;
  align-items: center;
  width: 105em;
}

.marquee-black {
  justify-content: flex-end;
  align-items: center;
  display: flex;
  position: absolute;
}

.footer-img-wrapper {
  display: none;
}

.mobile-menu-container {
  z-index: 1000;
  width: 100vw;
  height: 100vh;
  display: none;
  position: fixed;
  inset: 0% 0% auto;
}

.nav-block {
  z-index: 1;
  display: flex;
  position: absolute;
}

.whipe-cream-btm {
  background-color: var(--cream);
  width: 100%;
  height: 100%;
  position: absolute;
}

.whipe-cream-btm.is-intro {
  z-index: 2;
  inset: auto 0% 0%;
}

.n-link {
  grid-column-gap: .25em;
  grid-row-gap: .25em;
  justify-content: flex-start;
  align-items: flex-start;
  padding: .25em .125em;
  text-decoration: none;
  display: flex;
  overflow: hidden;
}

.body-5 {
  background-color: var(--white);
}

.int-cta-block {
  z-index: 8;
  border: 1px solid var(--black);
  background-color: var(--cream);
  width: 100%;
  height: 100%;
  padding-bottom: 0;
  padding-left: 3em;
  padding-right: 3em;
}

.text-white {
  color: var(--white);
  margin-bottom: .5em;
  font-weight: 500;
}

.banner-img {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/6787c53671a97b8708dd702a_dj-home-2-min.webp");
  background-position: 50%;
  background-repeat: no-repeat;
  background-size: cover;
  width: 100%;
  height: 100%;
}

.banner-img.is-interviews {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/6833f467207bd2515101790b_Int-banner%20-%20bw-min%20(2).jpg");
  display: none;
}

.banner-img.is-interviews.is-mobile {
  display: none;
}

.banner-img.is-about {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/6835371eef61cee0dff9bc3b_WhatsApp%20Image%202024-02-10%20at%2010.49%202-min.jpg");
}

.banner-img.is-about.is-top {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/6835372348a9c969c7829b4d_WhatsApp%20Image%202024-02-10%20at%2010.49%202-min.png");
  position: absolute;
  inset: 0%;
}



.d-9 {
  width: 9%;
  position: absolute;
  inset: 3em auto auto 3em;
}

.d-9.is-ab-top {
  width: 12%;
  top: 5em;
}

.rot-int-img-wrap {
  border: 1px solid var(--black);
  background-color: var(--white);
  width: 14.25em;
  height: 10.25em;
  padding: .25em;
  position: absolute;
}

.rot-int-img-wrap.is-1 {
  top: 1em;
}

.rot-int-img-wrap.is-2 {
  top: 13.375em;
  right: 6em;
}

.rot-int-img-wrap.is-3 {
  right: -2em;
}

.rot-int-img-wrap.is-4 {
  bottom: 13.375em;
  right: 6em;
}

.rot-int-img-wrap.is-5 {
  bottom: 0;
}

.rot-int-img-wrap.is-6 {
  bottom: 13.375em;
  left: 6em;
}

.rot-int-img-wrap.is-7 {
  left: -2em;
}

.rot-int-img-wrap.is-8 {
  top: 13.375em;
  left: 6em;
}

.whipe-hero {
  background-color: var(--black);
  width: 100%;
  height: 100%;
  position: absolute;
  inset: 0% auto 0% 0%;
}

.mobile-line {
  display: none;
}

.heading-11 {
  cursor: default;
}

.img-hide {
  border-radius: 100%;
  width: 20em;
  height: 20em;
  position: absolute;
  overflow: hidden;
}

.gallery-marquee-container {
  z-index: 10;
  border-radius: 100%;
  justify-content: center;
  align-items: center;
  width: 12.5em;
  height: 12.5em;
  display: flex;
  position: absolute;
}

.gallery-marquee-container.is-home {
  width: 72em;
  height: 105vh;
  position: sticky;
  top: 0%;
  overflow: visible;
}

.gallery-marq-wrap {
  background-color: var(--cream);
  border-radius: 100%;
  justify-content: flex-start;
  align-items: center;
  width: 90%;
  height: 90%;
  display: flex;
  overflow: hidden;
}

.gallery-marq-wrap.is-home {
  z-index: 1;
  border: 2px solid var(--black);
  color: var(--black);
  flex-flow: column;
  justify-content: center;
  align-items: center;
  width: 25.5em;
  height: 25.5em;
  text-decoration: none;
  display: flex;
  position: relative;
}

.text-block-9 {
  text-transform: uppercase;
  font-size: 2.5em;
  font-style: italic;
  font-weight: 400;
}

.article-fall-wrapper {
  width: 100%;
  height: 100%;
  position: absolute;
}

.article-fall-wrapper.is-home {
  justify-content: center;
  align-items: center;
  display: flex;
}

.article-fall-wrapper.is-fall {
  display: flex;
}

.article-fall-container {
  width: 100%;
  height: 80vh;
  display: flex;
  position: relative;
  overflow: hidden;
}

.article-fall-container.is-home {
  height: 90vh;
}

.article-fall-container.is-ab {
  z-index: 2;
  display: flex;
  position: absolute;
  inset: auto 0% 0%;
}

.article-fall-container.is-logos {
  height: 100vh;
  margin-top: -24em;
}

.article-fall-container.is-home-articles {
  flex-flow: column;
  height: 80vh;
  display: flex;
}

.article-fall-card {
  z-index: 1;
  border: 2px solid var(--black);
  width: 25em;
  padding: 2em;
  position: absolute;
  inset: auto auto 0% 0%;
}

.article-fall-card.is-3 {
  bottom: 1.5em;
  left: 37em;
  transform: rotate(-6deg);
}

.article-fall-card.is-3.is-fall, .article-fall-card.is-3.is-def {
  background-color: var(--white);
}

.article-fall-card.is-1 {
  background-color: var(--black);
  bottom: .5em;
  left: 2em;
  transform: rotate(2deg);
}

.article-fall-card.is-2 {
  background-color: var(--cream);
  bottom: 9em;
  left: 23.5em;
  transform: rotate(-3deg);
}

.article-fall-card.is-4 {
  background-color: var(--black);
  bottom: 12em;
  left: auto;
  right: 26em;
  transform: rotate(12deg);
}

.article-fall-card.is-5 {
  background-color: var(--cream);
  inset: auto .5em 10em auto;
  transform: rotate(-15deg);
}

.fall-card-text {
  text-transform: uppercase;
  font-family: Mango Grotesque Vf, Arial, sans-serif;
  font-size: 2.25em;
  font-weight: 500;
  line-height: .9;
}

.fall-card-text.text-white {
  margin-bottom: 0;
}

.logo-card {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/682abe97ef89f21b5ccdd71c_fall%20logos-1-min.jpg");
  background-position: 50%;
  background-repeat: no-repeat;
  background-size: cover;
  width: 15em;
  height: 5.5em;
  position: absolute;
  inset: auto 0% 4.5em auto;
  transform: rotate(-45deg);
}

.logo-card.is-1 {
  z-index: 1;
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/682abe97cf53b77008717207_fall%20logos-min.jpg");
  background-size: contain;
  width: 75%;
  height: 75%;
  position: relative;
  bottom: auto;
  right: auto;
  transform: none;
}

.logo-card.is-2 {
  z-index: 1;
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/682abe97809ccb389fbdedb9_fall%20logos-2-min.jpg");
  background-size: contain;
  width: 80%;
  height: 80%;
  position: relative;
  bottom: auto;
  right: auto;
  transform: none;
}

.logo-card.is-3 {
  bottom: 3.5em;
  transform: rotate(-32deg);
}

.logo-card.is-h-1 {
  z-index: 1;
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/682d3ae7feaad751165b26d0_Group-2.svg");
  background-position: 50%;
  background-size: contain;
  width: 100%;
  height: 100%;
  position: relative;
  bottom: auto;
  right: auto;
  transform: none;
}

.logo-card.is-h-2 {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/682d3ae7b07b124b85626b5d_Vector.svg");
  background-position: 50%;
  background-size: contain;
  width: 100%;
  height: 100%;
  position: static;
  transform: none;
}

.logo-card.is-h-3 {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/682d3ae76fe3087d991e0ba6_Group%2032.svg");
  background-position: 50%;
  background-size: contain;
  width: 100%;
  height: 100%;
  position: static;
  transform: none;
}

.logo-card.is-h-4 {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/682d3ae7c1819e423b7a6f05_layer1.svg");
  background-position: 50%;
  background-size: contain;
  width: 100%;
  height: 100%;
  position: static;
  transform: none;
}

.logo-card.is-h-5 {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/682d3ae7d2c70a7b07aca389_Group%2030.svg");
  background-position: 50%;
  background-size: contain;
  width: 100%;
  height: 100%;
  position: static;
  transform: none;
}

.logo-card.is-h-6 {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/682d3ae72252eb8af61c7d6d_Group.svg");
  background-position: 50%;
  background-size: contain;
  width: 100%;
  height: 100%;
  position: static;
  transform: none;
}

.logo-card.is-h-7 {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/682d3ae7ec43cafff685b01a_Group-1.svg");
  background-position: 50%;
  background-size: contain;
  width: 100%;
  height: 100%;
  position: static;
  transform: none;
}

.logo-card.is-4 {
  z-index: 1;
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/684c271fc4e0e170f8c444bf_TG%20logo.svg");
  background-size: contain;
  width: 75%;
  height: 75%;
  position: relative;
  bottom: auto;
  right: auto;
  transform: none;
}

.logo-card.is-5 {
  z-index: 1;
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/684c28f9dfcb11a1f84e4042_dailyft.png");
  background-size: contain;
  width: 100%;
  height: 100%;
  position: relative;
  bottom: auto;
  right: auto;
  transform: none;
}

.logo-card.is-6 {
  z-index: 1;
  width: 100%;
  height: 100%;
  position: relative;
  bottom: auto;
  right: auto;
  transform: none;
}

.logo-card.is-7 {
  z-index: 1;
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/684c2bd0dd3e5055e66060d6_CT%20logo.svg");
  background-size: contain;
  width: 80%;
  height: 80%;
  position: relative;
  bottom: auto;
  right: auto;
  transform: none;
}

.logo-card.is-8 {
  z-index: 1;
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/684c2d5a18215686a41c72c3_GV%20logo.svg");
  background-size: contain;
  width: 80%;
  height: 80%;
  position: relative;
  bottom: auto;
  right: auto;
  transform: none;
}

.logo-card.is-9 {
  z-index: 1;
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/684c304690097f7c76e04466_stlogo.jpg");
  background-size: contain;
  width: 80%;
  height: 60%;
  position: relative;
  bottom: auto;
  right: auto;
  transform: none;
}

.fall-logo-bg {
  background-color: var(--white);
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  display: flex;
  position: absolute;
  inset: 0%;
}

.fall-logo-bg.is-red {
  background-color: #ee1c25;
}

.fall-logo-bg.is-yellow {
  background-color: #ffae00;
}

.fall-logo-bg.is-cream {
  background-color: var(--cream);
}

.logo-card-wrap {
  border: 2px solid var(--black);
  justify-content: center;
  align-items: center;
  width: 22em;
  height: 6em;
  display: flex;
  position: absolute;
  inset: auto 15.5em 1.5em auto;
  overflow: hidden;
  transform: rotate(8deg);
}

.logo-card-wrap.is-2 {
  width: 20em;
  inset: auto auto 11em .5em;
  transform: rotate(15deg);
}

.logo-card-wrap.is-h-1 {
  background-color: #0c2950;
  width: 18em;
  height: 6em;
  padding: 1.5em 2em;
  bottom: 1.3em;
  left: 20.05em;
  transform: rotate(-9deg);
}

.logo-card-wrap.is-h-1.is-def, .logo-card-wrap.is-h-1.is-fall {
  width: 14em;
  height: 5em;
  padding-top: 1.3em;
  padding-bottom: 1.3em;
  left: 43.05em;
}

.logo-card-wrap.is-h-2 {
  background-color: var(--black);
  width: 18em;
  height: 6em;
  padding: 1.5em 2em;
  bottom: .5em;
  left: 38.4em;
  right: auto;
  transform: rotate(-2.5deg);
}

.logo-card-wrap.is-h-2.is-def, .logo-card-wrap.is-h-2.is-fall {
  width: 14em;
  height: 5em;
  left: 57.4em;
}

.logo-card-wrap.is-h-3 {
  background-color: #dba200;
  width: 18em;
  height: 6em;
  padding: 1em 2em 1.2em;
  bottom: .5em;
  right: 25.45em;
  transform: rotate(3.8deg);
}

.logo-card-wrap.is-h-3.is-def, .logo-card-wrap.is-h-3.is-fall {
  width: 16em;
  height: 5em;
  right: 12.45em;
}

.logo-card-wrap.is-h-4 {
  background-color: var(--white);
  width: 17em;
  height: 6em;
  padding: 2em;
  bottom: 8.5em;
  left: 30em;
  right: auto;
  transform: rotate(15deg);
}

.logo-card-wrap.is-h-4.is-def, .logo-card-wrap.is-h-4.is-fall {
  width: 15em;
  height: 5em;
  padding-top: 1.6em;
  padding-bottom: 1.6em;
  bottom: 7.4em;
  left: 49em;
}

.logo-card-wrap.is-h-5 {
  background-color: var(--cream);
  width: 17em;
  height: 6em;
  padding: 1.2em 2em;
  bottom: 8em;
  right: 35.3em;
  transform: rotate(-10deg);
}

.logo-card-wrap.is-h-5.is-def {
  width: 13em;
  height: 5em;
  bottom: 7em;
  right: 22.3em;
}

.logo-card-wrap.is-h-5.is-fall {
  width: 13em;
  height: 5em;
  padding-top: 1.1em;
  padding-bottom: 1.1em;
  bottom: 7em;
  right: 22.3em;
}

.logo-card-wrap.is-h-6 {
  background-color: var(--white);
  width: 9em;
  height: 6em;
  padding: 1em 2em;
  bottom: 8.4em;
  right: 25.65em;
  transform: rotate(45deg);
}

.logo-card-wrap.is-h-6.is-def, .logo-card-wrap.is-h-6.is-fall {
  width: 7em;
  height: 5em;
  bottom: 7em;
  right: 14.6em;
}

.logo-card-wrap.is-h-7 {
  background-color: var(--black);
  width: 18em;
  height: 6em;
  padding: 1.125em 2em;
  bottom: 14.4em;
  left: 35.8em;
  right: auto;
  transform: rotate(6deg);
}

.logo-card-wrap.is-h-7.is-def, .logo-card-wrap.is-h-7.is-fall {
  width: 15em;
  height: 5em;
  bottom: 12.35em;
  left: 54em;
}

.logo-card-wrap.is-int-1 {
  width: 20em;
  height: 5.5em;
  inset: auto auto 1.5em .35em;
  transform: rotate(9deg);
}

.logo-card-wrap.is-int-2 {
  width: 27em;
  height: 5.5em;
  bottom: 0;
  left: 22.5em;
  transform: rotate(0);
}

.logo-card-wrap.is-int-3 {
  width: 14.5em;
  height: 5.5em;
  bottom: 2.5em;
  left: 56em;
  transform: rotate(-20deg);
}

.logo-card-wrap.is-int-7 {
  width: 18em;
  height: 5em;
  bottom: 8.4em;
  right: 14em;
  transform: rotate(15deg);
}

.logo-card-wrap.is-int-5 {
  background-color: var(--black);
  width: 17em;
  height: 5.5em;
  bottom: 9.2em;
  left: 9.45em;
  transform: rotate(-17.5deg);
}

.logo-card-wrap.is-int-4 {
  width: 25em;
  height: 5.5em;
  inset: auto 4em .8em auto;
  transform: rotate(4deg);
}

.logo-card-wrap.is-int-6 {
  width: 23em;
  height: 5.5em;
  bottom: 6.2em;
  left: 24em;
  transform: rotate(-4deg);
}

.logo-card-wrap.is-int-8 {
  background-color: #015093;
  width: 14em;
  height: 6.25em;
  bottom: 9.3em;
  right: 39.5em;
  transform: rotate(28deg);
}

.int-img-colored {
  background-image: url("https://d3e54v103j8qbb.cloudfront.net/img/background-image.svg");
  background-position: 50%;
  background-repeat: no-repeat;
  background-size: cover;
  width: 100%;
  min-width: 110%;
  height: 110%;
  position: relative;
}

.ft-image {
  background-image: url("https://d3e54v103j8qbb.cloudfront.net/img/background-image.svg");
  background-position: 50% 0;
  background-repeat: no-repeat;
  background-size: cover;
  width: 100%;
  height: 100%;
}

.btn-bg-fill {
  background-color: var(--white);
  width: 110%;
  height: 100%;
  position: absolute;
}

.page-no-wrap {
  border: 1px dashed var(--black);
  background-color: var(--white);
  border-radius: 100px;
  justify-content: center;
  align-items: center;
  height: 2em;
  padding: .6em 1em .45em;
  display: flex;
}

.page-no-wrap.is-tip {
  background-color: var(--black);
  white-space: nowrap;
  padding-top: .6em;
  padding-left: 2em;
  padding-right: 2em;
  position: absolute;
  left: auto;
  right: auto;
}

.cursor-parent {
  z-index: 1005;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  display: flex;
  position: fixed;
  inset: 0% 0% auto;
}

.cursor-div {
  background-color: var(--black);
  border-radius: 100%;
  width: .6em;
  height: .6em;
}

.img-circle {
  background-color: var(--cream);
  border-radius: 100%;
  width: 1.5em;
  height: 1.5em;
}

.btn-outer-cta-external {
  z-index: 5;
  border: 2px dashed var(--black);
  border-radius: 100px;
  justify-content: center;
  align-items: center;
  padding: .2em;
  text-decoration: none;
  display: flex;
  position: absolute;
  inset: auto 3em 6em auto;
}

.btn-outer-cta-external.not-ab {
  position: static;
}

.int-date-container {
  cursor: default;
  justify-content: center;
  align-items: flex-end;
  width: 100%;
  height: 100%;
  margin-bottom: -10em;
  padding-right: 8em;
  display: flex;
  position: absolute;
  inset: auto 0% 0%;
}

.int-date {
  opacity: 1;
  color: #ded9d0;
  letter-spacing: -10px;
  font-size: 60em;
  font-style: italic;
  font-weight: 400;
  line-height: .7;
}

.hov-img-wrapper {
  z-index: 1;
  justify-content: center;
  align-items: center;
  width: 54em;
  height: 100%;
  display: flex;
  position: absolute;
  bottom: 0%;
  left: 23em;
  right: 23em;
}

.inner-set-gal {
  z-index: 3;
  border-radius: 100%;
  justify-content: center;
  align-items: center;
  width: 48em;
  height: 48em;
  display: flex;
  position: absolute;
}

.div-block-13 {
  z-index: 1;
  background-color: var(--cream);
  color: var(--light-cream);
  letter-spacing: -10px;
  text-transform: uppercase;
  justify-content: center;
  align-items: center;
  width: 100%;
  margin-top: -6em;
  font-style: italic;
  line-height: .7;
  display: flex;
  position: relative;
  overflow: hidden;
}

.news-marquee-text {
  color: var(--light-cream);
  letter-spacing: -.05em;
  text-transform: uppercase;
  font-size: 16em;
  font-style: italic;
  line-height: .75;
}

.news-marquee-text.text-black {
  color: var(--black);
}

.gal-images-wrapper {
  width: 100%;
  height: 100%;
  position: absolute;
}

.gal-div {
  width: 50%;
}

.int-intro-img-color {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/68333cc5b37e3f9a44f3bdf8_Int-Intro%20Img%20colored-3-min.jpg");
  background-position: 50%;
  background-repeat: no-repeat;
  background-size: cover;
  width: 100%;
  height: 100%;
  position: absolute;
}

.int-intro-img-color.is-1 {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/68333cc59c797863e4997ca6_Int-Intro%20Img%20colored-min.jpg");
}

.int-intro-img-color.is-2 {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/68333cc58c5bfdeb491f4748_Int-Intro%20Img%20colored-2-min.jpg");
}

.int-intro-img-color.is-3 {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/68333cc6c0b29cfacf035915_Int-Intro%20Img%20colored-1-min.jpg");
}

.int-intro-img-color.is-ab-2-color {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/685ccb7e982f7af0f9a21f20_About%20Intro%20DJ-4-min.jpg");
}

.int-intro-img-color.is-ab-3-color {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/685ccb7ee9bc30b23e204fd2_About%20Intro%20DJ-1-min.jpg");
}

.int-intro-img-color.is-ab-1-color {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/685ccb7ea28e6271c4936ba3_About%20Intro%20DJ-3-min.jpg");
}

.gal-intro-color-img {
  background-image: url("https://d3e54v103j8qbb.cloudfront.net/img/background-image.svg");
  background-position: 50%;
  background-repeat: no-repeat;
  background-size: cover;
  width: 100%;
  height: 100%;
  position: absolute;
}

.gal-intro-color-img.is-4 {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/6833c4e3df5f1f8dd4a9ab26_Gallery-intro-color-img-min.jpg");
}

.gal-intro-color-img.is-2 {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/6833c6a576c3b033ca63f214_gal-intro-color-1-min.jpg");
}

.gal-intro-color-img.is-3 {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/6833c6a56efccb98fee33198_gal-intro-color-min.jpg");
}

.gal-intro-color-img.is-1 {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/6833c4e19fe12a6837a62d82_Gallery-intro-color-img-1-min.jpg");
}



.empty-section {
  width: 100%;
  height: 180vh;
}

.empty-section.is-about {
  height: 100vh;
}

.banner-marquee {
  z-index: 2;
  width: 100%;
  display: flex;
  position: absolute;
  overflow: hidden;
}

.banner-marquee-track {
  justify-content: flex-start;
  align-items: center;
  width: 396em;
  display: flex;
}

.banner-marquee-col {
  width: 198em;
  padding-top: .8em;
  display: flex;
}

.banner-marquee-text {
  color: var(--white);
  letter-spacing: -10px;
  text-transform: uppercase;
  white-space: nowrap;
  font-size: 15em;
  font-style: italic;
  line-height: .7;
}

.body-6 {
  background-color: var(--black);
}

.div-block-14 {
  width: auto;
  height: 100%;
}

.banner-sm-img-wrap {
  border: 2px dashed var(--white);
  width: 16em;
  height: 20em;
  padding: .4em;
  position: absolute;
  left: 3em;
}

.banner-sm-img-wrap.is-2 {
  z-index: 1;
  width: 20em;
  height: 25em;
  bottom: 6em;
  left: 30em;
}

.banner-sm-img-wrap.is-3 {
  z-index: 4;
  top: 12em;
  left: auto;
  right: 3em;
}

.banner-sm-img-wrap.is-1 {
  z-index: 3;
  top: 6em;
}

.banner-sm-img {
  background-image: url("https://d3e54v103j8qbb.cloudfront.net/img/background-image.svg");
  background-position: 50%;
  background-repeat: no-repeat;
  background-size: cover;
  width: 100%;
  height: 100%;
}

.banner-sm-img.is-2 {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/68340273c7474c02f69967d6_banner-sm-images-2-min.jpg");
}

.banner-sm-img.is-3 {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/683402736efccb98fe053bce_banner-sm-images-min.jpg");
}

.banner-sm-img.is-1 {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/683402734a658ff4eb2c75ab_banner-sm-images-1-min.jpg");
}

.top-line-wrap {
  justify-content: flex-start;
  align-items: center;
  width: 100%;
  padding-left: 3em;
  padding-right: 3em;
  display: flex;
  position: absolute;
  inset: 0% 0% auto;
}

.btm-line {
  border-bottom: 2px dashed var(--black);
  width: 100%;
  height: 0;
}

.grid-trans {
  border-bottom: 2px dashed #ded9d066;
  border-right: 2px dashed #ded9d066;
  width: 100%;
  height: 100%;
}



.gallery-marquee-bdr {
  border: 2px dashed var(--white);
  border-radius: 100%;
  width: 100%;
  height: 100%;
  position: absolute;
  inset: 0%;
}

.gallery-marquee-bdr.is-home {
  border-color: var(--brown);
  width: 28em;
  height: 28em;
  inset: auto;
}

.book-intro-color-img {
  background-image: url("https://d3e54v103j8qbb.cloudfront.net/img/background-image.svg");
  background-position: 50%;
  background-repeat: no-repeat;
  background-size: cover;
  width: 100%;
  height: 100%;
  position: absolute;
}

.book-intro-color-img.is-1 {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/6835acf3546cef6bf5e586e1_book%20intro%20color%20img-2-min.jpg");
}

.book-intro-color-img.is-2 {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/6835acf38ea21af6588965fb_book%20intro%20color%20img-1-min.jpg");
}

.book-intro-color-img.is-3 {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/6835acf370b24fb2b59a0d9a_book%20intro%20color%20img-min.jpg");
}

.book-intro-color-img.is-4 {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/6835b00482082f5710da0c03_Book%20intro%20images-1-min.jpg");
}

.hover-image-wrapper {
  z-index: 1;
  width: 32em;
  height: 100%;
  position: absolute;
  inset: auto 24em 0% auto;
}

.hov-book {
  background-image: url("https://d3e54v103j8qbb.cloudfront.net/img/background-image.svg");
  background-position: 50%;
  background-repeat: no-repeat;
  background-size: cover;
  width: 15em;
  height: 21em;
}

.hov-book.is-1 {
  z-index: 3;
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/6835b00482082f5710da0c03_Book%20intro%20images-1-min.jpg");
  position: absolute;
  bottom: -8em;
  right: 2em;
  transform: rotate(20deg);
}

.hov-book.is-1.is-int {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/683402734a658ff4eb2c75ab_banner-sm-images-1-min.jpg");
}

.hov-book.is-1.is-art {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/683d37d580a05ccf436878ce_Nav%20img%20-%2013-min.jpg");
}

.hov-book.is-1.is-gal {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/6833c4e3df5f1f8dd4a9ab26_Gallery-intro-color-img-min.jpg");
}

.hov-book.is-1.is-about {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/684b9dfa851b0e299445287c_About%20intro-2-min.jpg");
}

.hov-book.is-2 {
  z-index: 2;
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/6835acf3546cef6bf5e586e1_book%20intro%20color%20img-2-min.jpg");
  position: absolute;
  bottom: -3em;
  right: 12em;
  transform: rotate(10deg);
}

.hov-book.is-2.is-int {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/68340273c7474c02f69967d6_banner-sm-images-2-min.jpg");
}

.hov-book.is-2.is-art {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/683d35f3d11e67308fde77c2_3dd4dda2a7c44b8788f1916c97f8622e_Nav%20img%20-%209.jpg");
}

.hov-book.is-2.is-gal {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/6833c6a56efccb98fee33198_gal-intro-color-min.jpg");
}

.hov-book.is-2.is-about {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/684b9dfa6d0752466bb341ad_About%20intro-1-min.jpg");
}

.hov-book.is-3 {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/6835acf38ea21af6588965fb_book%20intro%20color%20img-1-min.jpg");
  position: absolute;
  top: 5.5em;
  right: 19em;
  transform: rotate(-20deg);
}

.hov-book.is-3.is-int {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/683402736efccb98fe053bce_banner-sm-images-min.jpg");
}

.hov-book.is-3.is-art {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/683d35f3e9fd0ed0efbfbfef_8cd50517f867980a747055863ca42121_Nav%20img%20-%2010.svg");
}

.hov-book.is-3.is-gal {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/6833c6a576c3b033ca63f214_gal-intro-color-1-min.jpg");
}

.hov-book.is-3.is-about {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/684b9dfa2ef4cb0896868bbf_About%20intro-min.jpg");
}

.footer-bg {
  background-color: var(--black);
  width: 100%;
  height: 100%;
  display: flex;
  position: absolute;
  bottom: -.5em;
  left: 0%;
}

.news-marquee-text-wrap {
  width: 724em;
  padding-top: 1em;
  padding-right: 2em;
}

.articles-marquee {
  white-space: nowrap;
  justify-content: flex-start;
  align-items: center;
  width: 100%;
  display: flex;
  overflow: hidden;
}

.news-marquee-track {
  justify-content: flex-start;
  align-items: center;
  width: 1508em;
  display: flex;
}

.d-58 {
  width: 58%;
}

.d-59 {
  width: 59%;
}

.publication-text {
  opacity: 0;
  color: var(--brown);
}

.filter-panel {
  justify-content: space-between;
  padding: 1em 3em;
  display: flex;
}

.form-field {
  grid-column-gap: .5em;
  grid-row-gap: .5em;
  flex-flow: row;
  justify-content: flex-start;
  align-items: center;
  width: 50%;
  display: flex;
}

.form-field.is-search {
  width: 100%;
}

.select-field {
  border: 2px solid var(--black);
  background-color: var(--white);
  color: var(--grey);
  text-transform: uppercase;
  border-radius: 100px;
  height: 2.25em;
  margin-bottom: 0;
  padding: .15em 1em 0 .4em;
  font-family: Mango Grotesque Vf, Arial, sans-serif;
  font-size: 1.5em;
  line-height: .6;
}

.form {
  justify-content: space-between;
  align-items: center;
  width: 100%;
  display: flex;
}

.utility-page-wrap {
  justify-content: center;
  align-items: center;
  width: 100vw;
  max-width: 100%;
  height: 100vh;
  max-height: 100%;
  display: flex;
}

.utility-page-content {
  text-align: center;
  flex-direction: column;
  width: 260px;
  display: flex;
}

.utility-page-form {
  flex-direction: column;
  align-items: stretch;
  display: flex;
}

.form-block-2 {
  width: 100%;
  margin-bottom: 0;
}

.search {
  grid-column-gap: .5em;
  grid-row-gap: .5em;
  justify-content: flex-start;
  align-items: center;
  width: 50%;
  margin-bottom: 0;
  display: flex;
}

.search-button {
  background-color: var(--transparent);
}

.search-input {
  margin-bottom: 0;
}

.field-label {
  white-space: nowrap;
  margin-bottom: 0;
}

.text-field-2 {
  margin-bottom: 0;
}

.filter-inner {
  grid-column-gap: .25em;
  grid-row-gap: .25em;
  justify-content: flex-start;
  align-items: center;
  width: 40%;
  display: flex;
}

.btn-filter {
  border: 2px dashed var(--black);
  border-radius: 100px;
  justify-content: center;
  align-items: center;
  padding: .2em;
  text-decoration: none;
  display: flex;
}

.next-btn {
  z-index: 5;
  border: 2px dashed var(--black);
  background-color: var(--white);
  border-radius: 100px;
  justify-content: space-between;
  align-items: center;
  width: 8em;
  margin-left: 0;
  margin-right: 0;
  padding: .2em;
  font-size: 1vw;
  line-height: .6;
  text-decoration: none;
  display: flex;
  position: relative;
}

.next-btn.is-prev {
  padding-left: .75em;
  padding-right: 1em;
}

.pag-btn-div {
  padding-top: .15em;
}

.icon {
  margin-left: 0;
}

.article-pagination {
  background-color: var(--white);
  flex-flow: row;
  justify-content: center;
  align-items: flex-start;
  margin-top: 0;
  padding: 2.5em 3em 6.5em;
  display: flex;
}

.article-pagination.is-hidden {
  display: none;
}

.page-count-2 {
  white-space: nowrap;
  width: auto;
  margin-top: 0;
  font-size: 2em;
  font-style: italic;
}

.btn-inner-split {
  z-index: 3;
  justify-content: space-between;
  width: 100%;
  display: flex;
  position: relative;
}

.filter-panel--out {
  z-index: 10;
  width: 100%;
  padding: 0 0 1em;
  display: block;
  position: fixed;
  inset: 0% 0% auto;
}

.filter-panel--out.is-hidden, .filter-panel--out.is-btm {
  display: none;
}

.page-btn {
  border: 2px solid var(--black);
  background-color: var(--white);
  color: var(--black);
  border-radius: 100%;
  justify-content: center;
  align-items: center;
  width: 2.5em;
  height: 2.5em;
  font-size: 14px;
  font-style: italic;
  text-decoration: none;
  display: flex;
}

.page-btn:lang(en) {
  background-color: var(--black);
  color: var(--white);
  font-size: 1.25em;
}

.all-btn-wrapper {
  justify-content: center;
  align-items: center;
  display: flex;
}

.page-btn-collection {
  grid-column-gap: 0em;
  grid-row-gap: 0em;
  justify-content: flex-start;
  align-items: center;
  display: flex;
}

.filter-txt-wrap {
  padding-left: 1.5em;
  padding-right: 1em;
}

.page-count-wrap {
  justify-content: flex-start;
  align-items: center;
  width: 10em;
  display: flex;
}

.click-gal-wrapper {
  z-index: 2;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  display: flex;
  position: absolute;
}

.click-gal-img {
  border: 2px solid var(--white);
  background-image: url("https://d3e54v103j8qbb.cloudfront.net/img/background-image.svg");
  background-position: 50%;
  background-repeat: no-repeat;
  background-size: cover;
  width: 40em;
  height: 24em;
  position: absolute;
}

.click-gal-img.is-1 {
  z-index: 7;
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/6842bf86d798f801dff3f591_Mask%20group-3-min.webp");
  inset: auto auto -6em -8em;
  transform: rotate(15deg);
}

.click-gal-img.is-2 {
  z-index: 8;
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/6842bf88bf88e4ed3f09a6a3_Mask%20group-4-min.webp");
  bottom: -8.5em;
  transform: rotate(-15deg);
}

.click-gal-img.is-3 {
  z-index: 6;
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/6842bf8997ad107697d910c0_gallery-2-min.webp");
  inset: auto -4em -7.5em auto;
  transform: rotate(10deg);
}

.click-gal-img.is-4 {
  z-index: 3;
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/6842bf8b10f6187e1be90d1b_Mask%20group-1-min.webp");
  left: -8em;
  transform: rotate(-18deg);
}

.click-gal-img.is-5 {
  z-index: 4;
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/6842bf852759b835fafb18a3_Mask%20group-2-min.webp");
  transform: rotate(16deg);
}

.click-gal-img.is-6 {
  z-index: 5;
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/6842bf86d79f2878509ab797_gallery-1-min.webp");
  right: -8em;
  transform: rotate(-7.5deg);
}

.click-gal-img.is-7 {
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/6842bf8600531e6177c5cd24_Mask%20group-min.webp");
  inset: -4em auto auto -4em;
  transform: rotate(8deg);
}

.click-gal-img.is-8 {
  z-index: 1;
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/6842bf88654ca02af3c7d288_gallery-min.webp");
  top: -6em;
  transform: rotate(-4.5deg);
}

.click-gal-img.is-9 {
  z-index: 2;
  background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/6842bf897b8a892722e1bfca_Mask%20group-5-min.webp");
  inset: -8em -8em auto auto;
  transform: rotate(15deg);
}

.collection-list-wrapper-5 {
  margin-top: 4em;
}

.home-article-text-wrap {
  padding-top: .75em;
  padding-right: 5em;
}

.home-article-text-wrap.is-1 {
  width: 150.5em;
}

.home-article-text-wrap.is-2 {
  white-space: nowrap;
  width: 146.5em;
}

.home-article-text-wrap.is-2.is-articles {
  width: 135em;
}

.home-article-text-wrap.is-3 {
  white-space: nowrap;
  width: 188em;
  position: relative;
}

.home-article-text-wrap.is-4 {
  width: 166em;
}

.home-article-track.is-1 {
  white-space: nowrap;
  justify-content: flex-start;
  align-items: center;
  width: 301em;
  display: flex;
}

.home-article-track.is-2 {
  justify-content: flex-start;
  align-items: center;
  width: 293em;
  display: flex;
}

.home-article-track.is-2.is-articles {
  width: 270em;
}

.home-article-track.is-3 {
  justify-content: flex-start;
  align-items: center;
  width: 376em;
  margin-bottom: -.5em;
  display: flex;
}

.home-article-track.is-4 {
  justify-content: flex-end;
  align-items: center;
  width: 376em;
  margin-bottom: -.5em;
  display: flex;
}

.home-article-track.is-5 {
  justify-content: flex-end;
  align-items: center;
  width: 332em;
  display: flex;
}

.home-article-marquee-wrap {
  width: 100%;
  position: relative;
  overflow: hidden;
}

.home-article-marquee-wrap.is-left {
  justify-content: flex-start;
  align-items: center;
  display: flex;
}

.home-article-marquee-wrap.is-right {
  white-space: nowrap;
  justify-content: flex-end;
  align-items: center;
  display: flex;
}

.home-article-marquee-section {
  flex-flow: column;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: auto;
  display: flex;
  position: relative;
  overflow: hidden;
}

.ab-btn-wrapper {
  z-index: 2;
  width: auto;
  position: absolute;
}

.int-card-wrapper {
  justify-content: center;
  align-items: flex-start;
  width: 100%;
  height: 500vh;
  display: flex;
  position: relative;
}

.div-block-15 {
  justify-content: center;
  align-items: center;
  width: 100%;
  display: flex;
  overflow: hidden;
}

.marquee-move-container {
  justify-content: flex-start;
  align-items: center;
  width: 100%;
  display: flex;
}

.marquee-move-container.is-right {
  justify-content: flex-end;
  align-items: center;
  display: flex;
}

.btn-bg-white {
  background-color: var(--white);
  width: 110%;
  height: 100%;
  position: absolute;
}

.home-intro-marquee-box {
  grid-column-gap: .5em;
  grid-row-gap: .5em;
  flex-flow: column;
  justify-content: center;
  align-items: center;
  width: 120em;
  display: flex;
}

.marquee-flex {
  grid-column-gap: .5em;
  grid-row-gap: .5em;
  flex-flow: column;
  justify-content: center;
  align-items: flex-start;
  width: 100%;
  display: flex;
}

.marquee-flex.is-main, .marquee-flex.is-hidden {
  justify-content: center;
  align-items: center;
  position: relative;
  overflow: hidden;
}

.home-intro-marquee {
  justify-content: flex-start;
  align-items: center;
  width: 425.6em;
  display: flex;
}

.home-intro-marquee.is-2 {
  justify-content: flex-end;
  align-items: center;
  width: 323.6em;
}

.home-intro-m-text {
  color: var(--black);
  text-transform: uppercase;
  font-family: Mango Grotesque Vf, Arial, sans-serif;
  font-size: 4em;
  font-weight: 600;
  line-height: .6;
}

.home-intro-m-text.is-grey {
  color: var(--another-grey);
}

.home-intro-m-text-wrap {
  padding-top: .6em;
}

.home-intro-m-set {
  grid-column-gap: 3em;
  grid-row-gap: 3em;
  justify-content: flex-start;
  align-items: center;
  width: 106.4em;
  padding-left: 3em;
  display: flex;
}

.home-intro-m-set.is-grey {
  width: 80.9em;
}

.page-intro-main-box {
  flex-flow: column;
  justify-content: flex-start;
  align-items: center;
  display: flex;
  position: absolute;
}

.page-intro-main-box.is-mobile {
  display: none;
}

.m-dash {
  background-color: var(--black);
  width: 1.5em;
  height: 6px;
}

.m-dash.bg-grey {
  background-color: var(--another-grey);
}

.intro-hide-box {
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  display: flex;
  position: relative;
  overflow: hidden;
}

.marquee-flex-nm {
  grid-column-gap: .5em;
  grid-row-gap: .5em;
  flex-flow: column;
  justify-content: center;
  align-items: flex-start;
  display: flex;
  position: relative;
}

.intro-marquee-container {
  justify-content: flex-start;
  align-items: center;
  width: 100%;
  display: flex;
}

.intro-marquee-container.is-right {
  justify-content: flex-end;
  align-items: center;
}

.whipe-cream-intro {
  z-index: 1;
  background-color: var(--cream);
  width: 100%;
  height: 100%;
  position: absolute;
  inset: 0% 0% auto;
}

.whipe-cream-intro.is-btm {
  inset: auto 0% 0%;
}

.whipe-cream-intro.is-panel-top {
  height: 50%;
}

.whipe-cream-intro.is-panel-btm {
  height: 50%;
  inset: auto 0% 0%;
}

.page-tip-wrapper {
  justify-content: center;
  align-items: flex-end;
  width: 100%;
  height: 100%;
  display: flex;
  position: absolute;
  inset: 0% 0% auto;
}

.np-link-wrap {
  grid-column-gap: .5em;
  grid-row-gap: .5em;
  justify-content: flex-start;
  align-items: center;
  text-decoration: none;
  display: flex;
}

.text-span-17 {
  text-decoration: underline;
}

.div-block-16 {
  border-top: 2px dashed var(--black);
  position: relative;
}

.int-card-set {
  justify-content: center;
  align-items: flex-start;
  width: 100%;
  height: 500vh;
  display: flex;
  position: absolute;
  overflow: hidden;
}

.image-carousel-mobile, .image-carousel-wrapper {
  display: none;
}

.btn-outer-book {
  border: 2px dashed var(--black);
  border-radius: 100px;
  justify-content: center;
  align-items: center;
  padding: .2em;
  text-decoration: none;
  display: flex;
  position: relative;
}

@media screen and (max-width: 991px) {
  h1 {
    font-size: 9em;
  }

  h3 {
    font-size: 4.5em;
  }

  .container-lg.flex-v.is-articles {
    border-bottom-style: none;
  }

  .container-lg.h-padded.is-mob-change {
    justify-content: center;
    align-items: flex-start;
    padding-right: 3em;
    overflow: auto;
  }

  .container-lg.is-about-heading {
    justify-content: center;
    align-items: center;
  }

  .nav-menu {
    display: none;
  }

  .nav-link {
    grid-column-gap: .5em;
    grid-row-gap: .5em;
    justify-content: flex-start;
    align-items: flex-start;
    padding-top: .5em;
  }

  .nav-link.is-mobile-2, .nav-link.is-mobile-1 {
    justify-content: flex-start;
    align-items: flex-start;
  }

  .nav-link.is-mobile-3 {
    justify-content: flex-start;
    align-items: flex-start;
    padding-right: .5em;
  }

  .nav-link.is-mobile-4 {
    justify-content: flex-start;
    align-items: flex-start;
  }

  .nav-link.is-mobile-5 {
    padding-right: 1em;
  }

  .nav-text {
    font-size: 4.5em;
  }

  .nav-text.pg-top {
    font-size: 14px;
  }

  .nav-text.is-tip {
    font-size: 13px;
  }

  .heading-md {
    font-size: 56px;
  }

  .heading-md.max-60 {
    max-width: 80%;
  }

  .heading-sm {
    letter-spacing: -.6px;
    font-size: 24px;
  }

  .marquee-mov {
    width: 2880px;
    padding-bottom: 2.5em;
  }

  .marquee-mov.is-white {
    width: 2880px;
  }

  .marquee-mov.is-home-int.is-1, .marquee-mov.is-home-int.is-2 {
    width: 1012px;
  }

  .section {
    padding: 80px 40px;
  }

  .section.mini {
    padding-top: 80px;
    padding-bottom: 48px;
  }

  .section.is-hero.is-about-hero, .section.is-h-articles {
    padding-top: 96px;
  }

  .img-parent.border.small-gap.h-scroll {
    height: auto;
    padding: 8px;
  }

  .img-parent.hero {
    width: 100%;
  }

  .img-parent.overflow-hidden.max-90 {
    height: auto;
  }

  .img-child.banner {
    height: 320px;
  }

  .img-child.home {
    height: 440px;
  }

  .img-child.about-1 {
    height: 280px;
  }

  .img-child.about-3 {
    height: 320px;
  }

  .img-child.about-4 {
    height: 280px;
  }

  .img-child.about-5 {
    height: 184px;
  }

  .img-child.book-cta {
    height: 160px;
  }

  .img-child.solo-img, .img-child.books {
    height: 100%;
  }

  .img-child.contact {
    background-image: none;
    height: 100%;
  }

  .text-para {
    font-size: 16px;
  }

  .text-para.article-date {
    width: 30%;
    font-size: 14px;
  }

  .flex-left {
    justify-content: flex-start;
    align-items: flex-start;
  }

  .flex-left.is-l-pad-17 {
    padding-left: 0;
  }

  .cell-3 {
    padding-left: 80px;
  }

  .border {
    padding: 8px;
  }

  .event-container {
    grid-column-gap: 32px;
    grid-row-gap: 32px;
    flex-flow: wrap;
    padding: 72px 32px;
  }

  .int-heading-wrapper {
    width: 85%;
  }

  .int-cont-wrapper {
    width: 47%;
  }

  .footer {
    justify-content: space-between;
    align-items: flex-end;
    padding: 72px 32px 96px;
  }

  .footer-link {
    padding: 12px 24px;
  }

  .footer-text {
    font-size: 14px;
  }

  .footer-text-wrap {
    width: 60%;
  }

  .text-block {
    font-size: 10px;
  }

  .h-scroll-section.highlights {
    overflow: hidden;
  }

  ._2-col-heading-wrapper.bg-cream {
    padding: 24px 16px;
  }

  ._2-col-content-block {
    grid-column-gap: 16px;
    grid-row-gap: 16px;
    padding: 24px 16px 16px;
  }

  .btn-inner-frame {
    grid-column-gap: 6px;
    grid-row-gap: 6px;
    padding: 10px 20px 10px 14px;
  }

  .btn-inner-frame.is-small {
    padding: 8px 12px 8px 8px;
  }

  .btn-inner-frame.is-pag {
    padding: 8px;
  }

  .btn-inner-frame.is-filters, .btn-inner-frame.is-big {
    padding: 8px 12px 8px 8px;
  }

  .btn-text, .btn-text.is-small {
    font-size: 16px;
  }

  .marquee-text.is-italic {
    font-size: 28px;
  }

  .marquee-wrapper.is-about {
    padding-top: .8em;
    padding-bottom: .8em;
  }

  .form-column.h-scroll.bg-white.border-no-pad {
    width: 100%;
  }

  .text-field.hscroll, .textarea.hscroll {
    font-size: 14px;
  }

  .contact-img {
    background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/6787c53671a97b8708dd701c_Contact%20Img-min.jpg");
    background-position: 50%;
    background-repeat: no-repeat;
    background-size: cover;
  }

  .footer-text-ab {
    font-size: 14px;
  }

  .book-hscroll-wrapper {
    grid-column-gap: 16px;
    grid-row-gap: 16px;
    flex-flow: row;
    grid-template-rows: auto auto;
    grid-template-columns: 1fr 1fr;
    grid-auto-columns: 1fr;
    display: flex;
  }

  .hscroll-height {
    height: 100vh;
  }

  .hscroll-height.about {
    height: auto;
  }

  .sticky-element {
    height: auto;
    position: relative;
  }

  .sticky-element.flex-v.highlights {
    height: auto;
    padding-right: 0;
  }

  .sticky-element.flex-v.books {
    grid-column-gap: 32px;
    grid-row-gap: 32px;
    height: 100%;
    padding-right: 0;
    overflow: auto;
  }

  .book-card {
    width: 90em;
  }

  .book-title-wrapper {
    padding-top: 24px;
    padding-bottom: 12px;
  }

  .book-title {
    font-size: 36px;
  }

  .top-wrapper {
    grid-column-gap: 0em;
    grid-row-gap: 0em;
  }

  .h-track {
    width: 100%;
  }

  .h-track.highlights {
    height: 100%;
    margin-top: 3em;
    padding-bottom: 1em;
    overflow: auto;
  }

  .track-flex {
    grid-column-gap: 32px;
    grid-row-gap: 32px;
    flex-flow: column;
    width: 100%;
  }

  .track-flex.highlights-card {
    flex-flow: row;
    width: auto;
    height: 100%;
    margin-right: 0;
    padding-right: 3em;
  }

  .btn-text-ab {
    font-size: 14px;
    display: none;
  }

  .btn-bg-hover.bg-brown, .btn-bg-hover.bg-black, .book-wrap.bw-3 {
    display: none;
  }

  .logo-text {
    font-size: 20px;
  }

  .home-hero-container {
    flex-flow: row;
    align-items: stretch;
    height: 100vh;
  }

  .home-hero-column {
    width: 100%;
  }

  .home-hero-img {
    object-fit: cover;
  }

  .hero-content {
    grid-column-gap: 32px;
    grid-row-gap: 32px;
    padding: 96px 32px 80px;
  }

  .action {
    grid-column-gap: 8px;
    grid-row-gap: 8px;
  }

  .action.is-padded {
    height: auto;
  }

  .action.is-home {
    margin-top: 4em;
  }

  .home-about-wrapper {
    width: 80%;
    padding-left: 32px;
    padding-right: 0;
  }

  .img-appear-parent {
    display: none;
  }

  .img-appear-parent._2.is-mobile {
    position: relative;
  }

  .img-appear-parent.is-mobile {
    width: 24em;
    height: 36em;
    display: block;
    position: relative;
  }

  .logo-img-1, .logo-img-2, .logo-img-3 {
    height: 36px;
  }

  .logo-img-4 {
    height: 32px;
  }

  .logo-img-5 {
    height: 34px;
  }

  .logo-img-6, .logo-img-7, .logo-img-8 {
    height: 36px;
  }

  .book-card-wrapper {
    grid-column-gap: 16px;
    grid-row-gap: 16px;
    width: auto;
  }

  .book-col-home {
    grid-column-gap: 16px;
    grid-row-gap: 16px;
    grid-template-rows: auto auto;
    grid-template-columns: 1fr 1fr;
    grid-auto-columns: 1fr;
  }

  .book-collection-wrapper {
    margin-top: 24px;
  }

  .book-collection-wrapper.is-1, .book-collection-wrapper.is-2, .book-collection-wrapper.is-3, .book-collection-wrapper.is-4 {
    width: 320px;
  }

  .space-btw {
    justify-content: space-between;
    align-items: flex-end;
  }

  .highlights-card {
    grid-column-gap: 1em;
    grid-row-gap: 1em;
    width: 36em;
  }

  .highlight-card-title {
    padding-top: 2em;
    padding-bottom: 1.5em;
  }

  .date-text {
    font-size: 28px;
  }

  .text-block-4 {
    font-size: 20px;
  }

  .hero-span {
    font-size: 40px;
  }

  .article-wrapper {
    padding-bottom: 2.5em;
  }

  .read-article {
    font-size: 14px;
  }

  .article-heading {
    width: 100%;
    font-size: 32px;
  }

  .burger-menu {
    grid-column-gap: 4px;
    grid-row-gap: 4px;
    height: 32px;
    display: flex;
    position: relative;
    overflow: hidden;
  }

  .mobile-menu {
    display: none;
  }

  .text-block-5 {
    text-align: right;
  }

  .about-img.ai-6 {
    height: 184px;
  }

  .about-img.ai-5 {
    height: 280px;
  }

  .about-img.ai-4 {
    height: 320px;
  }

  .about-img.ai-3 {
    height: 400px;
  }

  .about-img.ai-2 {
    height: 280px;
  }

  .book-cta-img.bi-1, .book-cta-img.bi-2 {
    height: 160px;
  }

  .hero-100 {
    height: 90vh;
  }

  .hero-h-wrapper.is-80 {
    width: 90%;
    display: flex;
  }

  .hero-h-wrapper.is-80.is-mobile {
    display: none;
  }

  .hero-h-wrapper.is-tablet {
    display: flex;
  }

  .hero-h-wrapper.is-tablet-hide {
    display: none;
  }

  .btn-circle {
    width: 12px;
    height: 12px;
  }

  .btn-text-wrap {
    padding-top: 2px;
  }

  .btn-outer-frame {
    padding: 1px;
  }

  .hero-img.is-books {
    background-position: 60%;
    width: 100%;
    height: 100%;
  }

  .hero-img.is-articles {
    background-position: 70% 0;
  }

  .hero-img.is-btm {
    background-position: 70%;
  }

  .hero-img.is-btm.is-interviews, .hero-img.is-top.is-interviews {
    background-position: 50% 0;
  }

  .text {
    font-size: 16px;
  }

  .d-30.is-rel-high {
    width: 40%;
  }

  .art-heading-wrapper {
    width: 90%;
  }

  .paper-div {
    display: none;
  }

  .h-hide {
    padding-top: 1.6em;
  }

  .h-hide.is-h2-super {
    padding-top: 2.4em;
    padding-bottom: 0;
  }

  .h-hide.italic.is-cta {
    padding-top: 2em;
    padding-bottom: 1.5em;
  }

  .h-hide.is-sp {
    padding-top: 2.5em;
    padding-bottom: .75em;
  }

  .h1-italic {
    font-size: 9.125em;
  }

  .h1-italic.is-hero {
    font-size: 9em;
  }

  .h1-italic.cta {
    font-size: 8em;
  }

  .h1-italic.is-super {
    font-size: 14em;
  }

  .h-hide-italic {
    padding-top: .4em;
    padding-left: .6em;
    padding-right: .6em;
  }

  .book-img-wrapper {
    height: 440px;
  }

  .about-h-row {
    width: 100%;
  }

  .scroll-bar-wrapper {
    display: none;
  }

  .d-80 {
    width: 100%;
  }

  .para-wrapper.is-a-1 {
    width: 36em;
  }

  .para-wrapper.is-a-2 {
    width: 85%;
  }

  .para-wrapper.is-a-4 {
    width: 50%;
  }

  .d-40.is-mobile.is-space-btm {
    margin-bottom: 2em;
  }

  .int-img-wrapper {
    width: 47%;
    height: 56em;
    padding: 6px;
  }

  .int-img-wrapper.is-hov-1, .int-img-wrapper.is-hov-2 {
    display: none;
  }

  .flex-v-split.is-right.is-mobile-change {
    grid-column-gap: 16px;
    grid-row-gap: 16px;
    justify-content: flex-start;
    align-items: flex-start;
  }

  .flex-v-split.is-home-int {
    padding-left: 8px;
    padding-right: 8px;
  }

  .book-col-img-wrapper {
    border-width: .3em;
    border-radius: 4px;
    width: 50%;
    height: 70vh;
    padding: 4px;
  }

  .book-content-wrapper {
    width: 50%;
    padding: 24px 16px;
  }

  .flex-c-btm.is-mobile {
    padding-left: 0;
    padding-right: 0;
  }

  .marq-column {
    width: 1440px;
  }

  .marq-column.is-gal-marq {
    width: 2736px;
  }

  .text-caps.is-gal-marq {
    font-size: 32px;
  }

  .text-caps.is-italic.text-white {
    font-size: 24px;
  }

  .text-caps.is-home-int {
    font-size: 30px;
  }

  .text-cap-wrap {
    width: 140px;
  }

  .text-cap-wrap.is-gal-marq {
    width: 274px;
  }

  .text-cap-wrap.is-home-int {
    width: 201px;
  }

  .marquee-circle.is-gal-dash {
    width: 20px;
  }

  .marquee-circle.bg-white {
    width: 8px;
    height: 8px;
  }

  .marq-set {
    grid-column-gap: 16px;
    grid-row-gap: 16px;
    width: 360px;
    padding-left: 16px;
  }

  .marq-set.is-gal-marq {
    grid-column-gap: 24px;
    grid-row-gap: 24px;
    width: 684px;
    padding-left: 24px;
  }

  .marq-set.is-home-int {
    width: 506px;
    padding-left: 16px;
  }

  .h-italic-wrapper.is-h2-super {
    padding-top: 1.5em;
    padding-bottom: 1em;
  }

  .marquee-track.is-home-cream {
    width: 2320px;
  }

  .marquee-track.is-home-black {
    width: 1912px;
  }

  .h1-hero {
    font-size: 9em;
  }

  .h1-hero.cta {
    font-size: 8.5em;
  }

  .home-hero-section {
    height: 90vh;
  }

  .h-light-text-wrap.is-1 {
    padding-left: 32em;
  }

  .h-light-text-wrap.is-2 {
    padding-right: 0;
  }

  .d-60 {
    width: 70%;
  }

  .h2-super-italic {
    font-size: 12em;
  }

  .h2-super {
    font-size: 13em;
  }

  .white-container, .black-container {
    width: 240vh;
    height: 240vh;
  }

  .gallery-row {
    grid-column-gap: 21vw;
    grid-row-gap: 21vw;
    padding-left: 10.5vw;
    padding-right: 10.5vw;
  }

  .gallery-img-wrapper {
    width: 54vw;
    height: 32em;
  }

  .gal-text {
    font-size: 2.75em;
  }

  .grid {
    width: 150%;
  }

  .article-flex {
    grid-column-gap: 32px;
    grid-row-gap: 32px;
  }

  .hero-sub-wrapper {
    width: 50%;
    margin-top: 3em;
  }

  .nav-no {
    font-size: 3em;
    line-height: .7;
  }

  .drop-pop.is-mobile {
    width: 24em;
    height: 15em;
    padding: .4em;
    position: static;
  }

  .drop-pop.is-mobile.is-1 {
    transform: rotate(-20deg);
  }

  .drop-pop.is-mobile.is-2 {
    transform: rotate(20deg);
  }

  .drop-pop.is-mobile.is-3 {
    transform: rotate(-20deg);
  }

  .drop-pop.is-mobile.is-4, .drop-pop.is-mobile.is-4.w--current {
    transform: rotate(20deg);
  }

  .drop-pop.is-mobile.is-5 {
    transform: rotate(-20deg);
  }

  .drop-pop-img.is-mobile {
    width: 100%;
    height: 100%;
  }

  .drop-pop-wrapper {
    display: none;
  }

  .page-bar {
    z-index: 998;
    justify-content: space-between;
    align-items: center;
    padding-top: 12px;
    padding-left: 32px;
    padding-right: 32px;
  }

  .h-scroll-bar {
    grid-column-gap: 1em;
    grid-row-gap: 1em;
  }

  .scroll-point, .scroll-point.is-1, .scroll-point.is-2 {
    width: 12px;
    height: 12px;
  }

  .sroll-line {
    width: 1.5em;
    height: 2px;
  }

  .footer-big-txt-wrap {
    padding-top: 16px;
  }

  .footer-flex {
    justify-content: flex-start;
    align-items: flex-start;
    width: 75%;
  }

  .gtnp-text {
    font-size: 32px;
  }

  .np-text {
    font-size: 24px;
  }

  .circle-cta-wrapper {
    display: flex;
  }

  .circle-cta-wrapper.is-home {
    height: 110vh;
  }

  .inner-circle-cream {
    width: 40em;
    height: 40em;
  }

  .inner-circle-cream.is-home {
    width: 48em;
    height: 48em;
  }

  .inner-circle-cream.is-articles {
    width: 80em;
    height: 80em;
  }

  .inner-circle-white {
    width: 68em;
    height: 68em;
  }

  .outer-dotted {
    width: 120em;
    height: 120em;
  }

  .rotate-interviews.is-black {
    width: 90em;
    height: 90em;
  }

  .rotate-interviews.is-white {
    width: 90em;
    height: 90em;
    display: none;
  }

  .outer-circle-cream {
    width: 104em;
    height: 104em;
  }

  .inner-dotted {
    width: 54em;
    height: 54em;
  }

  .inner-dotted.is-home {
    width: 80vh;
    height: 80vh;
  }

  .rot-img-wrapper {
    width: 68em;
    height: 68em;
  }

  .rot-img-wrapper.is-home {
    width: 90vh;
    height: 90vh;
  }

  .rot-int-img {
    width: 14em;
    height: 18em;
  }

  .inner-set {
    width: 72em;
    height: 72em;
  }

  .rot-int-wrapper {
    width: 82em;
    height: 82em;
  }

  .rot-int-wrapper.w--current {
    width: 104em;
    min-width: 104em;
    height: 104em;
  }

  .hov-circle {
    display: none;
  }

  .int-img-div {
    z-index: 6;
    width: 9em;
    height: 12em;
  }

  .article-date-wrap {
    width: 10%;
  }

  .transition-intro {
    display: flex;
  }

  .page-intro-text {
    font-size: 22em;
  }

  .page-intro-text.is-sm {
    font-size: 20em;
  }

  .page-intro-text-wrap.is-sp-home-top {
    padding-right: 4em;
  }

  .book-intro-img-wrapper {
    width: 20em;
    height: 28em;
  }

  .article-intro-div.is-white.is-1, .article-intro-div.is-white.is-2 {
    width: 36em;
  }

  .article-intro-div.is-white.is-ab-1, .article-intro-div.is-white.is-ab-2 {
    width: 32em;
  }

  .article-intro-div.is-black.is-1, .article-intro-div.is-black.is-2 {
    width: 36em;
  }

  .article-intro-div-text {
    font-size: 4em;
  }

  .int-intro-img-div {
    width: 28em;
    height: 20em;
  }

  .int-intro-img-div.is-ab-1 {
    top: 8em;
    left: 2em;
  }

  .int-intro-img-div.is-ab-2 {
    right: 24em;
  }

  .int-intro-img-div.is-ab-3 {
    top: 16em;
  }

  .div-block-11 {
    grid-column-gap: 1.5em;
    grid-row-gap: 1.5em;
  }

  .circle-cta-text, .circle-cta-text.is-italic {
    font-size: 12em;
  }

  .div-block-12 {
    grid-column-gap: 3em;
    grid-row-gap: 3em;
  }

  .btn-outer-cta, .btn-outer-cta.is-big {
    padding: 2px;
  }

  .home-int-div.is-1 {
    width: 36em;
  }

  .home-int-img-wrapper {
    height: 24em;
  }

  .int-set.is-1 {
    width: 36em;
  }

  .int-set.is-2 {
    width: 36em;
    top: 100em;
  }

  .int-set.is-3 {
    width: 36em;
    top: 200em;
  }

  .int-set.is-4 {
    width: 36em;
    top: 300em;
  }

  .h3-iitalic {
    font-size: 4em;
  }

  .d-50 {
    width: 60%;
  }

  .marquee-cream {
    transform: translate(-6em, -24em)rotate(-13deg);
  }

  .marquee-div.is-cream {
    grid-column-gap: 16px;
    grid-row-gap: 16px;
    width: 1160px;
    padding-left: 16px;
  }

  .marquee-div.is-black {
    grid-column-gap: 15px;
    grid-row-gap: 15px;
    width: 956px;
    padding-left: 16px;
  }

  .mobile-menu-container {
    z-index: 998;
    flex-flow: column;
    display: none;
    inset: 0% 0% auto;
  }

  .nav-block {
    justify-content: space-around;
    align-items: center;
    width: 100%;
    height: 19%;
    position: relative;
  }

  .nav-block.is-1, .nav-block.is-2, .nav-block.is-3 {
    grid-column-gap: 4em;
    grid-row-gap: 4em;
    justify-content: center;
    align-items: center;
  }

  .nav-block.is-4, .nav-block.is-5 {
    grid-column-gap: 8em;
    grid-row-gap: 8em;
    justify-content: center;
    align-items: center;
  }

  .burger-line-div {
    grid-column-gap: 4px;
    grid-row-gap: 4px;
    flex-flow: column;
    display: flex;
  }

  .cross-div {
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    display: flex;
    position: absolute;
  }

  .cross-line {
    width: 20px;
    height: 2px;
    position: absolute;
    transform: rotate(0);
  }

  .cross-line.is-1 {
    background-color: var(--black);
    width: 20px;
    height: 1px;
    padding-bottom: 0;
    padding-right: 0;
    transform: rotate(45deg);
  }

  .cross-line.is-2 {
    background-color: var(--black);
    width: 20px;
    height: 1px;
    padding-bottom: 0;
    padding-right: 0;
    transform: rotate(-45deg);
  }

  .whipe-cream-btm {
    inset: auto 0% 0%;
  }

  .d-9.is-ab-top {
    width: 20%;
    top: 10em;
  }

  .rot-int-img-wrap {
    width: 20em;
    height: 14em;
  }

  .whipe-hero {
    display: none;
  }

  .gallery-marquee-container {
    width: 25em;
    height: 25em;
  }

  .gallery-marq-wrap {
    width: 85%;
    height: 85%;
  }

  .article-fall-container {
    height: 60vh;
  }

  .article-fall-container.is-logos {
    height: 65vh;
  }

  .article-fall-container.is-home-articles {
    height: 55vh;
  }

  .article-fall-card.is-3.is-fall, .article-fall-card.is-3.is-def {
    border-width: 1px;
    width: 240px;
    left: 30em;
  }

  .article-fall-card.is-1.is-fall, .article-fall-card.is-1.is-def {
    border-width: 1px;
    width: 240px;
    left: -8em;
  }

  .article-fall-card.is-2.is-fall, .article-fall-card.is-2.is-def {
    border-width: 1px;
    width: 240px;
    bottom: 84px;
    left: 15em;
  }

  .article-fall-card.is-4.is-fall, .article-fall-card.is-4.is-def {
    width: 240px;
    bottom: 100px;
    right: 21.5em;
  }

  .article-fall-card.is-5.is-fall, .article-fall-card.is-5.is-def {
    border-width: 1px;
    width: 240px;
    bottom: 100px;
    right: -11.5em;
  }

  .fall-card-text {
    font-size: 24px;
  }

  .logo-card.is-2 {
    width: 88%;
    height: 88%;
  }

  .logo-card.is-3.is-fall, .logo-card.is-3.is-def {
    width: 120px;
    height: 52px;
    right: -2em;
  }

  .logo-card-wrap.is-2.is-fall, .logo-card-wrap.is-2.is-def {
    border-width: 1px;
    width: 160px;
    height: 52px;
    bottom: 96px;
    left: -10em;
  }

  .logo-card-wrap.is-1.is-fall, .logo-card-wrap.is-1.is-def {
    border-width: 1px;
    width: 150px;
    height: 52px;
    right: 111px;
  }

  .logo-card-wrap.is-h-1.is-def, .logo-card-wrap.is-h-1.is-fall {
    border-width: 1px;
    width: 144px;
    height: 50px;
    left: auto;
    right: 332px;
  }

  .logo-card-wrap.is-h-2.is-def, .logo-card-wrap.is-h-2.is-fall {
    border-width: 1px;
    width: 130px;
    height: 48px;
    left: auto;
    right: 200px;
  }

  .logo-card-wrap.is-h-3.is-def {
    width: 160px;
    height: 52px;
    right: 40px;
  }

  .logo-card-wrap.is-h-3.is-fall {
    border-width: 1px;
    width: 160px;
    height: 52px;
    right: 40px;
  }

  .logo-card-wrap.is-h-4.is-def, .logo-card-wrap.is-h-4.is-fall {
    border-width: 1px;
    width: 120px;
    height: 44px;
    bottom: 72px;
    left: auto;
    right: 272px;
  }

  .logo-card-wrap.is-h-5.is-def, .logo-card-wrap.is-h-5.is-fall {
    border-width: 1px;
    width: 110px;
    height: 48px;
    bottom: 60px;
    right: 158px;
  }

  .logo-card-wrap.is-h-6.is-def, .logo-card-wrap.is-h-6.is-fall {
    border-width: 1px;
    width: 64px;
    height: 44px;
    bottom: 70px;
    right: 90px;
  }

  .logo-card-wrap.is-h-7.is-def, .logo-card-wrap.is-h-7.is-fall {
    border-width: 1px;
    width: 140px;
    height: 48px;
    bottom: 115px;
    left: auto;
    right: 210px;
  }

  .logo-card-wrap.is-int-1 {
    height: 7em;
  }

  .logo-card-wrap.is-int-1.is-fall {
    width: 22em;
    height: 7em;
  }

  .logo-card-wrap.is-int-2, .logo-card-wrap.is-int-2.is-fall, .logo-card-wrap.is-int-3 {
    height: 7em;
  }

  .logo-card-wrap.is-int-7 {
    height: 7em;
    bottom: 10.5em;
  }

  .logo-card-wrap.is-int-5 {
    width: 20em;
    height: 7.5em;
    bottom: 11.5em;
    left: 6em;
  }

  .logo-card-wrap.is-int-4 {
    height: 7em;
  }

  .logo-card-wrap.is-int-6 {
    height: 7em;
    bottom: 7.8em;
  }

  .logo-card-wrap.is-int-8 {
    width: 15em;
    height: 7.5em;
    bottom: 12em;
    right: 37em;
  }

  .page-no-wrap {
    height: auto;
    padding: 8px 1.2em 6px;
  }

  .page-no-wrap.is-tip {
    height: auto;
    padding-top: 8px;
    padding-bottom: 8px;
  }

  .cursor-parent {
    display: none;
  }

  .news-marquee-text.text-black, .news-marquee-text.text-grey {
    font-size: 124px;
  }

  .banner-marquee-track {
    width: 3092px;
  }

  .banner-marquee-col {
    width: 1546px;
  }

  .banner-marquee-text {
    letter-spacing: -6px;
    font-size: 120px;
  }

  .banner-sm-img-wrap {
    width: 24em;
    height: 32em;
  }

  .banner-sm-img-wrap.is-2 {
    width: 30em;
    height: 38em;
  }

  .banner-sm-img-wrap.is-1 {
    width: 24em;
    height: 32em;
    left: 0;
  }

  .hover-image-wrapper, .footer-bg {
    display: none;
  }

  .news-marquee-text-wrap {
    width: 5590px;
  }

  .news-marquee-track {
    width: 11180px;
  }

  .select-field {
    height: 2em;
    font-size: 20px;
  }

  .filter-inner {
    width: 45%;
  }

  .btn-filter {
    padding: 2px;
  }

  .next-btn {
    width: 80px;
    padding: 2px;
  }

  .article-pagination {
    padding-bottom: 12em;
  }

  .page-btn {
    width: 32px;
    height: 32px;
  }

  .page-btn-collection {
    font-size: 1.5em;
  }

  .click-gal-wrapper {
    display: none;
  }

  .collection-list-wrapper-5 {
    margin-top: 6em;
  }

  .home-article-text-wrap.is-1 {
    width: 1170px;
    padding-right: 24px;
  }

  .home-article-text-wrap.is-2 {
    width: 1130px;
    padding-right: 32px;
  }

  .home-article-text-wrap.is-3 {
    width: 1450px;
    padding-right: 24px;
  }

  .home-article-text-wrap.is-4 {
    width: 1280px;
  }

  .home-article-track.is-1 {
    width: 2340px;
  }

  .home-article-track.is-2 {
    width: 2260px;
  }

  .home-article-track.is-3, .home-article-track.is-4 {
    width: 2900px;
  }

  .home-article-track.is-5 {
    width: 2560px;
  }

  .home-intro-marquee-box {
    width: 140em;
  }

  .marquee-flex.is-main, .marquee-flex.is-hidden {
    grid-column-gap: 12px;
    grid-row-gap: 12px;
  }

  .home-intro-marquee.is-2 {
    width: 2728px;
  }

  .home-intro-marquee.is-1 {
    width: 3624px;
  }

  .home-intro-m-text {
    white-space: nowrap;
    font-size: 40px;
  }

  .home-intro-m-text-wrap {
    padding-top: 6px;
  }

  .home-intro-m-set {
    grid-column-gap: 16px;
    grid-row-gap: 16px;
    width: 906px;
    padding-left: 16px;
  }

  .home-intro-m-set.is-grey {
    width: 682px;
  }

  .image-carousel-mobile {
    grid-column-gap: .5em;
    grid-row-gap: .5em;
    white-space: nowrap;
    justify-content: space-between;
    align-items: flex-start;
    min-width: 125em;
    height: auto;
    display: flex;
  }

  .image-carousel-wrapper {
    justify-content: center;
    align-items: center;
    width: 100%;
    margin-top: 8em;
    display: flex;
    overflow: hidden;
  }
}

@media screen and (max-width: 767px) {
  .container-lg.flex-sb {
    padding-left: 24px;
    padding-right: 24px;
  }

  .container-lg.is-nav {
    background-color: var(--cream);
    border-top-width: 1px;
  }

  .container-lg.h-padded {
    padding-left: 16px;
    padding-right: 16px;
  }

  .nav-text.pg-top {
    font-size: 14px;
  }

  .heading-lg.flex-text-v {
    width: 100%;
    font-size: 36px;
  }

  .marquee-mov {
    padding-top: 2em;
    padding-bottom: 4em;
  }

  .section.no-btm-pad {
    padding-left: 16px;
    padding-right: 16px;
  }

  .img-parent.border.int-img {
    width: 100%;
  }

  .event-container {
    grid-column-gap: 24px;
    grid-row-gap: 24px;
  }

  .description-container.flex-h.interview {
    flex-flow: column;
    padding-bottom: 5em;
  }

  .int-cont-wrapper {
    width: 50%;
  }

  .footer {
    padding-left: 16px;
    padding-right: 16px;
  }

  .footer-text-wrap {
    width: 80%;
  }

  .flex-v.interview-content {
    width: 100%;
  }

  .btn-inner-frame {
    padding: 8px 16px 8px 8px;
  }

  .btn-text {
    font-size: 14px;
  }

  .hscroll-height {
    height: 100vh;
  }

  .sticky-element.flex-v.highlights {
    padding-left: 16px;
  }

  .sticky-element.flex-v.books {
    padding-top: 0;
    padding-bottom: 3em;
  }

  .book-card {
    width: 75em;
  }

  .h-track {
    height: 100%;
  }

  .track-flex.highlights-card {
    margin-top: 0;
  }

  .logo-text {
    font-size: 16px;
  }

  .home-hero-container {
    flex-flow: column;
  }

  .home-hero-column {
    width: 100%;
  }

  .hero-content {
    grid-column-gap: 32px;
    grid-row-gap: 32px;
    width: 100%;
    padding: 80px 24px 48px;
  }

  .book-card-wrapper {
    grid-column-gap: 12px;
    grid-row-gap: 12px;
  }

  .book-collection-wrapper {
    width: 240px;
  }

  .book-collection-wrapper.is-1, .book-collection-wrapper.is-2, .book-collection-wrapper.is-3, .book-collection-wrapper.is-4 {
    width: 280px;
  }

  .burger-menu {
    height: 20px;
  }

  .burger-line-1, .burger-line-2, .border-line-3 {
    height: 1px;
  }

  .article-list-wrapper-home {
    margin-top: 40px;
  }

  .interview-img {
    height: 110%;
  }

  .hero-100 {
    height: 120vh;
  }

  .hero-100.is-books {
    height: 130vh;
  }

  .btn-circle {
    width: 10px;
    height: 10px;
  }

  .btn-text-wrap {
    padding-top: .2em;
  }

  .book-img-wrapper {
    height: 360px;
    padding: 4px;
  }

  .about-h-row {
    padding-left: 16px;
    padding-right: 16px;
  }

  .int-img-wrapper {
    width: 45%;
  }

  .book-col-img-wrapper {
    width: 35%;
  }

  .book-content-wrapper {
    width: 65%;
    padding-top: 0;
    padding-bottom: 0;
  }

  .home-hero-section {
    height: 120vh;
  }

  .home-hero-section.is-books {
    height: 130vh;
  }

  .sticky-hero {
    width: 240vw;
    height: 240vw;
  }

  .white-container {
    width: 240vh;
    height: 240vh;
  }

  .black-container {
    width: 240vh;
  }

  .container-hide {
    width: 240vw;
    height: 240vw;
  }

  .d-70.is-hidden {
    width: 100%;
  }

  .gal-text.is-1 {
    font-size: 2.5em;
  }

  .grid {
    width: 120%;
    height: 120%;
  }

  .grid.is-home-gallery {
    width: 160%;
  }

  .grid-box {
    border-bottom-width: 1px;
    border-right-width: 1px;
  }

  .drop-pop.is-mobile {
    display: none;
  }

  .page-bar {
    padding-left: 16px;
    padding-right: 16px;
  }

  .scroll-point, .scroll-point.is-1, .scroll-point.is-2 {
    width: 10px;
    height: 10px;
  }

  .footer-flex {
    width: 60%;
  }

  .circle-cta-wrapper.is-home {
    height: 280vh;
  }

  .circle-cta-wrapper.is-home.is-interview {
    height: 105vh;
  }

  .inner-dotted.is-home {
    width: 200vh;
    height: 200vh;
  }

  .rot-img-wrapper.is-home {
    width: 100em;
    height: 100em;
  }

  .transition-intro {
    display: none;
  }

  .book-intro-img-wrapper, .book-intro-img-wrapper.is-1 {
    width: 20em;
    height: 28em;
  }

  .section-100.is-home-int {
    height: 115vh;
  }

  .int-set.is-2 {
    top: 64em;
  }

  .int-set.is-3 {
    top: 124em;
    left: 8em;
  }

  .int-set.is-4 {
    top: 160em;
  }

  .nav-block {
    height: 17.5%;
  }

  .rot-int-img-wrap {
    width: 24em;
    height: 15em;
    padding: .45em;
  }

  .article-fall-container, .article-fall-container.is-logos, .article-fall-container.is-home-articles {
    height: 120vh;
  }

  .article-fall-container.is-articles {
    height: 110vh;
  }

  .fall-card-text {
    font-size: 24px;
  }

  .logo-card-wrap.is-int-1 {
    border-width: 1px;
    width: 24em;
    height: 7.5em;
  }

  .logo-card-wrap.is-int-1.is-fall {
    width: 24em;
    height: 7.5em;
  }

  .logo-card-wrap.is-int-2 {
    border-width: 1px;
    height: 7.5em;
    left: 24.5em;
  }

  .logo-card-wrap.is-int-2.is-fall {
    height: 7.5em;
  }

  .logo-card-wrap.is-int-3 {
    border-width: 1px;
  }

  .logo-card-wrap.is-int-7 {
    border-width: 1px;
    width: 19em;
    height: 7.5em;
    bottom: 10.5em;
    right: 15.5em;
  }

  .logo-card-wrap.is-int-5 {
    border-width: 1px;
    bottom: 12.5em;
  }

  .logo-card-wrap.is-int-4 {
    border-width: 1px;
    width: 26em;
    height: 7.5em;
    right: 2.75em;
  }

  .logo-card-wrap.is-int-6 {
    border-width: 1px;
    width: 24em;
    height: 7.5em;
    bottom: 8.3em;
  }

  .logo-card-wrap.is-int-8 {
    border-width: 1px;
    bottom: 11.5em;
    right: 36.5em;
  }

  .page-no-wrap {
    padding-left: 1.6em;
    padding-right: 1.6em;
  }

  .page-no-wrap.is-tip {
    display: none;
  }

  .page-no-wrap.is-tip.is-gallery {
    display: flex;
  }

  .page-btn {
    width: 40px;
    height: 40px;
  }

  .int-card-wrapper {
    height: 650vh;
  }

  .image-carousel-wrapper {
    margin-top: 5em;
    overflow: hidden;
  }
}

@media screen and (max-width: 479px) {
  h1 {
    letter-spacing: -.4px;
    font-size: 14em;
    line-height: .6;
  }

  h3 {
    font-size: 8em;
  }

  .container-lg.flex-sb {
    padding-top: 8px;
    padding-bottom: 8px;
  }

  .container-lg.flex-h {
    grid-column-gap: 24px;
    grid-row-gap: 24px;
    flex-flow: column;
  }

  .container-lg.spaced {
    grid-column-gap: 24px;
    grid-row-gap: 24px;
  }

  .container-lg.is-nav {
    background-color: var(--cream);
    border-top-width: 1px;
    padding: 8px 16px;
  }

  .container-lg.h-padded {
    padding-left: 16px;
    padding-right: 16px;
  }

  .container-lg.h-padded.is-mob-change.is-books {
    margin-bottom: 32px;
  }

  .container-lg.is-mobile {
    padding-left: 16px;
    padding-right: 16px;
  }

  .nav-link {
    grid-column-gap: 2em;
    grid-row-gap: 2em;
    padding-top: 1.25em;
    padding-bottom: .25em;
  }

  .nav-link.is-mobile-2 {
    padding-right: 2em;
  }

  .nav-link.is-mobile-1 {
    padding-right: .5em;
  }

  .nav-link.is-mobile-3 {
    padding-right: 1em;
  }

  .nav-link.is-mobile-4 {
    padding-right: 4.5em;
  }

  .nav-link.is-mobile-5 {
    justify-content: flex-start;
    align-items: flex-start;
  }

  .nav-text {
    font-size: 10em;
  }

  .nav-text.pg-top {
    font-size: 14px;
  }

  .heading-lg.max-70 {
    overflow-wrap: normal;
    max-width: 100%;
  }

  .heading-md {
    font-size: 36px;
  }

  .heading-md.max-70, .heading-md.max-60 {
    max-width: 100%;
  }

  .heading-sm {
    letter-spacing: -.2px;
    font-size: 20px;
    font-weight: 500;
    line-height: 24px;
  }

  .hero-section.bg-cream.relative {
    padding-top: 96px;
    padding-left: 24px;
    padding-right: 24px;
  }

  .hero-section.events {
    padding-top: 72px;
    padding-left: 24px;
    padding-right: 24px;
  }

  .hero-section.article-page.bg-cream {
    padding: 120px 24px 80px;
  }

  .hero-section.book-page.bg-cream {
    padding-top: 80px;
  }

  .marquee-section.about {
    margin-bottom: 0;
  }

  .marquee-img-container {
    width: 50%;
    height: 240px;
  }

  .flex-h._150-percent {
    min-width: 300%;
  }

  .flex-h.center.article-link {
    grid-column-gap: 4px;
    grid-row-gap: 4px;
  }

  .marquee-mov {
    width: 1792px;
    padding-top: 16px;
    padding-bottom: 16px;
  }

  .marquee-mov.is-gal-marq {
    justify-content: flex-start;
    align-items: center;
    width: 2976px;
  }

  .marquee-mov.is-white {
    width: 2112px;
  }

  .section {
    grid-column-gap: 40px;
    grid-row-gap: 40px;
    padding: 96px 16px 64px;
  }

  .section.bg-cream {
    overflow: hidden;
  }

  .section.bg-cream.mobile {
    grid-column-gap: 32px;
    grid-row-gap: 32px;
  }

  .section.events {
    grid-column-gap: 32px;
    grid-row-gap: 32px;
    padding-top: 32px;
    padding-bottom: 40px;
  }

  .section.left-nopad {
    padding: 40px 24px 0 0;
  }

  .section.mini {
    padding-left: 24px;
    padding-right: 24px;
  }

  .section.no-h-pad.bg-cream {
    grid-column-gap: 48px;
    grid-row-gap: 48px;
  }

  .section.is-hero.is-about-hero {
    padding-top: 64px;
    overflow: hidden;
  }

  .section.no-btm-pad {
    grid-column-gap: 32px;
    grid-row-gap: 32px;
  }

  .section.no-btm-pad.is-rel-high {
    z-index: 2;
  }

  .section.is-articles {
    border-top-width: .5em;
  }

  .section.no-pad.big-btm {
    padding-top: 80px;
    padding-bottom: 96px;
  }

  .section.home-int-wrapper {
    height: 550vh;
  }

  .section.is-h-books {
    padding-top: 96px;
  }

  .img-parent {
    border-radius: 1px;
  }

  .img-parent.border.border-white.split-card {
    padding: 6px;
  }

  .img-parent.border.small-gap {
    padding: 4px;
  }

  .img-parent.border.small-gap.h-scroll {
    padding: 8px;
  }

  .img-parent.overflow-hidden.max-90 {
    width: 100%;
  }

  .img-child.banner {
    height: 240px;
  }

  .img-child.hero {
    width: 100%;
  }

  .img-child.home {
    height: 360px;
  }

  .img-child.img-event-banner {
    height: 240px;
  }

  .img-child.split-card {
    height: 50em;
  }

  .img-child.img-article-hero {
    height: 200px;
  }

  .img-child.home-about {
    height: auto;
  }

  .img-child.books {
    height: 100%;
  }

  .img-child.is-a-6 {
    width: 200vw;
    position: absolute;
  }

  .img-child.is-a-6-ab {
    width: 200vw;
  }

  .text-para {
    font-size: 16px;
    line-height: 1.3;
  }

  .text-para.max-50, .text-para.max-30.margin-top {
    width: 100%;
    max-width: 100%;
  }

  .text-para.article-date {
    width: 100%;
    font-size: 14px;
    line-height: 1.2;
  }

  .text-para.event-highlight {
    line-height: 1;
  }

  .text-para.max-80 {
    width: 100%;
    max-width: 100%;
  }

  .div-left-padding {
    width: 100%;
    padding-left: 0;
  }

  .flex-left {
    grid-column-gap: 32px;
    grid-row-gap: 32px;
    flex-flow: column;
  }

  .flex-left.is-l-pad-17 {
    padding-left: 0;
  }

  .flex-left.is-auto.is-filter-clear {
    display: none;
  }

  .flex-left.is-auto.is-filter-clear.is-mobile {
    flex-flow: row;
    justify-content: center;
    align-items: center;
    width: 100%;
    display: flex;
  }

  .cell {
    padding-top: 0;
    padding-left: 0;
  }

  .cell-2 {
    padding-left: 0;
  }

  .cell-3 {
    padding-left: 0;
  }

  .border {
    padding: 4px;
  }

  .event-container {
    grid-column-gap: 16px;
    grid-row-gap: 16px;
    flex-flow: column;
    justify-content: flex-start;
    align-items: stretch;
    padding: 56px 16px 96px;
  }

  .event-container.border-btm {
    grid-column-gap: 16px;
    grid-row-gap: 16px;
    padding-bottom: 16px;
  }

  .heading-container.bg-white.flex-split.border {
    grid-column-gap: 8px;
    grid-row-gap: 8px;
    flex-flow: column;
    justify-content: space-between;
    align-items: flex-start;
    padding: 12px;
  }

  .description-container.border-btm {
    padding: 12px 12px 8px;
  }

  .description-container.flex-h.interview {
    grid-column-gap: 32px;
    grid-row-gap: 32px;
    padding-bottom: 32px;
  }

  .int-heading-wrapper {
    width: 100%;
  }

  .int-cont-wrapper {
    width: 100%;
    padding-left: 0;
    padding-right: 24px;
  }

  .collection-list {
    grid-column-gap: 8px;
    grid-row-gap: 8px;
    flex-flow: wrap;
    grid-template-rows: auto auto;
    grid-template-columns: 1fr 1fr;
    grid-auto-columns: 1fr;
    grid-auto-flow: row;
    justify-content: stretch;
    display: grid;
  }

  .collection-list-2 {
    grid-column-gap: 40px;
    grid-row-gap: 40px;
  }

  .h1-wrapper {
    padding-top: 16px;
    padding-bottom: 24px;
  }

  .divider {
    width: 100%;
    display: flex;
  }

  .divider.brown {
    background-color: var(--grey);
    margin-top: 8px;
  }

  .footer {
    grid-column-gap: 24px;
    grid-row-gap: 24px;
    border-top-width: .5em;
    flex-flow: column;
    justify-content: space-between;
    align-items: flex-start;
    padding: 48px 16px 80px;
  }

  .footer-menu {
    flex-flow: column;
    grid-auto-flow: row;
    padding-left: 0;
    padding-right: 0;
    display: flex;
  }

  .footer-link {
    border-top: 1px none var(--black);
    border-bottom: 1px solid var(--black);
    border-left-style: none;
    border-left-width: 0;
    padding-top: 16px;
    padding-bottom: 16px;
  }

  .footer-link.last {
    border-right-style: none;
  }

  .footer-text {
    font-size: 20px;
  }

  .footer-text-wrap {
    width: 100%;
    padding-left: 0;
  }

  .footer-marquee {
    height: auto;
  }

  .moving-wrapper {
    grid-column-gap: 8px;
    grid-row-gap: 8px;
    width: 2576px;
  }

  .footer-super-text {
    text-shadow: 1px 1px 0 var(--black), -1px 1px 0 var(--black), -1px -1px 0 var(--black), 1px -1px 0 var(--black);
    font-size: 96px;
  }

  .footer-super-text-wrap {
    width: 596px;
    padding-top: 16px;
    padding-left: 24px;
    padding-right: 24px;
  }

  .star-img {
    width: 48px;
    height: 48px;
  }

  .hl-label-container {
    padding-top: 6px;
    padding-bottom: 6px;
    position: static;
  }

  .text-block {
    font-size: 12px;
  }

  .title-wrapper.small {
    grid-column-gap: 16px;
    grid-row-gap: 16px;
  }

  .h-scroll-section.bg-cream {
    padding-bottom: 40px;
  }

  .h-scroll-section.highlights {
    height: auto;
    padding-top: 80px;
    padding-bottom: 0;
  }

  .h-scroll-section.is-books {
    padding-bottom: 80px;
  }

  .article-hero-content-wrapper {
    grid-column-gap: 24px;
    grid-row-gap: 24px;
    flex-flow: column;
    justify-content: flex-start;
    align-items: stretch;
    margin-top: 24px;
  }

  .img-article-main {
    width: 100%;
    height: 200px;
  }

  ._2-col-heading-wrapper.bg-cream {
    padding: 16px;
  }

  .grid-row-1 {
    grid-column-gap: 12px;
    grid-row-gap: 12px;
    flex-flow: column;
    grid-template-rows: auto auto;
    grid-template-columns: 1fr;
    display: flex;
  }

  .flex-v.interview-content {
    grid-column-gap: 8px;
    grid-row-gap: 8px;
  }

  ._2-col-content-block {
    grid-column-gap: 20px;
    grid-row-gap: 20px;
    justify-content: flex-start;
    align-items: stretch;
    padding: 16px;
  }

  .btn-inner-frame {
    grid-column-gap: 4px;
    grid-row-gap: 4px;
    padding: 8px 12px 8px 8px;
  }

  .btn-inner-frame.form {
    font-size: 14px;
  }

  .btn-inner-frame.small {
    display: none;
  }

  .btn-inner-frame.small.mobile {
    display: flex;
  }

  .btn-inner-frame.secondary {
    background-color: #f8ede000;
  }

  .btn-inner-frame.book {
    display: none;
  }

  .btn-inner-frame.is-small {
    padding: 8px 12px 8px 8px;
  }

  .btn-inner-frame.is-filters {
    justify-content: center;
    align-items: center;
    width: 140px;
  }

  .grid-wrapper {
    grid-column-gap: 16px;
    grid-row-gap: 16px;
  }

  .section-no-hpad.about {
    padding-top: 80px;
    padding-bottom: 40px;
  }

  .marq-inner.bg-black {
    padding-top: 12px;
    padding-bottom: 12px;
  }

  .marq-inner.border-nopad {
    padding-top: 16px;
    padding-bottom: 16px;
  }

  .marq-inner.ab.bg-cream {
    padding-top: 12px;
    padding-bottom: 12px;
    display: none;
  }

  .arrow {
    width: 3em;
    height: 3em;
  }

  .marquee-text {
    font-size: 16px;
    font-weight: 400;
  }

  .marquee-text.text-black {
    font-size: 14px;
  }

  .marquee-text.is-italic {
    white-space: nowrap;
    font-size: 28px;
  }

  .marquee-text.is-italic.is-white {
    white-space: nowrap;
  }

  .marquee-wrapper {
    padding-top: 4px;
    padding-bottom: 4px;
  }

  ._2-col-hero {
    flex-flow: column;
    grid-template-rows: auto auto;
    grid-template-columns: 1fr;
    padding-top: 0;
    display: flex;
  }

  .form-column.h-scroll.bg-white.border-no-pad {
    grid-column-gap: 24px;
    grid-row-gap: 24px;
    width: 100%;
    padding: 24px;
  }

  .form-column.contact-page {
    grid-column-gap: 24px;
    grid-row-gap: 24px;
    padding-top: 96px;
    padding-bottom: 40px;
  }

  .form-column.padded.border.space-btw {
    grid-column-gap: 16px;
    grid-row-gap: 16px;
    padding: 24px 16px;
  }

  .form-block {
    padding: 16px;
  }

  .text-field {
    font-size: 14px;
  }

  .textarea {
    margin-bottom: 12px;
    font-size: 14px;
  }

  .contact-img {
    height: 400px;
  }

  .footer-text-ab {
    font-size: 20px;
  }

  .book-hscroll-wrapper {
    grid-column-gap: 4em;
    grid-row-gap: 4em;
    flex-flow: row;
    justify-content: flex-start;
    align-items: stretch;
    width: 504em;
  }

  .hscroll-height, .hscroll-height.about {
    height: auto;
  }

  .sticky-element {
    height: auto;
    position: relative;
  }

  .sticky-element.flex-v.highlights {
    grid-column-gap: 32px;
    grid-row-gap: 32px;
    height: auto;
    padding: 0 16px 80px;
  }

  .sticky-element.flex-v.books {
    padding-left: 0;
  }

  .book-card {
    flex-flow: column;
    justify-content: flex-start;
    width: 80em;
    height: 100%;
  }

  .book-card.home {
    grid-column-gap: 4px;
    grid-row-gap: 4px;
    width: 240px;
    text-decoration: none;
  }

  .book-title-wrapper {
    padding-top: 0;
    padding-bottom: 12px;
  }

  .book-title {
    font-size: 32px;
  }

  .collection-item {
    height: auto;
  }

  .collection-list-wrapper {
    width: 100%;
    padding-left: 16px;
    padding-right: 16px;
  }

  .top-wrapper {
    grid-column-gap: 16px;
    grid-row-gap: 16px;
    width: 100%;
  }

  .h-track {
    flex-flow: column;
    width: 100%;
  }

  .track-flex {
    grid-column-gap: 24px;
    grid-row-gap: 24px;
    flex-flow: column;
    width: 100%;
    margin-right: 0;
  }

  .track-flex.highlights-card {
    grid-column-gap: 12px;
    grid-row-gap: 12px;
    flex-flow: column;
    width: auto;
    height: auto;
  }

  .btm-wrapper.flex-split {
    grid-column-gap: 12px;
    grid-row-gap: 12px;
    justify-content: flex-start;
  }

  .home-hero-container {
    height: auto;
  }

  .home-hero-img {
    width: 100%;
  }

  .hero-content {
    grid-column-gap: 28px;
    grid-row-gap: 28px;
    justify-content: flex-start;
    padding: 96px 16px 64px;
  }

  .action {
    height: auto;
  }

  .action.is-padded {
    grid-column-gap: 4px;
    grid-row-gap: 4px;
    width: 100%;
    height: auto;
    padding-top: 16px;
    padding-left: 0;
    padding-right: 0;
  }

  .home-about-wrapper {
    grid-column-gap: 24px;
    grid-row-gap: 24px;
    width: 100%;
    padding-left: 24px;
  }

  .img-appear-parent {
    width: 36em;
    height: 50em;
    bottom: -5em;
    right: 3em;
  }

  .img-appear-parent.is-mobile {
    width: 184px;
    height: 264px;
  }

  .book-card-wrapper {
    justify-content: flex-start;
  }

  .book-col-home {
    grid-column-gap: 12px;
    grid-row-gap: 12px;
    flex-flow: row;
    grid-template-rows: auto auto;
    grid-template-columns: 1fr 1fr;
    grid-auto-columns: 1fr;
    display: flex;
  }

  .collection-item-2 {
    display: flex;
  }

  .book-collection-wrapper {
    width: 100%;
  }

  .book-collection-wrapper.is-1, .book-collection-wrapper.is-2, .book-collection-wrapper.is-3, .book-collection-wrapper.is-4 {
    width: 240px;
    margin-top: 0;
  }

  .highlights-card {
    grid-column-gap: 6px;
    grid-row-gap: 6px;
    width: 100%;
  }

  .highlight-card-title {
    padding: 12px;
  }

  .date-text {
    font-size: 24px;
  }

  .hlight-card-body {
    border-width: 1px;
    padding: 12px 40px 16px 12px;
  }

  .text-block-4 {
    font-size: 18px;
  }

  .hero-span {
    font-size: 32px;
  }

  .card-whipe-btm.bg-cream.ab-low {
    height: 0%;
  }

  .horrizontal-line.thin {
    background-color: #e5ded7;
    height: 1px;
    margin-top: 4px;
  }

  .article-wrapper {
    grid-column-gap: 0px;
    grid-row-gap: 0px;
    border-bottom-width: .5em;
    border-bottom-color: var(--black);
    flex-flow: column;
    justify-content: center;
    align-items: center;
    padding: 0;
  }

  .article-column-1 {
    grid-column-gap: 10px;
    grid-row-gap: 10px;
    flex-flow: column;
    justify-content: space-between;
    align-items: flex-start;
  }

  .read-article {
    color: var(--brown);
  }

  .article-heading {
    width: 100%;
    font-size: 32px;
    font-weight: 600;
  }

  .article-heading.is-ab {
    font-weight: 600;
    inset: auto auto 0% 0%;
  }

  .burger-menu {
    height: 20px;
  }

  .top-section-wrapper {
    grid-column-gap: 24px;
    grid-row-gap: 24px;
  }

  .img-banner {
    height: 240px;
  }

  .img-banner.banner-about {
    background-position: 0%;
  }

  .mobile-menu {
    padding-left: 24px;
    padding-right: 24px;
    display: none;
  }

  .mobile-nav-link {
    padding-top: 24px;
    padding-bottom: 24px;
  }

  .text-block-5 {
    font-size: 20px;
  }

  .column-v-split.padded.bg-black {
    grid-column-gap: 16px;
    grid-row-gap: 16px;
    justify-content: flex-start;
    padding: 20px 16px;
  }

  .collection-item-3 {
    width: 100%;
    max-width: 100%;
  }

  .image-6 {
    width: 100%;
  }

  .page-count {
    margin-top: 8px;
    font-size: 16px;
    font-weight: 400;
  }

  .hero-100 {
    height: 140vh;
  }

  .hero-100.is-books {
    height: 100vh;
  }

  .hero-100.is-flex-v-c.is-sticky {
    grid-column-gap: 16px;
    grid-row-gap: 16px;
  }

  .hero-h-wrapper {
    grid-column-gap: 0em;
    grid-row-gap: 0em;
    display: none;
  }

  .hero-h-wrapper.is-80 {
    grid-column-gap: 0em;
    grid-row-gap: 0em;
    width: 100%;
    display: none;
  }

  .hero-h-wrapper.is-80.is-mobile {
    display: flex;
  }

  .hero-h-wrapper.is-gallery.is-2.is-mobile {
    grid-column-gap: 1em;
    grid-row-gap: 1em;
    display: flex;
  }

  .hero-h-wrapper.is-gallery.is-1.is-mobile {
    display: flex;
  }

  .hero-h-wrapper.is-gallery-end.is-mobile {
    grid-column-gap: 1em;
    grid-row-gap: 1em;
  }

  .hero-h-wrapper.is-about {
    width: 85%;
    display: none;
  }

  .hero-h-wrapper.is-about.is-mobile {
    z-index: 21;
    grid-column-gap: 0em;
    grid-row-gap: 0em;
    grid-template-rows: auto auto;
    grid-template-columns: 1fr 1fr;
    grid-auto-columns: 1fr;
    display: flex;
    position: relative;
  }

  .hero-h-wrapper.is-mobile {
    grid-column-gap: 0em;
    grid-row-gap: 0em;
    display: flex;
  }

  .hero-h-wrapper.is-tablet {
    display: none;
  }

  .btn-circle {
    width: 8px;
    min-width: 8px;
    height: 8px;
    min-height: 8px;
  }

  .btn-text-wrap {
    padding-top: .4em;
  }

  .btn-outer-frame {
    padding: 2px;
  }

  .btn-outer-frame.is-interview {
    border-width: 1px;
    margin-top: 12px;
  }

  .btn-outer-frame.is-desktop {
    display: none;
  }

  .btn-outer-frame.is-mobile {
    display: flex;
  }

  .hero-img {
    background-position: 71%;
  }

  .hero-img.is-books {
    background-position: 70%;
  }

  .hero-img.is-articles {
    display: none;
  }

  .hero-img.is-articles.is-mobile {
    background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/68634519bb3d6d7e4d1d4780_Articles-Mobile-hero-min.jpg");
    background-position: 100%;
    display: block;
  }

  .hero-img.is-btm.is-interviews {
    display: none;
  }

  .hero-img.is-btm.is-interviews.is-mobile {
    background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/68396321e0eb006fdaa27634_mobile%20int%20-img-min.jpg");
    display: flex;
  }

  .hero-img.is-top.is-interviews {
    display: none;
  }

  .hero-img.is-top.is-interviews.is-mobile {
    z-index: 4;
    background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/68396321e0eb006fdaa27634_mobile%20int%20-img-min.jpg");
    display: flex;
  }

  .d-25 {
    width: 65%;
  }

  .d-30.is-rel-high {
    width: 55%;
  }

  .d-29 {
    width: 80%;
  }

  .flex-split-btm {
    grid-column-gap: 16px;
    grid-row-gap: 16px;
    flex-flow: column;
    justify-content: flex-start;
    align-items: flex-start;
  }

  .art-heading-wrapper {
    width: 95%;
  }

  .paper-div {
    display: none;
  }

  .h-row {
    grid-column-gap: 5px;
    grid-row-gap: 5px;
    flex-flow: wrap;
    justify-content: flex-start;
    align-items: center;
  }

  .h-row.is-mobile {
    display: flex;
  }

  .h-hide {
    height: auto;
    padding-top: 2.8em;
  }

  .h-hide.is-h2-super {
    padding-top: 3.6em;
    padding-left: 1em;
    padding-right: 1em;
  }

  .h-hide.is-hero {
    padding-top: 11px;
  }

  .h-hide.italic.is-cta {
    padding: 3.5em 2.5em 2em 2em;
  }

  .h-hide.is-top {
    margin-bottom: -.5em;
  }

  .h-hide.is-sp {
    padding-top: 4.5em;
    padding-bottom: 1em;
  }

  .h1-italic {
    font-size: 12.5em;
    line-height: .9;
  }

  .h1-italic.is-hero {
    font-size: 14.125em;
    line-height: .8;
  }

  .h-hide-italic {
    padding-top: 1em;
    padding-left: 0;
    padding-right: 2.5em;
  }

  .whipe-r-brown {
    display: block;
  }

  .footer-move {
    justify-content: flex-start;
    align-items: center;
    width: 1288px;
  }

  .about-h-row {
    border-bottom-width: .5em;
    padding: 28px 16px;
  }

  .h-no {
    font-size: 32px;
    display: none;
  }

  .h-no.is-mobile {
    z-index: 21;
    display: block;
    position: absolute;
    inset: 32px 16px auto auto;
  }

  .flex-split-top {
    flex-flow: column;
  }

  .flex-split-top.is-mobile {
    grid-column-gap: 32px;
    grid-row-gap: 32px;
    justify-content: flex-start;
    align-items: flex-start;
  }

  .scroll-bar-wrapper {
    margin-top: 80px;
  }

  .bg-bar {
    height: 4px;
  }

  .scroll-bar {
    height: 8px;
  }

  .flex-center {
    padding-left: 0;
    padding-right: 0;
  }

  .flex-center.is-v.is-home {
    z-index: 5;
    padding-left: 16px;
    padding-right: 16px;
  }

  .about-img-wrapper {
    border-width: .5em;
    border-radius: 2px;
    padding: 4px;
  }

  .about-img-wrapper.is-a-3 {
    width: 90%;
    height: 320px;
    padding: 4px;
  }

  .about-img-wrapper.is-a-4 {
    height: 224px;
  }

  .about-img-wrapper.is-a-2 {
    border-radius: 2px;
    width: 100%;
    height: 240px;
    padding: 4px;
  }

  .about-img-wrapper.is-a-5 {
    width: 100%;
    height: 200px;
  }

  .about-img-wrapper.is-a-6 {
    width: 100%;
    height: 75vh;
  }

  .para-wrapper.is-a-1 {
    width: 80%;
    padding-left: 0;
  }

  .para-wrapper.is-a-2 {
    width: 100%;
    margin-bottom: 32px;
    padding-left: 72px;
    padding-right: 32px;
  }

  .para-wrapper.is-a-4 {
    width: 80%;
    margin-bottom: 16px;
  }

  .d-35 {
    width: 75%;
  }

  .d-35.is-mobile {
    width: 85%;
  }

  .d-55.is-mobile {
    width: 100%;
  }

  .flex-v-left.is-l-pad-3 {
    padding-left: 0;
  }

  .d-40 {
    width: 80%;
  }

  .d-40.is-mobile {
    width: 75%;
  }

  .d-40.is-mobile.is-space-btm {
    margin-bottom: 2em;
  }

  .sticky-section {
    height: 100vh;
    margin-top: -25vh;
  }

  .sticky-section.is-img {
    height: auto;
    margin-top: 0;
    padding-top: 48px;
  }

  .int-img-wrapper {
    border-width: .5em;
    border-radius: 2px;
    width: 100%;
    height: 400px;
    padding: 5px;
  }

  .int-img-wrapper.is-hov-1, .int-img-wrapper.is-hov-2 {
    display: none;
  }

  .flex-v-split.is-right.is-mobile-change {
    grid-column-gap: 12px;
    grid-row-gap: 12px;
    justify-content: flex-start;
    align-items: flex-start;
    margin-top: 0;
  }

  .flex-v-split.is-home-int {
    grid-column-gap: 8px;
    grid-row-gap: 8px;
  }

  .pagination-top {
    border-width: .5em;
    padding: 1em;
  }

  .pagination-no.w--current {
    width: 7em;
    height: 7em;
  }

  .pag-no {
    font-size: 14px;
  }

  .book-col-img-wrapper {
    border-width: .5em;
    border-radius: 2px;
    width: 100%;
    height: 56vh;
    min-height: 56vh;
  }

  .book-content-wrapper {
    width: 100%;
    height: 100%;
    padding-top: 16px;
    padding-left: 0;
  }

  .sticky-container {
    height: auto;
    position: static;
  }

  .flex-c-btm.is-mobile {
    flex-flow: column;
    justify-content: flex-start;
    align-items: flex-start;
    padding-left: 0;
    padding-right: 0;
  }

  .marq-column {
    width: 1056px;
  }

  .text-caps {
    font-size: 15px;
  }

  .text-caps.is-italic.text-white {
    font-size: 18px;
  }

  .text-cap-wrap {
    width: 100px;
  }

  .marquee-circle {
    width: 16px;
    height: 16px;
  }

  .marquee-circle.is-gal-dash {
    width: 8px;
    height: 1px;
  }

  .marquee-circle.bg-white {
    width: 8px;
    height: 8px;
  }

  .marq-set {
    grid-column-gap: 12px;
    grid-row-gap: 12px;
    width: 264px;
    padding-left: 12px;
  }

  .h-italic-wrapper.is-h2-super {
    padding-top: 2em;
    padding-left: 2em;
    padding-right: 2em;
  }

  .h-italic-wrapper.is-h2-super.is-special {
    padding-bottom: 4em;
    padding-left: 2.8em;
  }

  .marquee-track.is-home-cream, .marquee-track.is-home-black {
    padding-top: 5em;
    padding-bottom: 5em;
  }

  .h1-hero {
    font-size: 15em;
    line-height: .55;
  }

  .home-hero-section.is-books {
    height: 100vh;
  }

  .h-light-text-wrap.is-1 {
    padding-left: 16px;
  }

  .d-60 {
    width: 85%;
  }

  .d-36 {
    width: 65%;
  }

  .h2-super-italic, .h2-super-italic.is-black {
    font-size: 20.5em;
  }

  .h2-super, .h2-super.is-black {
    font-size: 21em;
  }

  .gallery-row {
    grid-column-gap: 2vw;
    grid-row-gap: 2vw;
    padding-left: 5vw;
    padding-right: 5vw;
  }

  .gallery-img-wrapper {
    width: 76vw;
    height: 48em;
    display: none;
  }

  .gallery-img-wrapper.is-mobile {
    width: 240px;
    height: 360px;
    display: block;
  }

  .d-70.is-hidden {
    width: 80%;
  }

  .gal-text, .gal-text.is-1 {
    font-size: 16px;
  }

  .grid {
    width: 300%;
  }

  .grid.is-home-gallery {
    width: 310%;
  }

  .grid-row.is-mobile {
    display: flex;
  }

  .article-flex {
    grid-column-gap: 6px;
    grid-row-gap: 6px;
    flex-flow: column;
    justify-content: flex-start;
    align-items: flex-start;
    width: 100%;
    padding: 20px 16px;
  }

  .hero-sub-wrapper {
    width: 75%;
    margin-top: 16px;
  }

  .footer-inner-wrapper {
    padding-top: 0;
  }

  .nav-no {
    font-size: 5em;
  }

  .drop-pop.is-mobile {
    width: 48em;
    height: 30em;
    padding: 1em;
    display: flex;
  }

  .drop-pop.is-mobile.is-2 {
    margin-right: -6em;
  }

  .drop-pop.is-mobile.is-3 {
    margin-left: -16em;
  }

  .drop-pop.is-mobile.is-4.w--current {
    margin-right: -6em;
  }

  .drop-pop.is-mobile.is-5 {
    margin-left: -8em;
  }

  .drop-pop-wrapper {
    display: none;
  }

  .page-bar {
    padding-top: 16px;
    padding-left: 16px;
    padding-right: 16px;
  }

  .h-scroll-bar {
    grid-column-gap: 2em;
    grid-row-gap: 2em;
  }

  .scroll-point {
    border-width: .5em;
    width: 8px;
    height: 8px;
  }

  .scroll-point.is-1, .scroll-point.is-2 {
    width: 8px;
    height: 8px;
  }

  .sroll-line {
    width: 3em;
  }

  .footer-big-txt {
    font-size: 16em;
  }

  .footer-flex {
    grid-column-gap: 12px;
    grid-row-gap: 12px;
    flex-flow: column;
    width: 100%;
    padding-right: 32px;
  }

  .footer-right {
    flex-flow: row;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    padding-top: 1.5em;
  }

  .gtnp-text {
    font-size: 9em;
  }

  .circle-cta-wrapper.is-home {
    height: 120vh;
  }

  .inner-circle-cream {
    width: 68em;
    height: 68em;
  }

  .inner-circle-cream.is-home {
    width: 72em;
    height: 72em;
  }

  .inner-circle-cream.is-articles {
    width: 85em;
    height: 85em;
  }

  .inner-circle-white {
    width: 112em;
    height: 112em;
  }

  .outer-dotted {
    width: 160em;
    height: 160em;
  }

  .rotate-interviews.is-black, .rotate-interviews.is-white {
    width: 152em;
    height: 152em;
  }

  .outer-circle-cream {
    width: 176em;
    height: 176em;
  }

  .inner-dotted {
    width: 92em;
    height: 92em;
  }

  .inner-dotted.is-home {
    width: 62vh;
    height: 62vh;
  }

  .rot-img-wrapper {
    width: 135em;
    height: 135em;
  }

  .rot-img-wrapper.is-home {
    width: 75vh;
    height: 75vh;
  }

  .rot-int-img {
    width: 26em;
    height: 36em;
  }

  .inner-set {
    width: 100em;
    height: 100em;
  }

  .rot-int-wrapper {
    width: 176em;
    height: 176em;
  }

  .rot-int-wrapper.w--current {
    width: 160em;
    height: 160em;
  }

  .hov-circle {
    width: 176em;
    height: 176em;
    display: none;
    inset: auto;
  }

  .int-img-panel.is-left, .int-img-panel.is-right {
    display: none;
  }

  .article-date-wrap {
    width: 18.5%;
  }

  .whipe-cream, .transition {
    display: none;
  }

  .page-intro-text {
    letter-spacing: -6px;
    font-size: 24em;
  }

  .page-intro-text.is-sm {
    font-size: 30em;
  }

  .page-intro-text.is-home {
    font-size: 36em;
  }

  .page-intro-text-wrap {
    justify-content: center;
    align-items: center;
    width: 100%;
    padding-right: 3em;
    display: flex;
  }

  .page-intro-text-wrap.is-sp-home {
    padding-top: 2em;
    padding-bottom: 7em;
    padding-right: 5em;
    display: flex;
  }

  .page-intro-text-wrap.is-sp-home.is-m-hidden {
    display: none;
  }

  .page-intro-text-wrap.is-sp-home-top {
    margin-top: 2em;
    padding-top: 2em;
    padding-bottom: 1em;
    display: flex;
  }

  .page-intro-text-wrap.is-sp-home-top.is-m-hidden {
    display: none;
  }

  .book-intro-img-wrapper {
    width: 36em;
    height: 52em;
  }

  .book-intro-img-wrapper.is-1 {
    width: 36em;
    height: 52em;
    left: 0;
  }

  .book-intro-img-wrapper.is-2 {
    bottom: 32em;
    left: 12em;
  }

  .book-intro-img-wrapper.is-3 {
    top: 56em;
    right: 4em;
  }

  .book-intro-img-wrapper.is-4 {
    bottom: -4em;
  }

  .trans-box-out {
    padding: 12px;
  }

  .article-intro-div.is-white {
    width: 64em;
    padding: 6em;
  }

  .article-intro-div.is-white.is-1, .article-intro-div.is-white.is-2 {
    width: 64em;
  }

  .article-intro-div.is-white.is-ab-1 {
    width: 64em;
    bottom: 32em;
  }

  .article-intro-div.is-white.is-ab-2 {
    width: 64em;
    top: 48em;
    left: -4em;
    right: 0;
  }

  .article-intro-div.is-black {
    width: 64em;
    padding: 6em;
  }

  .article-intro-div.is-black.is-1 {
    width: 64em;
    bottom: 32em;
    left: -6em;
  }

  .article-intro-div.is-black.is-2 {
    width: 64em;
    top: 32em;
    right: -4em;
  }

  .article-intro-div-text, .article-intro-div-text.is-white {
    font-size: 8em;
  }

  .int-intro-img-div {
    width: 55em;
    height: 35em;
  }

  .int-intro-img-div.is-1 {
    top: 18em;
  }

  .int-intro-img-div.is-2 {
    bottom: 48em;
    left: 10em;
  }

  .int-intro-img-div.is-3 {
    top: 48em;
    right: 6em;
  }

  .int-intro-img-div.is-gallery-1 {
    top: -4em;
    left: -8em;
  }

  .int-intro-img-div.is-gallery-3 {
    top: 14em;
    right: -8em;
  }

  .int-intro-img-div.is-gallery-2 {
    bottom: 28em;
    left: -4em;
  }

  .int-intro-img-div.is-gallery-4 {
    bottom: -8em;
    right: -4em;
  }

  .int-intro-img-div.is-ab-2 {
    bottom: 4em;
    right: 6em;
  }

  .int-intro-img-div.is-ab-3 {
    top: 32em;
  }

  .transing-out-box {
    padding: 12px;
  }

  .div-block-11 {
    grid-column-gap: 2em;
    grid-row-gap: 2em;
  }

  .circle-cta-text {
    font-size: 19em;
  }

  .circle-cta-text.is-italic {
    font-size: 18em;
  }

  .div-block-12 {
    grid-column-gap: 4.5em;
    grid-row-gap: 4.5em;
  }

  .btn-outer-cta.is-big {
    border-width: 2px;
  }

  .h1 {
    margin-top: 0;
    margin-bottom: 0;
  }

  .heading-9 {
    line-height: .85;
  }

  .home-int-div {
    padding: 4px;
  }

  .home-int-div.is-1 {
    grid-column-gap: 16px;
    grid-row-gap: 16px;
    flex-flow: column;
    width: 68em;
    padding: 6px 6px 16px;
  }

  .home-int-img-wrapper {
    height: 156px;
  }

  .int-set.is-1 {
    width: 68em;
  }

  .int-set.is-2 {
    width: 68em;
    top: 140em;
    right: 16px;
  }

  .int-set.is-3 {
    width: 68em;
    top: 265em;
    left: 40px;
  }

  .int-set.is-4 {
    width: 68em;
    top: 400em;
    right: 16px;
  }

  .h3-iitalic {
    font-size: 8em;
    line-height: 1;
  }

  .d-50 {
    width: 80%;
  }

  .marquee-cont.is-left {
    height: 96em;
    margin-top: -48px;
  }

  .marquee-cont.is-right {
    height: 120em;
  }

  .marquee-cream {
    top: -80px;
    left: -20px;
    transform: translate(-15em, -24em)rotate(-20deg);
  }

  .marquee-black {
    transform: translate(370px, -180px)rotate(30deg);
  }

  .footer-split {
    justify-content: space-between;
    align-items: flex-start;
    width: 100%;
    display: flex;
  }

  .footer-img-wrapper {
    border: 1px solid var(--black);
    background-color: var(--cream);
    justify-content: center;
    align-items: center;
    width: 48%;
    padding: 4px;
    display: flex;
  }

  .footer-img {
    background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/67ea2c80f6c2d761159d7b07_Nav%20img%20-%205-min.jpg");
    background-position: 50%;
    background-repeat: no-repeat;
    background-size: cover;
    width: 120px;
  }

  .footer-img.is-articles {
    background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/67ea2c8130d76b0505b8c49e_Nav%20img%20-%204-min.jpg");
    width: 100%;
  }

  .heading-10 {
    line-height: .6;
  }

  .mobile-menu-container {
    flex-flow: column;
    justify-content: flex-start;
    align-items: center;
    display: none;
  }

  .nav-block {
    grid-column-gap: 8em;
    grid-row-gap: 8em;
    width: 100%;
    height: 18%;
    padding-left: 16px;
    padding-right: 16px;
  }

  .nav-block.is-1 {
    grid-column-gap: 8em;
    grid-row-gap: 8em;
    justify-content: center;
    align-items: center;
  }

  .nav-block.is-2 {
    grid-column-gap: 8em;
    grid-row-gap: 8em;
    justify-content: flex-start;
    align-items: center;
  }

  .nav-block.is-3 {
    grid-column-gap: 8em;
    grid-row-gap: 8em;
    justify-content: flex-end;
    align-items: center;
  }

  .nav-block.is-4, .nav-block.is-5 {
    grid-column-gap: 8em;
    grid-row-gap: 8em;
  }

  .banner-img.is-interviews {
    background-position: 60%;
    display: none;
  }

  .banner-img.is-interviews.is-mobile {
    background-image: url("https://cdn.prod.website-files.com/6787c53671a97b8708dd7000/683963217d80a2f677905b59_mobile%20int%20-img-1-min.jpg");
    display: none;
  }



  .d-9.is-ab-top {
    width: 50%;
  }

  .rot-int-img-wrap {
    width: 140px;
    height: 96px;
    padding: 3px;
  }

  .rot-int-img-wrap.is-2 {
    top: 16.5em;
  }

  .rot-int-img-wrap.is-3 {
    right: -4em;
  }

  .rot-int-img-wrap.is-4, .rot-int-img-wrap.is-6 {
    bottom: 16.5em;
  }

  .rot-int-img-wrap.is-7 {
    left: -4em;
  }

  .rot-int-img-wrap.is-8 {
    top: 16.5em;
  }

  .mobile-line {
    background-color: var(--brown);
    width: 100%;
    height: 1px;
    padding-bottom: 0;
    padding-right: 0;
    display: block;
  }

  .text-block-7, .text-block-8 {
    color: var(--black);
  }

  .gallery-marquee-container {
    width: 160px;
    height: 160px;
  }

  .article-fall-container, .article-fall-container.is-logos {
    height: 60vh;
  }

  .article-fall-container.is-home-articles {
    height: 70vh;
  }

  .article-fall-card {
    padding: 4em;
  }

  .article-fall-card.is-3.is-fall, .article-fall-card.is-3.is-def {
    left: 26em;
  }

  .article-fall-card.is-2.is-fall, .article-fall-card.is-2.is-def {
    left: -8em;
  }

  .article-fall-card.is-4.is-fall, .article-fall-card.is-4.is-def {
    bottom: 116px;
  }

  .article-fall-card.is-5.is-fall, .article-fall-card.is-5.is-def {
    bottom: 80px;
  }

  .logo-card.is-1 {
    width: 85%;
    height: 85%;
  }

  .logo-card.is-3.is-fall, .logo-card.is-3.is-def {
    z-index: 3;
    width: 148px;
    height: 60px;
  }

  .logo-card-wrap.is-2.is-fall, .logo-card-wrap.is-2.is-def {
    z-index: 2;
    bottom: 90px;
    left: -2em;
  }

  .logo-card-wrap.is-1.is-fall, .logo-card-wrap.is-1.is-def {
    width: 144px;
    height: 56px;
    bottom: 176px;
    right: 32px;
  }

  .logo-card-wrap.is-h-1.is-def, .logo-card-wrap.is-h-1.is-fall {
    right: 262px;
  }

  .logo-card-wrap.is-h-2.is-def, .logo-card-wrap.is-h-2.is-fall {
    right: 130px;
  }

  .logo-card-wrap.is-h-3.is-def, .logo-card-wrap.is-h-3.is-fall {
    right: -30px;
  }

  .logo-card-wrap.is-h-4.is-def, .logo-card-wrap.is-h-4.is-fall {
    bottom: 65px;
    right: 202px;
  }

  .logo-card-wrap.is-h-5.is-def, .logo-card-wrap.is-h-5.is-fall {
    right: 88px;
  }

  .logo-card-wrap.is-h-6.is-def, .logo-card-wrap.is-h-6.is-fall {
    right: 20px;
  }

  .logo-card-wrap.is-h-7.is-def, .logo-card-wrap.is-h-7.is-fall {
    bottom: 112px;
    right: 155px;
  }

  .logo-card-wrap.is-int-1 {
    border-width: 1px;
    width: 44em;
    height: 13em;
    bottom: 3em;
    left: -20em;
  }

  .logo-card-wrap.is-int-1.is-fall {
    width: 44em;
    height: 13em;
    right: -8.2em;
  }

  .logo-card-wrap.is-int-2 {
    border-width: 1px;
    width: 48em;
    height: 13em;
    bottom: -.4em;
    left: 24.5em;
    right: auto;
  }

  .logo-card-wrap.is-int-2.is-fall {
    height: 13em;
  }

  .logo-card-wrap.is-int-3 {
    border-width: 1px;
    width: 24em;
    height: 13em;
    bottom: 30em;
    left: auto;
    right: 24em;
  }

  .logo-card-wrap.is-int-7 {
    border-width: 1px;
    width: 32em;
    height: 12em;
    bottom: 16.5em;
    right: -1em;
  }

  .logo-card-wrap.is-int-5 {
    border-width: 1px;
    width: 30em;
    height: 13em;
    bottom: 21em;
    left: -3em;
    right: auto;
  }

  .logo-card-wrap.is-int-4 {
    border-width: 1px;
    width: 48em;
    height: 12em;
    bottom: 1em;
    right: -20.5em;
  }

  .logo-card-wrap.is-int-6 {
    border-width: 1px;
    width: 44em;
    height: 12em;
    bottom: 14em;
    left: auto;
    right: 32em;
  }

  .logo-card-wrap.is-int-8 {
    border-width: 1px;
    width: 26em;
    height: 13em;
    bottom: 31em;
    left: 25em;
    right: auto;
  }

  .page-no-wrap {
    padding: 7px 12px 6px;
  }

  .page-no-wrap.is-tip {
    padding-left: 12px;
    padding-right: 12px;
  }

  .cursor-parent {
    display: none;
  }

  .btn-outer-cta-external {
    bottom: 56px;
    left: 16px;
    right: auto;
  }

  .btn-outer-cta-external.not-ab {
    padding: 2px;
  }

  .int-date-container {
    justify-content: center;
    align-items: flex-end;
    margin-bottom: -10em;
    padding-right: 8em;
    transition: opacity .2s;
  }

  .int-date {
    font-size: 70em;
  }



  .banner-marquee-col {
    padding-top: 8px;
    padding-bottom: 4px;
  }

  .banner-sm-img-wrap {
    width: 36em;
    height: 48em;
    padding: 4px;
  }

  .banner-sm-img-wrap.is-2 {
    width: 56em;
    height: 72em;
    padding: 4px;
  }

  .banner-sm-img-wrap.is-3 {
    width: 44em;
    height: 56em;
    top: 120px;
    right: -12px;
  }

  .banner-sm-img-wrap.is-1 {
    width: 32em;
    height: 44em;
    top: 52px;
    left: -12px;
  }



  .publication-text {
    position: absolute;
  }

  .filter-panel {
    width: 100%;
    padding: 16px;
  }

  .form {
    flex-flow: column;
  }

  .filter-inner {
    width: 100%;
  }

  .filter-inner.is-mobile-hide {
    display: none;
  }

  .article-pagination {
    padding-bottom: 120px;
  }

  .filter-panel--out.is-btm {
    padding-bottom: 40px;
    display: flex;
    inset: auto 0% 0%;
  }

  .page-btn-collection {
    font-size: 3em;
  }

  .int-card-wrapper {
    height: 500vh;
  }

  .home-intro-marquee-box {
    width: 180em;
  }

  .page-intro-main-box {
    display: none;
  }

  .page-intro-main-box.is-mobile {
    display: flex;
  }

  .page-tip-wrapper {
    justify-content: flex-start;
    align-items: flex-end;
    padding-top: 16px;
    padding-left: 16px;
    display: none;
  }

  .image-carousel-mobile {
    grid-column-gap: 8px;
    grid-row-gap: 8px;
    justify-content: center;
    align-items: center;
    min-width: auto;
  }

  .image-carousel-wrapper {
    padding-bottom: 5em;
  }
}

#w-node-cf7e7324-f323-da18-e0ea-5e21b4a91a40-08dd6ffc {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

@media screen and (max-width: 991px) {
  #w-node-_71b3ccf8-2e5e-4785-7ecd-7b3d5ab32ef4-08dd6ff4, #w-node-_88b8edde-dc24-7e76-4179-3612e00e144d-08dd6ff4, #w-node-_81d3b725-19fd-b0c8-b1a9-fcd0b8fb0ef5-08dd6ff4, #w-node-e0cb3584-c9c5-5972-8903-b9f5463ce838-08dd6ff4 {
    grid-area: span 2 / span 1 / span 2 / span 1;
  }
}
