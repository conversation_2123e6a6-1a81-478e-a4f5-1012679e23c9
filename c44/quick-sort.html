<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8"/>
    <title>高效排序算法：双边快速排序的分治思想与优化策略详解 | Java技术博客</title>
    <meta content="深入解析快速排序算法，从单边循环到双边循环，包含随机基准点和重复值处理的完整优化策略。"
          name="description"/>
    <meta content="高效排序算法：双边快速排序的分治思想与优化策略详解 | Java技术博客" property="og:title"/>
    <meta content="深入解析快速排序算法，从单边循环到双边循环，包含随机基准点和重复值处理的完整优化策略。"
          property="og:description"/>
    <meta content="width=device-width, initial-scale=1" name="viewport"/>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, 'Microsoft YaHei', sans-serif;
            line-height: 1.8;
            color: #252321;
            background-color: #f6f4f1;
            overflow-x: hidden;
        }
        
        .nav-back {
            position: fixed;
            top: 30px;
            left: 30px;
            z-index: 1000;
        }
        
        .nav-back a {
            display: inline-flex;
            align-items: center;
            padding: 12px 24px;
            background: rgba(37, 35, 33, 0.9);
            color: #f6f4f1;
            text-decoration: none;
            border-radius: 30px;
            font-weight: 500;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            font-size: 14px;
        }
        
        .nav-back a:hover {
            background: #252321;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(37, 35, 33, 0.3);
        }
        
        .page-header {
            background: white;
            padding: 100px 20px 60px 20px;
            text-align: center;
            border-bottom: 1px solid rgba(188, 136, 74, 0.1);
        }
        
        .page-title {
            font-size: clamp(2rem, 4vw, 3rem);
            font-weight: 700;
            margin-bottom: 15px;
            color: #252321;
            line-height: 1.2;
        }
        
        .page-subtitle {
            font-size: clamp(1rem, 2vw, 1.2rem);
            color: #666;
            margin-bottom: 20px;
            font-weight: 400;
        }
        
        .page-date {
            font-size: 0.9rem;
            color: #bc884a;
            font-weight: 500;
            display: inline-block;
            padding: 8px 16px;
            background: rgba(188, 136, 74, 0.1);
            border-radius: 20px;
        }
        
        .content-section {
            max-width: 1000px;
            margin: 0 auto;
            padding: 80px 20px;
        }
        
        .section-title {
            font-size: 2.5rem;
            color: #252321;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
        }
        
        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #bc884a, #8b6914);
            border-radius: 2px;
        }
        
        .algorithm-section {
            background: linear-gradient(135deg, rgba(52, 152, 219, 0.05) 0%, rgba(41, 128, 185, 0.05) 100%);
            padding: 40px;
            border-radius: 20px;
            margin: 40px 0;
            border-left: 5px solid #3498db;
        }
        
        .optimization-section {
            background: linear-gradient(135deg, rgba(155, 89, 182, 0.05) 0%, rgba(142, 68, 173, 0.05) 100%);
            padding: 40px;
            border-radius: 20px;
            margin: 40px 0;
            border-left: 5px solid #9b59b6;
        }
        
        .overview-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 40px;
            margin: 60px 0;
        }
        
        .overview-card {
            background: white;
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(37, 35, 33, 0.1);
            transition: all 0.3s ease;
            border: 1px solid rgba(188, 136, 74, 0.1);
        }
        
        .overview-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(37, 35, 33, 0.15);
        }
        
        .card-title {
            font-size: 1.5rem;
            color: #252321;
            margin-bottom: 20px;
            font-weight: 600;
        }
        
        .card-content {
            color: #666;
            line-height: 1.8;
        }
        
        .steps-list {
            list-style: none;
            counter-reset: step-counter;
        }
        
        .steps-list li {
            counter-increment: step-counter;
            margin: 20px 0;
            padding: 20px;
            background: rgba(52, 152, 219, 0.05);
            border-radius: 10px;
            position: relative;
            padding-left: 60px;
        }
        
        .steps-list li::before {
            content: counter(step-counter);
            position: absolute;
            left: 20px;
            top: 50%;
            transform: translateY(-50%);
            background: #3498db;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 14px;
        }
        
        .code-section {
            margin: 60px 0;
        }
        
        .code-container {
            background: #1e1e1e;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            margin: 30px 0;
        }
        
        .code-header {
            background: #2d2d2d;
            padding: 15px 20px;
            color: #ccc;
            font-size: 14px;
            font-weight: 500;
            border-bottom: 1px solid #444;
        }
        
        .code-block {
            margin: 0;
            padding: 30px;
            background: #1e1e1e;
            color: #d4d4d4;
            font-family: 'Fira Code', 'Monaco', 'Consolas', monospace;
            font-size: 14px;
            line-height: 1.6;
            overflow-x: auto;
            white-space: pre-wrap;
        }
        
        .highlight {
            background: rgba(188, 136, 74, 0.1);
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: 600;
            color: #bc884a;
        }
        
        .visual-demo {
            background: white;
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(37, 35, 33, 0.1);
            margin: 40px 0;
            text-align: center;
        }
        
        .array-visualization {
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 30px 0;
            flex-wrap: wrap;
            gap: 10px;
        }
        
        .array-element {
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-weight: bold;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        
        .pivot {
            background: #e74c3c;
            color: white;
            border-color: #c0392b;
        }
        
        .left-pointer {
            background: #3498db;
            color: white;
            border-color: #2980b9;
        }
        
        .right-pointer {
            background: #2ecc71;
            color: white;
            border-color: #27ae60;
        }
        
        .normal {
            background: #f8f9fa;
            color: #333;
        }
        
        .back-to-top {
            position: fixed;
            bottom: 30px;
            right: 30px;
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #bc884a, #8b6914);
            color: white;
            border: none;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            box-shadow: 0 4px 12px rgba(188, 136, 74, 0.3);
            transition: all 0.3s ease;
            opacity: 0;
            visibility: hidden;
            z-index: 1000;
        }
        
        .back-to-top.visible {
            opacity: 1;
            visibility: visible;
        }
        
        .back-to-top:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 16px rgba(188, 136, 74, 0.4);
        }
        
        .footer-section {
            background: linear-gradient(135deg, #252321 0%, #3a3632 100%);
            color: white;
            text-align: center;
            padding: 60px 20px;
            margin-top: 80px;
        }
        
        .footer-content p {
            margin: 10px 0;
            opacity: 0.9;
        }
        
        .footer-content p:first-child {
            font-size: 1.1rem;
            font-weight: 500;
        }
        
        .footer-content p:last-child {
            font-size: 0.9rem;
            opacity: 0.7;
        }
        
        @media (max-width: 768px) {
            .nav-back {
                top: 15px;
                left: 15px;
            }
            
            .nav-back a {
                padding: 10px 18px;
                font-size: 13px;
            }
            
            .page-header {
                padding: 80px 15px 40px 15px;
            }
            
            .content-section {
                padding: 40px 15px;
            }
            
            .section-title {
                font-size: 2rem;
                margin-bottom: 25px;
            }
            
            .overview-grid {
                grid-template-columns: 1fr;
                gap: 25px;
                margin: 40px 0;
            }
            
            .overview-card {
                padding: 25px 20px;
            }
            
            .algorithm-section,
            .optimization-section {
                padding: 25px 20px;
                margin: 30px 0;
            }
            
            .code-block {
                font-size: 12px;
                padding: 20px;
            }
            
            .array-element {
                width: 40px;
                height: 40px;
                font-size: 14px;
            }
            
            .back-to-top {
                bottom: 20px;
                right: 20px;
                width: 45px;
                height: 45px;
                font-size: 18px;
            }
        }
    </style>
</head>
<body>
    <nav class="nav-back">
        <a href="index.html">← 返回首页</a>
    </nav>

    <header class="page-header">
        <h1 class="page-title">高效排序算法：双边快速排序</h1>
        <p class="page-subtitle">分治思想与优化策略详解</p>
        <div class="page-date">2025-01-08</div>
    </header>

    <main class="content-section">
        <section id="overview">
            <h2 class="section-title">快速排序概述</h2>
            
            <div class="overview-grid">
                <div class="overview-card">
                    <h3 class="card-title">算法特点</h3>
                    <div class="card-content">
                        <ul style="list-style-type: disc; margin-left: 20px;">
                            <li><span class="highlight">分治思想</span>：将大问题分解为小问题</li>
                            <li><span class="highlight">原地排序</span>：空间复杂度 O(log n)</li>
                            <li><span class="highlight">不稳定排序</span>：相等元素可能改变相对位置</li>
                            <li><span class="highlight">平均时间复杂度</span>：O(n log n)</li>
                        </ul>
                    </div>
                </div>
                
                <div class="overview-card">
                    <h3 class="card-title">核心思想</h3>
                    <div class="card-content">
                        <p>快速排序采用<span class="highlight">分治策略</span>：</p>
                        <br>
                        <ol style="margin-left: 20px;">
                            <li>选择一个<span class="highlight">基准点（pivot）</span></li>
                            <li>将数组分为两部分：小于基准点和大于基准点</li>
                            <li>递归地对两部分进行排序</li>
                            <li>合并结果（无需额外操作）</li>
                        </ol>
                    </div>
                </div>
            </div>
        </section>

        <section id="lomuto-partition">
            <h2 class="section-title">单边循环（Lomuto分区）</h2>

            <div class="algorithm-section">
                <h3 class="card-title">算法要点</h3>
                <ol class="steps-list">
                    <li>选择<span class="highlight">最右侧元素</span>作为基准点</li>
                    <li>j 找比基准点小的，i 找比基准点大的，一旦找到，二者进行交换</li>
                    <li><span class="highlight">交换时机</span>：j 找到小的，且与 i 不相等</li>
                    <li>i 找到 >= 基准点元素后，不应自增</li>
                    <li>最后基准点与 i 交换，i 即为基准点最终索引</li>
                </ol>
            </div>

            <div class="visual-demo">
                <h3 class="card-title">执行过程演示</h3>
                <p style="margin-bottom: 20px;">以数组 [5, 3, 7, 2, 9, 8, 1, 4] 为例，基准点为 4：</p>

                <div class="demo-step">
                    <p><strong>步骤1：</strong>i 找到比基准点大的5，j找到比基准点小的2，交换</p>
                    <div class="array-visualization">
                        <div class="array-element left-pointer">5</div>
                        <div class="array-element normal">3</div>
                        <div class="array-element normal">7</div>
                        <div class="array-element right-pointer">2</div>
                        <div class="array-element normal">9</div>
                        <div class="array-element normal">8</div>
                        <div class="array-element normal">1</div>
                        <div class="array-element pivot">4</div>
                    </div>
                    <p style="margin-top: 10px; color: #666;">↓ 交换后</p>
                    <div class="array-visualization">
                        <div class="array-element normal">2</div>
                        <div class="array-element normal">3</div>
                        <div class="array-element normal">7</div>
                        <div class="array-element normal">5</div>
                        <div class="array-element normal">9</div>
                        <div class="array-element normal">8</div>
                        <div class="array-element normal">1</div>
                        <div class="array-element pivot">4</div>
                    </div>
                </div>

                <div class="demo-step" style="margin-top: 30px;">
                    <p><strong>步骤2：</strong>继续查找，i 找到7，j 找到3，交换</p>
                    <div class="array-visualization">
                        <div class="array-element normal">2</div>
                        <div class="array-element right-pointer">3</div>
                        <div class="array-element left-pointer">7</div>
                        <div class="array-element normal">5</div>
                        <div class="array-element normal">9</div>
                        <div class="array-element normal">8</div>
                        <div class="array-element normal">1</div>
                        <div class="array-element pivot">4</div>
                    </div>
                </div>

                <div class="demo-step" style="margin-top: 30px;">
                    <p><strong>最终：</strong>j 到达 right 处结束，基准点与 i 交换</p>
                    <div class="array-visualization">
                        <div class="array-element normal">2</div>
                        <div class="array-element normal">3</div>
                        <div class="array-element normal">1</div>
                        <div class="array-element pivot">4</div>
                        <div class="array-element normal">9</div>
                        <div class="array-element normal">8</div>
                        <div class="array-element normal">7</div>
                        <div class="array-element normal">5</div>
                    </div>
                    <p style="margin-top: 10px; color: #2ecc71; font-weight: 500;">分区完成！基准点4左边都小于4，右边都大于4</p>
                </div>
            </div>

            <div class="code-container">
                <div class="code-header">QuickSortLomuto.java</div>
                <pre class="code-block"><code>public class QuickSortLomuto {

    public static void sort(int[] a) {
        quick(a, 0, a.length - 1);
    }

    private static void quick(int[] a, int left, int right) {
        if (left >= right) {
            return;
        }
        int p = partition(a, left, right); // p代表基准点元素索引
        quick(a, left, p - 1);
        quick(a, p + 1, right);
    }

    private static int partition(int[] a, int left, int right) {
        int pv = a[right]; // 基准点元素值
        int i = left;
        int j = left;
        while (j < right) {
            if (a[j] < pv) { // j 找到比基准点小的了
                if (i != j) {
                    swap(a, i, j);
                }
                i++;
            }
            j++;
        }
        swap(a, i, right);
        return i;
    }

    private static void swap(int[] a, int i, int j) {
        int t = a[i];
        a[i] = a[j];
        a[j] = t;
    }

    public static void main(String[] args) {
        int[] a = {5, 3, 7, 2, 9, 8, 1, 4};
        System.out.println(Arrays.toString(a));
        sort(a);
        System.out.println(Arrays.toString(a));
    }
}</code></pre>
            </div>
        </section>

        <section id="hoare-partition">
            <h2 class="section-title">双边循环（Hoare分区）</h2>

            <div class="algorithm-section">
                <h3 class="card-title">算法要点</h3>
                <ol class="steps-list">
                    <li>选择<span class="highlight">最左侧元素</span>作为基准点</li>
                    <li>j 找比基准点小的，i 找比基准点大的，一旦找到，二者进行交换</li>
                    <li><span class="highlight">i 从左向右</span>查找</li>
                    <li><span class="highlight">j 从右向左</span>查找</li>
                    <li>最后基准点与 i 交换，i 即为基准点最终索引</li>
                </ol>
            </div>

            <div class="visual-demo">
                <h3 class="card-title">执行过程演示</h3>
                <p style="margin-bottom: 20px;">以数组 [9, 3, 7, 2, 8, 5, 1, 4] 为例，基准点为 9：</p>

                <div class="demo-step">
                    <p><strong>步骤1：</strong>i 找到比基准点大的（无），j 找到比基准点小的1，交换</p>
                    <div class="array-visualization">
                        <div class="array-element pivot">9</div>
                        <div class="array-element normal">3</div>
                        <div class="array-element normal">7</div>
                        <div class="array-element normal">2</div>
                        <div class="array-element normal">8</div>
                        <div class="array-element normal">5</div>
                        <div class="array-element right-pointer">1</div>
                        <div class="array-element normal">4</div>
                    </div>
                </div>

                <div class="demo-step" style="margin-top: 30px;">
                    <p><strong>最终：</strong>i == j 时退出循环，基准点与 j 交换</p>
                    <div class="array-visualization">
                        <div class="array-element normal">1</div>
                        <div class="array-element normal">3</div>
                        <div class="array-element normal">7</div>
                        <div class="array-element normal">2</div>
                        <div class="array-element normal">8</div>
                        <div class="array-element normal">5</div>
                        <div class="array-element normal">4</div>
                        <div class="array-element pivot">9</div>
                    </div>
                    <p style="margin-top: 10px; color: #2ecc71; font-weight: 500;">分区完成！基准点9左边都小于9，右边都大于9</p>
                </div>
            </div>

            <div class="code-container">
                <div class="code-header">QuickSortHoare.java</div>
                <pre class="code-block"><code>public class QuickSortHoare {

    public static void sort(int[] a) {
        quick(a, 0, a.length - 1);
    }

    private static void quick(int[] a, int left, int right) {
        if (left >= right) {
            return;
        }
        int p = partition(a, left, right);
        quick(a, left, p - 1);
        quick(a, p + 1, right);
    }

    private static int partition(int[] a, int left, int right) {
        int i = left;
        int j = right;
        int pv = a[left];
        while (i < j) {
            while (i < j && a[j] > pv) {
                j--;
            }
            while (i < j && pv >= a[i]) {
                i++;
            }
            swap(a, i, j);
        }
        swap(a, left, j);
        return j;
    }

    private static void swap(int[] a, int i, int j) {
        int t = a[i];
        a[i] = a[j];
        a[j] = t;
    }

    public static void main(String[] args) {
        int[] a = {9, 3, 7, 2, 8, 5, 1, 4};
        System.out.println(Arrays.toString(a));
        sort(a);
        System.out.println(Arrays.toString(a));
    }
}</code></pre>
            </div>
        </section>

        <section id="optimizations">
            <h2 class="section-title">优化策略</h2>

            <div class="overview-grid">
                <div class="overview-card">
                    <h3 class="card-title">随机基准点</h3>
                    <div class="card-content">
                        <p>使用<span class="highlight">随机数</span>作为基准点，避免万一最大值或最小值作为基准点导致的分区不均衡。</p>
                        <br>
                        <p style="color: #e74c3c; font-weight: 500;">问题：如果数组已经有序，选择固定位置的基准点会导致最坏情况 O(n²)</p>
                        <br>
                        <p style="color: #2ecc71; font-weight: 500;">解决：随机选择基准点，期望时间复杂度仍为 O(n log n)</p>
                    </div>
                </div>

                <div class="overview-card">
                    <h3 class="card-title">处理重复值</h3>
                    <div class="card-content">
                        <p>如果重复值较多，原算法的分区效果不好，需要改进分区策略。</p>
                        <br>
                        <p><span class="highlight">核心思想</span>：</p>
                        <ul style="list-style-type: disc; margin-left: 20px;">
                            <li>改进前：i 只找大于的，j 找小于等于的</li>
                            <li>改进后：二者都找等于的交换</li>
                            <li>等于的值会平衡分布在基准点两边</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="optimization-section">
                <h3 class="card-title">随机基准点实现</h3>
                <div class="code-container">
                    <div class="code-header">随机基准点优化</div>
                    <pre class="code-block"><code>// 在 partition 方法开始时添加
int idx = ThreadLocalRandom.current().nextInt(right - left + 1) + left;
swap(a, idx, left);</code></pre>
                </div>
                <p style="margin-top: 20px; color: #666;">这样可以避免在已排序数组上的最坏情况，提高算法的鲁棒性。</p>
            </div>

            <div class="optimization-section">
                <h3 class="card-title">处理重复值的完整实现</h3>
                <div class="code-container">
                    <div class="code-header">QuickSortHandleDuplicate.java</div>
                    <pre class="code-block"><code>public class QuickSortHandleDuplicate {

    public static void sort(int[] a) {
        quick(a, 0, a.length - 1);
    }

    private static void quick(int[] a, int left, int right) {
        if (left >= right) {
            return;
        }
        int p = partition(a, left, right);
        quick(a, left, p - 1);
        quick(a, p + 1, right);
    }

    /*
        循环内
            i 从 left + 1 开始，从左向右找大的或相等的
            j 从 right 开始，从右向左找小的或相等的
            交换，i++ j--

        循环外 j 和 基准点交换，j 即为分区位置
     */
    private static int partition(int[] a, int left, int right) {
        int idx = ThreadLocalRandom.current().nextInt(right - left + 1) + left;
        swap(a, left, idx);
        int pv = a[left];
        int i = left + 1;
        int j = right;
        while (i <= j) {
            // i 从左向右找大的或者相等的
            while (i <= j && a[i] < pv) {
                i++;
            }
            // j 从右向左找小的或者相等的
            while (i <= j && a[j] > pv) {
                j--;
            }
            if (i <= j) {
                swap(a, i, j);
                i++;
                j--;
            }
        }
        swap(a, j, left);
        return j;
    }

    private static void swap(int[] a, int i, int j) {
        int t = a[i];
        a[i] = a[j];
        a[j] = t;
    }
}</code></pre>
                </div>
            </div>

            <div class="overview-card">
                <h3 class="card-title">重复值处理的关键细节</h3>
                <div class="card-content">
                    <ul style="list-style-type: disc; margin-left: 20px;">
                        <li>因为一开始 i 就可能等于 j，因此外层循环需要加<span class="highlight">等于条件</span>保证至少进入一次</li>
                        <li>内层 while 循环中 <span class="highlight">i <= j</span> 的 = 也不能去掉，因为 i == j 时也要做一次与基准点的判断</li>
                        <li>i == j 时，也要做一次 i++ 和 j-- 使下次循环二者不等才能退出</li>
                        <li>因为最后退出循环时 i 会大于 j，因此最终与基准点交换的是 <span class="highlight">j</span></li>
                        <li>内层两个 while 循环的先后顺序不再重要</li>
                    </ul>
                </div>
            </div>

            <div class="visual-demo">
                <h3 class="card-title">重复值分区效果对比</h3>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px; margin: 30px 0;">
                    <div>
                        <p style="text-align: center; color: #e74c3c; font-weight: 500; margin-bottom: 15px;">改进前：分区不均衡</p>
                        <div class="array-visualization" style="justify-content: center;">
                            <div class="array-element normal">2</div>
                            <div class="array-element normal">2</div>
                            <div class="array-element normal">2</div>
                            <div class="array-element pivot">4</div>
                            <div class="array-element normal">4</div>
                            <div class="array-element normal">4</div>
                            <div class="array-element normal">4</div>
                            <div class="array-element normal">6</div>
                        </div>
                    </div>
                    <div>
                        <p style="text-align: center; color: #2ecc71; font-weight: 500; margin-bottom: 15px;">改进后：分区均衡</p>
                        <div class="array-visualization" style="justify-content: center;">
                            <div class="array-element normal">2</div>
                            <div class="array-element normal">4</div>
                            <div class="array-element normal">2</div>
                            <div class="array-element pivot">4</div>
                            <div class="array-element normal">4</div>
                            <div class="array-element normal">6</div>
                            <div class="array-element normal">4</div>
                            <div class="array-element normal">2</div>
                        </div>
                    </div>
                </div>
                <p style="text-align: center; color: #666; margin-top: 20px;">通过让相等元素平衡分布，避免了分区极度不均衡的情况</p>
            </div>
        </section>

        <section id="summary">
            <h2 class="section-title">总结</h2>

            <div class="overview-card">
                <h3 class="card-title">快速排序的优势与特点</h3>
                <div class="card-content">
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 30px;">
                        <div>
                            <h4 style="color: #2ecc71; margin-bottom: 10px;">✅ 优势</h4>
                            <ul style="list-style-type: disc; margin-left: 20px;">
                                <li>平均时间复杂度 O(n log n)</li>
                                <li>原地排序，空间复杂度 O(log n)</li>
                                <li>实际性能优秀</li>
                                <li>分治思想经典应用</li>
                            </ul>
                        </div>
                        <div>
                            <h4 style="color: #e74c3c; margin-bottom: 10px;">⚠️ 注意事项</h4>
                            <ul style="list-style-type: disc; margin-left: 20px;">
                                <li>最坏时间复杂度 O(n²)</li>
                                <li>不稳定排序</li>
                                <li>递归深度可能很大</li>
                                <li>需要优化处理重复值</li>
                            </ul>
                        </div>
                    </div>
                    <br>
                    <p style="color: #bc884a; font-weight: 600; text-align: center; font-size: 1.1rem;">
                        快速排序是最重要的排序算法之一，掌握其分治思想和优化策略对算法学习具有重要意义。
                    </p>
                </div>
            </div>
        </section>
    </main>

    <footer class="footer-section">
        <div class="footer-content">
            <p>&copy; 2025 Java技术博客. 专注于算法与数据结构的深度解析.</p>
            <p>让复杂的算法变得简单易懂</p>
        </div>
    </footer>

    <button class="back-to-top" onclick="scrollToTop()" title="回到顶部">
        ↑
    </button>

    <script>
        // 平滑滚动到顶部功能
        function scrollToTop() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        }

        // 回到顶部按钮显示/隐藏
        window.addEventListener('scroll', function() {
            const backToTopBtn = document.querySelector('.back-to-top');
            if (window.scrollY > 300) {
                backToTopBtn.classList.add('visible');
            } else {
                backToTopBtn.classList.remove('visible');
            }
        });

        // 页面滚动动画
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        // 观察需要动画的元素
        document.addEventListener('DOMContentLoaded', function() {
            const animatedElements = document.querySelectorAll('.overview-card, .code-container, .algorithm-section, .optimization-section, .visual-demo');
            
            animatedElements.forEach(el => {
                el.style.opacity = '0';
                el.style.transform = 'translateY(30px)';
                el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
                observer.observe(el);
            });
        });
    </script>
</body>
</html>
