# 数据结构与算法展示系统

## 📋 项目概述

这是一个交互式的数据结构与算法可视化展示平台，包含了丰富的算法演示和LeetCode题解。

## 🎯 主要功能

### 1. 分类展示
- **基础概念**: 二进制运算、缓存行与局部性原理
- **数据结构**: 线性结构、堆、哈希表等
- **排序算法**: 基础排序和高级排序算法
- **搜索算法**: 二分查找及其变种
- **树结构**: 二叉树、平衡树、B树
- **图算法**: 最短路径、最小生成树
- **递归与回溯**: 经典递归问题和回溯算法
- **双指针技巧**: 数组问题的高效解法
- **字符串算法**: KMP、回文串等
- **动态规划**: 股票问题等经典DP
- **栈与队列**: 单调栈、单调队列应用
- **系统设计**: LRU、LFU缓存设计
- **贪心算法**: 局部最优解策略

### 2. LeetCode题解
- 包含50+道LeetCode题目的可视化解答
- 每道题都有详细的算法演示
- 支持按题号搜索
- 中文题目名称和说明

### 3. 交互式演示
- 所有算法都有可视化动画
- 支持步骤控制和参数调整
- 实时代码展示
- 复杂度分析

### 4. 搜索功能
- 支持按算法名称搜索
- 支持按分类搜索
- 支持按LeetCode题号搜索
- 实时搜索结果展示

## 🚀 访问方式

### 主页入口
1. 点击导航栏的"项目展示"
2. 点击主页的"查看所有项目"按钮

### 直接访问
访问 `algorithms.html` 页面

## 📁 文件结构

```
data-structures-frontend/
├── index.html                    # 原始目录页面
├── css/                         # 样式文件
│   └── prism.css               # 代码高亮样式
├── js/                          # JavaScript库
│   ├── p5.js                   # 图形库
│   ├── cytoscape.min.js        # 图形可视化
│   └── util.js                 # 工具函数
├── img/                         # 图片资源
├── 基础算法演示/
│   ├── binary_operation*.html   # 二进制运算
│   ├── cache_line*.html        # 缓存行演示
│   └── ...
├── 数据结构演示/
│   ├── ds_*.html               # 数据结构实现
│   ├── heap_*.html             # 堆结构
│   ├── hashtable.html          # 哈希表
│   └── ...
├── 排序算法/
│   ├── sort_bubble*.html       # 冒泡排序
│   ├── sort_quick*.html        # 快速排序
│   ├── sort_merge.html         # 归并排序
│   └── ...
├── 搜索算法/
│   ├── search_binary*.html     # 二分查找
│   └── ...
├── 树算法/
│   ├── tree_*.html             # 各种树结构
│   └── ...
├── 图算法/
│   ├── graph_dijkstra*.html    # 最短路径
│   ├── graph_floyd*.html       # Floyd算法
│   └── ...
└── LeetCode题解/
    ├── leetcode_*.html         # 按题号命名
    └── ...
```

## 🎨 设计特色

### 视觉设计
- 现代化的渐变背景
- 卡片式布局
- 响应式设计
- 毛玻璃效果
- 平滑动画过渡

### 用户体验
- 直观的分类导航
- 快速搜索功能
- LeetCode题目标识
- 复杂度信息展示
- 移动端适配

### 技术特点
- 纯前端实现
- 无需后端服务
- 快速加载
- 离线可用

## 📊 统计信息

- **总算法数量**: 100+ 个演示
- **LeetCode题目**: 50+ 道
- **分类数量**: 13 个主要分类
- **支持的算法类型**: 
  - 排序算法: 10+ 种
  - 搜索算法: 9 种二分查找变种
  - 树算法: 7 种树结构
  - 图算法: 6 种图算法
  - 数据结构: 10+ 种

## 🔧 技术栈

- **前端框架**: 原生HTML/CSS/JavaScript
- **可视化库**: P5.js, Cytoscape.js
- **代码高亮**: Prism.js
- **样式**: CSS3 + Flexbox/Grid
- **动画**: CSS Transitions + JavaScript

## 📱 响应式支持

- **桌面端**: 完整功能体验
- **平板端**: 优化的触控体验
- **移动端**: 简化的界面布局

## 🎯 学习路径建议

### 初学者
1. 基础概念 → 数据结构 → 排序算法
2. 从简单的冒泡排序开始
3. 理解时间复杂度概念

### 进阶学习
1. 搜索算法 → 树结构 → 图算法
2. 掌握二分查找的各种变种
3. 学习平衡树的实现原理

### 面试准备
1. LeetCode题解 → 系统设计 → 动态规划
2. 重点关注双指针、回溯等技巧
3. 练习系统设计类题目

## 🚀 未来计划

- [ ] 添加更多LeetCode题目
- [ ] 增加算法性能对比
- [ ] 添加用户学习进度跟踪
- [ ] 支持自定义数据输入
- [ ] 增加算法复杂度可视化
- [ ] 添加算法学习路径推荐

## 📞 反馈与建议

如果您有任何建议或发现问题，欢迎通过以下方式联系：
- 在主页留言
- 发送邮件反馈
- 提出改进建议

---

**让算法学习变得生动有趣！** 🎉
