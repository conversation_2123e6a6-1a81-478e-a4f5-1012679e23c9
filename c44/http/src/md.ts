
import * as path from "path";
import { FileUtil } from 'tools-mock';
const fileUrl = path.join(__dirname, "./md");
export const md = {
                '/af/20e188/00000000000000003b9b388d/27/l': {
                    method: 'GET',
                    fun: async (data:any) => {
                        console.log('/af/20e188/00000000000000003b9b388d/27/l',data);
            
            
                data = JSON.parse(data)
            
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22n4%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, '9199a1cb-262d-477f-92ab-15f8f7950f71'))
              return str
            }
        
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22n4%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, '8396cd9c-4831-4102-8462-1013f78e2060'))
              return str
            }
        
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22n4%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, 'cc667f9f-4b3f-4f6e-b264-09ed35b63fa2'))
              return str
            }
        
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22n4%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, '79ad1e3c-b393-4de9-baec-42d9765c28d1'))
              return str
            }
        
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22n4%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, '89594723-8306-4dfa-8af8-b5f91f6eb87b'))
              return str
            }
        
                         
          let str = await FileUtil.readFile(path.join(fileUrl, '9199a1cb-262d-477f-92ab-15f8f7950f71'))
          return str
          
                    }
                },

                
                '/af/16b50b/00000000000000003b9b388f/27/l': {
                    method: 'GET',
                    fun: async (data:any) => {
                        console.log('/af/16b50b/00000000000000003b9b388f/27/l',data);
            
            
                data = JSON.parse(data)
            
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22n4%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, '01e50a8e-d166-4aa7-a549-750bd45f1d76'))
              return str
            }
        
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22n4%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, 'fb76821d-993b-458c-9de7-9fa3993ebb2f'))
              return str
            }
        
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22n4%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, 'f2f35632-df09-4d17-a058-c983116d3908'))
              return str
            }
        
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22n4%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, '664600a1-e28b-4a38-9dea-481fd14217e0'))
              return str
            }
        
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22n4%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, 'c21646d8-0cdf-4a01-bcef-75adb52a9167'))
              return str
            }
        
                         
          let str = await FileUtil.readFile(path.join(fileUrl, '01e50a8e-d166-4aa7-a549-750bd45f1d76'))
          return str
          
                    }
                },

                
                '/af/0c52d4/00000000000000003b9adb35/27/l': {
                    method: 'GET',
                    fun: async (data:any) => {
                        console.log('/af/0c52d4/00000000000000003b9adb35/27/l',data);
            
            
                data = JSON.parse(data)
            
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22n4%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, '92f70b1b-76ea-45ec-8c84-78868cce2176'))
              return str
            }
        
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22n4%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, '61173fee-56a7-410a-bfc6-2f00733aa878'))
              return str
            }
        
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22n4%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, 'fa383449-eaf6-4ab1-9fab-ffe13245c91e'))
              return str
            }
        
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22n4%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, '9b9ffe82-e799-45e3-821e-3035c97afd61'))
              return str
            }
        
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22n4%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, 'c94c242f-fae3-49e6-88c5-01f1ae096e80'))
              return str
            }
        
                         
          let str = await FileUtil.readFile(path.join(fileUrl, '92f70b1b-76ea-45ec-8c84-78868cce2176'))
          return str
          
                    }
                },

                
                '/af/26f0fd/00000000000000003b9afa9c/27/l': {
                    method: 'GET',
                    fun: async (data:any) => {
                        console.log('/af/26f0fd/00000000000000003b9afa9c/27/l',data);
            
            
                data = JSON.parse(data)
            
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22i4%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, '4954456c-b84f-468b-a769-014ea93707de'))
              return str
            }
        
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22i4%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, 'b3cce5b9-f915-42f9-8132-e6f9512b31a8'))
              return str
            }
        
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22i4%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, '8d791fcb-be8c-4fb7-9113-8e882f0fa494'))
              return str
            }
        
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22i4%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, 'a0f96f88-b866-4e6a-a60f-5894b27146e8'))
              return str
            }
        
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22i4%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, '675e883e-3d22-4c71-a400-f65b1d1eee6b'))
              return str
            }
        
                         
          let str = await FileUtil.readFile(path.join(fileUrl, '4954456c-b84f-468b-a769-014ea93707de'))
          return str
          
                    }
                },

                
                '/': {
                    method: 'GET',
                    fun: async (data:any) => {
                        console.log('/',data);
            
            
                data = JSON.parse(data)
            
            if(data==`%7B%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, '37d1d1a5-7368-4f26-8184-914b679ecbe1'))
              return str
            }
        
            if(data==`%7B%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, '43dbec08-283d-44e5-9eee-6e43352777db'))
              return str
            }
        
            if(data==`%7B%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, '2c3332b8-e4b2-48ff-9093-d1c6629e3e10'))
              return str
            }
        
            if(data==`%7B%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, 'd92a38ee-f937-480a-a985-5d2337bbce3f'))
              return str
            }
        
            if(data==`%7B%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, 'f0f73f72-6bc0-4a32-8bff-be9958c86cae'))
              return str
            }
        
            if(data==`%7B%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, '97239fa3-82d3-42d3-a263-e45555b65685'))
              return str
            }
        
            if(data==`%7B%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, 'c654c62a-0623-4c72-9d7b-3dff58c1f311'))
              return str
            }
        
                         
          let str = await FileUtil.readFile(path.join(fileUrl, '37d1d1a5-7368-4f26-8184-914b679ecbe1'))
          return str
          
                    }
                },

                
                '/af/374120/00000000000000003b9adb34/27/l': {
                    method: 'GET',
                    fun: async (data:any) => {
                        console.log('/af/374120/00000000000000003b9adb34/27/l',data);
            
            
                data = JSON.parse(data)
            
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22n7%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, '5de9a91b-35e1-4a3b-a00a-ebf40303a5a5'))
              return str
            }
        
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22n7%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, '94fd6936-c620-4865-8982-54213dbc5d4a'))
              return str
            }
        
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22n7%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, 'f9fd3e1d-dfaf-4c5d-9375-69180f5b0a39'))
              return str
            }
        
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22n7%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, 'f9078ceb-2629-4bef-8e19-1710efae36f1'))
              return str
            }
        
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22n7%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, '2799687d-2327-4c08-a9bc-7b99b4c2ae79'))
              return str
            }
        
                         
          let str = await FileUtil.readFile(path.join(fileUrl, '5de9a91b-35e1-4a3b-a00a-ebf40303a5a5'))
          return str
          
                    }
                },

                
                '/af/28a19a/00000000000000003b9b1575/27/l': {
                    method: 'GET',
                    fun: async (data:any) => {
                        console.log('/af/28a19a/00000000000000003b9b1575/27/l',data);
            
            
                data = JSON.parse(data)
            
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22n7%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, 'ac359f17-8395-4316-acf0-d497140d04df'))
              return str
            }
        
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22n7%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, '4edf9564-aa0c-4428-bd07-46f05e4b1b25'))
              return str
            }
        
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22n7%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, 'ad1ccc05-4e6d-49ea-8f84-a3a81d14376f'))
              return str
            }
        
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22n7%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, '236092d5-f2ae-4a80-bcc2-db69b4bb0f23'))
              return str
            }
        
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22n7%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, 'a2c7249e-a7b3-4282-9792-d09d057f4805'))
              return str
            }
        
                         
          let str = await FileUtil.readFile(path.join(fileUrl, 'ac359f17-8395-4316-acf0-d497140d04df'))
          return str
          
                    }
                },

                
                '/af/39ddb1/00000000000000003b9b406f/27/l': {
                    method: 'GET',
                    fun: async (data:any) => {
                        console.log('/af/39ddb1/00000000000000003b9b406f/27/l',data);
            
            
                data = JSON.parse(data)
            
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22n9%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, 'fd70136b-554c-4439-9e4c-72e154634bcd'))
              return str
            }
        
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22n9%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, '928017a1-1309-4a7f-81cd-c9b54e7ad846'))
              return str
            }
        
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22n9%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, '8c0efdab-c5b5-464c-b5ea-4e6935fc350d'))
              return str
            }
        
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22n9%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, '546adba4-f7b2-4661-8c3c-24b3b5ec5ac5'))
              return str
            }
        
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22n9%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, '9c15ea21-67b2-4c01-82a8-40cead5a9580'))
              return str
            }
        
                         
          let str = await FileUtil.readFile(path.join(fileUrl, 'fd70136b-554c-4439-9e4c-72e154634bcd'))
          return str
          
                    }
                },

                
                '/af/28158e/00000000000000003b9b4066/27/l': {
                    method: 'GET',
                    fun: async (data:any) => {
                        console.log('/af/28158e/00000000000000003b9b4066/27/l',data);
            
            
                data = JSON.parse(data)
            
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22n4%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, '2f3b3b63-49cc-4c4b-b1a1-0c9bf9661f9b'))
              return str
            }
        
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22n4%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, '2f4b14ab-5887-4865-a723-3613274f0a3c'))
              return str
            }
        
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22n4%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, '1f895882-4389-474f-960a-f0db45fabfca'))
              return str
            }
        
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22n4%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, 'bcab0fe6-aea4-4528-8b78-9466cab69c75'))
              return str
            }
        
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22n4%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, '0d4340b3-de3c-483a-b903-89fdcc018b45'))
              return str
            }
        
                         
          let str = await FileUtil.readFile(path.join(fileUrl, '2f3b3b63-49cc-4c4b-b1a1-0c9bf9661f9b'))
          return str
          
                    }
                },

                
                '/af/386e17/00000000000000003b9b4067/27/l': {
                    method: 'GET',
                    fun: async (data:any) => {
                        console.log('/af/386e17/00000000000000003b9b4067/27/l',data);
            
            
                data = JSON.parse(data)
            
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22i4%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, '8e3d0c5e-c237-4d4a-9042-9039da9a00df'))
              return str
            }
        
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22i4%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, '9c48ce92-d186-4a64-bdb8-a84ca3caa674'))
              return str
            }
        
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22i4%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, '34a3cdab-698d-4053-843a-6b551e76cc40'))
              return str
            }
        
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22i4%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, 'fd305cbb-fa40-443a-a996-16a970a50b8d'))
              return str
            }
        
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22i4%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, 'ef0d1d1e-3bd2-4baf-ad37-a83197e63c9b'))
              return str
            }
        
                         
          let str = await FileUtil.readFile(path.join(fileUrl, '8e3d0c5e-c237-4d4a-9042-9039da9a00df'))
          return str
          
                    }
                },

                
                '/af/3e45bd/00000000000000003b9b1578/27/l': {
                    method: 'GET',
                    fun: async (data:any) => {
                        console.log('/af/3e45bd/00000000000000003b9b1578/27/l',data);
            
            
                data = JSON.parse(data)
            
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22n5%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, '5388326d-3416-4c56-a5e1-7556e2328eeb'))
              return str
            }
        
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22n5%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, '28d8c28a-b4a2-4824-bc7c-adaf6366e085'))
              return str
            }
        
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22n5%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, '4bd0d693-64b1-43c3-b29d-6da21c5a9af5'))
              return str
            }
        
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22n5%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, '322a7f33-a8b7-4068-a8b1-08cd7d9d7a9c'))
              return str
            }
        
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22n5%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, '03f91c9b-ea31-4c82-b963-f39b80f78131'))
              return str
            }
        
                         
          let str = await FileUtil.readFile(path.join(fileUrl, '5388326d-3416-4c56-a5e1-7556e2328eeb'))
          return str
          
                    }
                },

                
                '/af/62681e/00000000000000003b9b406a/27/l': {
                    method: 'GET',
                    fun: async (data:any) => {
                        console.log('/af/62681e/00000000000000003b9b406a/27/l',data);
            
            
                data = JSON.parse(data)
            
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22n7%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, '2aeaddfa-8546-4551-a274-f5b5f85cb3dc'))
              return str
            }
        
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22n7%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, '0effa354-6105-416a-b620-fd86842a0eac'))
              return str
            }
        
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22n7%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, '0e51218a-0da6-4b0d-8fb1-f9223774c555'))
              return str
            }
        
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22n7%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, '1fee9d26-7fa7-444a-99e5-a550c46fbe34'))
              return str
            }
        
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22n7%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, '88b95b99-55ec-4c86-bffb-109775d6f4ab'))
              return str
            }
        
                         
          let str = await FileUtil.readFile(path.join(fileUrl, '2aeaddfa-8546-4551-a274-f5b5f85cb3dc'))
          return str
          
                    }
                },

                
                '/af/76d40d/00000000000000003b9b4064/27/l': {
                    method: 'GET',
                    fun: async (data:any) => {
                        console.log('/af/76d40d/00000000000000003b9b4064/27/l',data);
            
            
                data = JSON.parse(data)
            
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22n3%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, '6de7f30b-008f-448d-a75e-8f8a4949116c'))
              return str
            }
        
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22n3%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, '302bc764-68f1-4fd7-bdc6-2b0da7dce856'))
              return str
            }
        
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22n3%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, '0fb7bc13-e583-4da5-afd0-e6cf823ab0cd'))
              return str
            }
        
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22n3%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, '896e670d-7315-4c4c-88c9-0f6ca8c0c443'))
              return str
            }
        
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22n3%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, '40605aa8-e5e1-4650-b8c0-ff0d20949cb5'))
              return str
            }
        
                         
          let str = await FileUtil.readFile(path.join(fileUrl, '6de7f30b-008f-448d-a75e-8f8a4949116c'))
          return str
          
                    }
                },

                
                '/af/6b6c33/00000000000000003b9b21ad/27/l': {
                    method: 'GET',
                    fun: async (data:any) => {
                        console.log('/af/6b6c33/00000000000000003b9b21ad/27/l',data);
            
            
                data = JSON.parse(data)
            
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22n4%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, '1d66bd6e-342a-42cb-a19c-52fa1e4180b6'))
              return str
            }
        
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22n4%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, 'b260c42e-6051-4a49-8482-365814d72da3'))
              return str
            }
        
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22n4%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, 'ebc159b3-06c5-48e4-831f-6be5f0376b8a'))
              return str
            }
        
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22n4%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, '658a5a67-84c7-4680-a566-e8d04269ba51'))
              return str
            }
        
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22n4%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, 'd47d3df4-b78a-4b1f-901d-857733339abb'))
              return str
            }
        
                         
          let str = await FileUtil.readFile(path.join(fileUrl, '1d66bd6e-342a-42cb-a19c-52fa1e4180b6'))
          return str
          
                    }
                },

                
                '/af/78524b/0000000000000000000178b7/27/l': {
                    method: 'GET',
                    fun: async (data:any) => {
                        console.log('/af/78524b/0000000000000000000178b7/27/l',data);
            
            
                data = JSON.parse(data)
            
            if(data==`%7B%22subset_id%22:%221%22,%22fvd%22:%22n4%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, 'c77ce958-e0a3-4168-89ae-a8069c90dfb7'))
              return str
            }
        
            if(data==`%7B%22subset_id%22:%221%22,%22fvd%22:%22n4%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, '23399e10-cdfa-40b2-b007-dced402ba9cd'))
              return str
            }
        
            if(data==`%7B%22subset_id%22:%221%22,%22fvd%22:%22n4%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, '6812b846-b371-4fb9-b442-c98339cc0bef'))
              return str
            }
        
            if(data==`%7B%22subset_id%22:%221%22,%22fvd%22:%22n4%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, 'a1a45963-a248-46f0-a43d-a46841e6e101'))
              return str
            }
        
            if(data==`%7B%22subset_id%22:%221%22,%22fvd%22:%22n4%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, 'd0b5fa3c-9785-4e14-9093-8063ad83f42e'))
              return str
            }
        
                         
          let str = await FileUtil.readFile(path.join(fileUrl, 'c77ce958-e0a3-4168-89ae-a8069c90dfb7'))
          return str
          
                    }
                },

                
                '/af/a7428e/00000000000000003b9afa9a/27/l': {
                    method: 'GET',
                    fun: async (data:any) => {
                        console.log('/af/a7428e/00000000000000003b9afa9a/27/l',data);
            
            
                data = JSON.parse(data)
            
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22i4%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, '64435bb4-8749-46d8-9882-b3a54937d852'))
              return str
            }
        
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22i4%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, '2d7f709a-da77-466e-93d0-4dd75c8966b0'))
              return str
            }
        
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22i4%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, '369d1b0d-f5ea-45dc-b52b-90daccd70c89'))
              return str
            }
        
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22i4%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, '2d37479c-80f4-4ef4-8d6f-78aacc97fe0d'))
              return str
            }
        
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22i4%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, '96a0c64b-ac95-4a3d-b90d-f662f5f5650a'))
              return str
            }
        
                         
          let str = await FileUtil.readFile(path.join(fileUrl, '64435bb4-8749-46d8-9882-b3a54937d852'))
          return str
          
                    }
                },

                
                '/af/9710d8/000000000000000000017238/27/l': {
                    method: 'GET',
                    fun: async (data:any) => {
                        console.log('/af/9710d8/000000000000000000017238/27/l',data);
            
            
                data = JSON.parse(data)
            
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22n4%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, '435e2b50-dc24-4c44-aa7e-773f90c6d1d7'))
              return str
            }
        
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22n4%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, '0ced051a-c858-47ee-83fc-c10798c7107f'))
              return str
            }
        
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22n4%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, 'dffd0d31-8db9-453a-9407-23948a892d76'))
              return str
            }
        
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22n4%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, '024d6400-3ea9-46d7-9a89-33e3e0483a89'))
              return str
            }
        
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22n4%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, '26813e83-aa58-444f-b121-fb8f6823bc86'))
              return str
            }
        
                         
          let str = await FileUtil.readFile(path.join(fileUrl, '435e2b50-dc24-4c44-aa7e-773f90c6d1d7'))
          return str
          
                    }
                },

                
                '/af/a941cf/00000000000000003b9afa9d/27/l': {
                    method: 'GET',
                    fun: async (data:any) => {
                        console.log('/af/a941cf/00000000000000003b9afa9d/27/l',data);
            
            
                data = JSON.parse(data)
            
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22n4%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, '035f72a2-25bf-41d2-9386-737f6b6ef1c3'))
              return str
            }
        
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22n4%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, '98faee3c-d856-49d5-9953-6f0cf235cb09'))
              return str
            }
        
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22n4%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, 'b51e6b8d-dafa-429c-8906-92face8d76b2'))
              return str
            }
        
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22n4%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, '895fd590-5f51-4308-9c80-c252d25ef5ba'))
              return str
            }
        
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22n4%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, 'a088023a-e095-4209-b2dd-64550dc8f2e4'))
              return str
            }
        
                         
          let str = await FileUtil.readFile(path.join(fileUrl, '035f72a2-25bf-41d2-9386-737f6b6ef1c3'))
          return str
          
                    }
                },

                
                '/af/701662/0000000000000000000178b9/27/l': {
                    method: 'GET',
                    fun: async (data:any) => {
                        console.log('/af/701662/0000000000000000000178b9/27/l',data);
            
            
                data = JSON.parse(data)
            
            if(data==`%7B%22subset_id%22:%221%22,%22fvd%22:%22n7%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, 'a5f2591f-02ce-4549-97e1-bc1d2fc7c051'))
              return str
            }
        
            if(data==`%7B%22subset_id%22:%221%22,%22fvd%22:%22n7%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, '550c7507-a85b-41f9-8ca9-b0d68f5d1ff2'))
              return str
            }
        
            if(data==`%7B%22subset_id%22:%221%22,%22fvd%22:%22n7%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, '1836421b-b824-45bd-845d-4bd3368eddb0'))
              return str
            }
        
            if(data==`%7B%22subset_id%22:%221%22,%22fvd%22:%22n7%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, '52a7338d-c31e-4f2b-9aa5-4320bebdc122'))
              return str
            }
        
            if(data==`%7B%22subset_id%22:%221%22,%22fvd%22:%22n7%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, 'a291329d-0c74-4e36-b0dd-a5d198e94567'))
              return str
            }
        
                         
          let str = await FileUtil.readFile(path.join(fileUrl, 'a5f2591f-02ce-4549-97e1-bc1d2fc7c051'))
          return str
          
                    }
                },

                
                '/af/a96e28/00000000000000003b9afa9b/27/l': {
                    method: 'GET',
                    fun: async (data:any) => {
                        console.log('/af/a96e28/00000000000000003b9afa9b/27/l',data);
            
            
                data = JSON.parse(data)
            
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22n4%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, '19468bb8-157d-4bdc-8ee2-9ff08fd01914'))
              return str
            }
        
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22n4%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, '7821639f-c05a-4ff0-8270-b78c1eab2e2f'))
              return str
            }
        
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22n4%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, 'ff47ef01-67ab-41ce-a5cd-8e0352ac71a8'))
              return str
            }
        
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22n4%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, 'dc5abec9-e859-4156-9cf0-7cab3ff1f7ec'))
              return str
            }
        
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22n4%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, '7b01dbf0-2732-473d-92a2-eec97be345af'))
              return str
            }
        
                         
          let str = await FileUtil.readFile(path.join(fileUrl, '19468bb8-157d-4bdc-8ee2-9ff08fd01914'))
          return str
          
                    }
                },

                
                '/af/b0e0b2/00000000000000003b9b388e/27/l': {
                    method: 'GET',
                    fun: async (data:any) => {
                        console.log('/af/b0e0b2/00000000000000003b9b388e/27/l',data);
            
            
                data = JSON.parse(data)
            
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22n4%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, '8b20f188-f23f-4d7b-b383-0e58c875ffce'))
              return str
            }
        
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22n4%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, 'e5c3de93-1d5e-42d4-80dc-8b73fdffc296'))
              return str
            }
        
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22n4%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, '9583970c-80ed-49be-8be8-7db984dc301f'))
              return str
            }
        
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22n4%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, 'bd580e86-e7a6-4013-be08-fdb99428c9e0'))
              return str
            }
        
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22n4%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, '13246673-56f1-42b5-b388-132585d2e89b'))
              return str
            }
        
                         
          let str = await FileUtil.readFile(path.join(fileUrl, '8b20f188-f23f-4d7b-b383-0e58c875ffce'))
          return str
          
                    }
                },

                
                '/af/7c955a/00000000000000003b9b406d/27/l': {
                    method: 'GET',
                    fun: async (data:any) => {
                        console.log('/af/7c955a/00000000000000003b9b406d/27/l',data);
            
            
                data = JSON.parse(data)
            
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22n8%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, 'd98d3260-3b4e-41ba-a4c8-a35b1e2427a9'))
              return str
            }
        
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22n8%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, '822c8ba2-c2db-4d1a-b4db-de9d7e83b35c'))
              return str
            }
        
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22n8%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, 'fb5d66c6-092f-439d-aff1-5c01fad78c0c'))
              return str
            }
        
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22n8%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, 'b29ba5d9-33b1-440a-8a13-6291814eae01'))
              return str
            }
        
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22n8%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, '88c67103-af4a-427c-a242-dacf129ea88b'))
              return str
            }
        
                         
          let str = await FileUtil.readFile(path.join(fileUrl, 'd98d3260-3b4e-41ba-a4c8-a35b1e2427a9'))
          return str
          
                    }
                },

                
                '/af/c049b3/00000000000000003b9b1570/27/l': {
                    method: 'GET',
                    fun: async (data:any) => {
                        console.log('/af/c049b3/00000000000000003b9b1570/27/l',data);
            
            
                data = JSON.parse(data)
            
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22n7%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, 'dcee532d-9be0-453c-a342-17940567369b'))
              return str
            }
        
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22n7%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, 'a67bb1eb-047a-43b0-8f15-d6424f1514c7'))
              return str
            }
        
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22n7%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, 'd27a737e-79ab-4b2c-91c1-abef9b12fcba'))
              return str
            }
        
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22n7%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, '6dbb39d1-10aa-4817-8a66-e486e88bc7d5'))
              return str
            }
        
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22n7%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, 'dc16c563-2151-45e6-b27a-ce3e33289ff4'))
              return str
            }
        
                         
          let str = await FileUtil.readFile(path.join(fileUrl, 'dcee532d-9be0-453c-a342-17940567369b'))
          return str
          
                    }
                },

                
                '/af/bc7a7f/0000000000000000000178b6/27/l': {
                    method: 'GET',
                    fun: async (data:any) => {
                        console.log('/af/bc7a7f/0000000000000000000178b6/27/l',data);
            
            
                data = JSON.parse(data)
            
            if(data==`%7B%22subset_id%22:%221%22,%22fvd%22:%22n3%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, 'ae6321cf-57f8-4ff7-9027-363cb58529e7'))
              return str
            }
        
            if(data==`%7B%22subset_id%22:%221%22,%22fvd%22:%22n3%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, '11dcba87-6045-406a-91ab-b52e80fe7233'))
              return str
            }
        
            if(data==`%7B%22subset_id%22:%221%22,%22fvd%22:%22n3%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, 'a05a83f5-f904-4551-b9e4-92ae562b190e'))
              return str
            }
        
            if(data==`%7B%22subset_id%22:%221%22,%22fvd%22:%22n3%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, '4ce57a64-0918-4949-aa97-b59ce6119edb'))
              return str
            }
        
            if(data==`%7B%22subset_id%22:%221%22,%22fvd%22:%22n3%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, 'bba7a217-4858-4341-a7aa-e61f26915ece'))
              return str
            }
        
                         
          let str = await FileUtil.readFile(path.join(fileUrl, 'ae6321cf-57f8-4ff7-9027-363cb58529e7'))
          return str
          
                    }
                },

                
                '/af/c7ae77/00000000000000003b9adb32/27/l': {
                    method: 'GET',
                    fun: async (data:any) => {
                        console.log('/af/c7ae77/00000000000000003b9adb32/27/l',data);
            
            
                data = JSON.parse(data)
            
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22i7%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, 'c25fae78-d543-4805-bca3-934dfdb55b45'))
              return str
            }
        
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22i7%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, 'e9762184-9d21-4376-a26f-e9505b745c92'))
              return str
            }
        
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22i7%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, 'd16fb30d-1c01-4a5e-8ad5-27cc1aa8ba2c'))
              return str
            }
        
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22i7%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, 'bb33b367-2430-41ea-a07d-0aaa66162a40'))
              return str
            }
        
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22i7%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, '1c9478d5-f16e-45b4-8ae1-e22b0cdba621'))
              return str
            }
        
                         
          let str = await FileUtil.readFile(path.join(fileUrl, 'c25fae78-d543-4805-bca3-934dfdb55b45'))
          return str
          
                    }
                },

                
                '/af/b739e6/00000000000000003b9b406b/27/l': {
                    method: 'GET',
                    fun: async (data:any) => {
                        console.log('/af/b739e6/00000000000000003b9b406b/27/l',data);
            
            
                data = JSON.parse(data)
            
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22i7%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, 'be181769-46f4-4417-a0ef-cc8d94e236cd'))
              return str
            }
        
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22i7%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, 'e20f51ea-982d-4843-b9dd-a78833c65ebd'))
              return str
            }
        
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22i7%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, 'd469772c-dbd8-4e36-9085-6295d3d05c6b'))
              return str
            }
        
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22i7%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, '5f84b392-3a77-4272-b189-9324aa6f6565'))
              return str
            }
        
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22i7%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, '7f69f7b9-6b8f-4f27-93cb-5288a8a1601e'))
              return str
            }
        
                         
          let str = await FileUtil.readFile(path.join(fileUrl, 'be181769-46f4-4417-a0ef-cc8d94e236cd'))
          return str
          
                    }
                },

                
                '/af/cf338c/000000000000000000017239/27/l': {
                    method: 'GET',
                    fun: async (data:any) => {
                        console.log('/af/cf338c/000000000000000000017239/27/l',data);
            
            
                data = JSON.parse(data)
            
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22n7%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, '8ae8af8f-80be-4df5-a01e-ad97e3edf256'))
              return str
            }
        
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22n7%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, 'a7be7fa2-5b15-46f6-a5fd-90bd748065ad'))
              return str
            }
        
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22n7%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, '334cfc99-7f6e-45ff-83a3-c9fde9cce1c4'))
              return str
            }
        
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22n7%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, '0f81d1dd-cdf0-4ded-a50d-fda882879372'))
              return str
            }
        
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22n7%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, '80dece54-b35f-4191-90d8-7285c8b78ba7'))
              return str
            }
        
                         
          let str = await FileUtil.readFile(path.join(fileUrl, '8ae8af8f-80be-4df5-a01e-ad97e3edf256'))
          return str
          
                    }
                },

                
                '/af/cee6e5/00000000000000003b9b4062/27/l': {
                    method: 'GET',
                    fun: async (data:any) => {
                        console.log('/af/cee6e5/00000000000000003b9b4062/27/l',data);
            
            
                data = JSON.parse(data)
            
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22n2%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, '102f6d45-ebc1-4904-9861-e496424b1a25'))
              return str
            }
        
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22n2%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, '64779fab-dcdf-4a1a-81cf-9868edd4c32d'))
              return str
            }
        
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22n2%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, '439195df-7485-4333-8630-c2b38f3c6bfc'))
              return str
            }
        
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22n2%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, 'f55df7cf-4afe-4367-bf41-04d72578fb2b'))
              return str
            }
        
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22n2%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, 'c86cb116-ae0e-4c1c-b3da-dbe3a4d97717'))
              return str
            }
        
                         
          let str = await FileUtil.readFile(path.join(fileUrl, '102f6d45-ebc1-4904-9861-e496424b1a25'))
          return str
          
                    }
                },

                
                '/af/d7bcae/00000000000000003b9adb31/27/l': {
                    method: 'GET',
                    fun: async (data:any) => {
                        console.log('/af/d7bcae/00000000000000003b9adb31/27/l',data);
            
            
                data = JSON.parse(data)
            
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22i4%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, 'b4c7ed2c-0814-4f98-bd7f-45600b2848cf'))
              return str
            }
        
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22i4%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, 'f0eeef8a-52f5-4145-80f0-6f986cb8503a'))
              return str
            }
        
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22i4%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, '93425b0f-b00e-43df-b757-3375db3c046c'))
              return str
            }
        
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22i4%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, 'c44a11fe-526c-4c83-ad2c-291ead0deadc'))
              return str
            }
        
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22i4%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, '1a8af93a-d7d2-4439-b97f-9e8bf7f9e1db'))
              return str
            }
        
                         
          let str = await FileUtil.readFile(path.join(fileUrl, 'b4c7ed2c-0814-4f98-bd7f-45600b2848cf'))
          return str
          
                    }
                },

                
                '/af/da38f0/0000000000000000000178b8/27/l': {
                    method: 'GET',
                    fun: async (data:any) => {
                        console.log('/af/da38f0/0000000000000000000178b8/27/l',data);
            
            
                data = JSON.parse(data)
            
            if(data==`%7B%22subset_id%22:%221%22,%22fvd%22:%22n6%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, 'fb492750-5775-46f0-a2f9-aec9abc191fa'))
              return str
            }
        
            if(data==`%7B%22subset_id%22:%221%22,%22fvd%22:%22n6%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, '624dd1cd-a9c8-4b35-b86a-3fdad54693a9'))
              return str
            }
        
            if(data==`%7B%22subset_id%22:%221%22,%22fvd%22:%22n6%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, 'f97b37b4-3e24-4be0-beb6-de25812f7edb'))
              return str
            }
        
            if(data==`%7B%22subset_id%22:%221%22,%22fvd%22:%22n6%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, 'f443a0bd-cb19-47a5-9fd6-a13773ba6deb'))
              return str
            }
        
            if(data==`%7B%22subset_id%22:%221%22,%22fvd%22:%22n6%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, '01764a45-77f1-42d9-b30d-30a289bf38ef'))
              return str
            }
        
                         
          let str = await FileUtil.readFile(path.join(fileUrl, 'fb492750-5775-46f0-a2f9-aec9abc191fa'))
          return str
          
                    }
                },

                
                '/css': {
                    method: 'GET',
                    fun: async (data:any) => {
                        console.log('/css',data);
            
            
                data = JSON.parse(data)
            
            if(data==`%7B%22family%22:%22Oswald:200,300,400,500,600,700%7CInter:300,regular,500,600,700,800,900%7CInstrument+Serif:regular,italic%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, 'dd1dac89-c591-4952-945a-729b5d1e2a8d'))
              return str
            }
        
            if(data==`%7B%22family%22:%22Oswald:200,300,400,500,600,700%7CInter:300,regular,500,600,700,800,900%7CInstrument+Serif:regular,italic%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, '1b79919b-52c3-453a-9f37-27ad11cfb236'))
              return str
            }
        
            if(data==`%7B%22family%22:%22Oswald:200,300,400,500,600,700%7CInter:300,regular,500,600,700,800,900%7CInstrument+Serif:regular,italic%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, 'ad40f595-ceb9-474f-a17e-a47a9f7d61f4'))
              return str
            }
        
            if(data==`%7B%22family%22:%22Oswald:200,300,400,500,600,700%7CInter:300,regular,500,600,700,800,900%7CInstrument+Serif:regular,italic%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, 'dcc5f099-66b7-4976-acfa-44f546c57159'))
              return str
            }
        
            if(data==`%7B%22family%22:%22Oswald:200,300,400,500,600,700%7CInter:300,regular,500,600,700,800,900%7CInstrument+Serif:regular,italic%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, '27351f60-750a-4336-9ede-2839438fcccd'))
              return str
            }
        
            if(data==`%7B%22family%22:%22Oswald:200,300,400,500,600,700%7CInter:300,regular,500,600,700,800,900%7CInstrument+Serif:regular,italic%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, 'f3d2428c-843d-439b-aead-9a559d1a8456'))
              return str
            }
        
                         
          let str = await FileUtil.readFile(path.join(fileUrl, 'dd1dac89-c591-4952-945a-729b5d1e2a8d'))
          return str
          
                    }
                },

                
                '/af/e6225e/00000000000000003b9b21aa/27/l': {
                    method: 'GET',
                    fun: async (data:any) => {
                        console.log('/af/e6225e/00000000000000003b9b21aa/27/l',data);
            
            
                data = JSON.parse(data)
            
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22i4%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, '7f83682c-ffce-4d48-ab4e-0b2d6db78982'))
              return str
            }
        
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22i4%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, '1bdbc3c8-e512-4c21-9521-e93fb9a66468'))
              return str
            }
        
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22i4%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, '5bfdd5b7-ee13-492a-930a-77d021ac86de'))
              return str
            }
        
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22i4%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, 'a3e8a376-5cd2-4e3e-8418-c3a89ca07c7d'))
              return str
            }
        
            if(data==`%7B%22primer%22:%227cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191%22,%22fvd%22:%22i4%22,%22v%22:%223%22%7D`) {
              let str = await FileUtil.readFile(path.join(fileUrl, '82f8b9ff-5579-4cac-9791-55385adc6d8c'))
              return str
            }
        
                         
          let str = await FileUtil.readFile(path.join(fileUrl, '7f83682c-ffce-4d48-ab4e-0b2d6db78982'))
          return str
          
                    }
                },

                }