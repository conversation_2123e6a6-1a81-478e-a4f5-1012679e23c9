import path = require('path')
import { MockData } from './MockData'
import { CMock, FileUtil } from "tools-mock";

const dirUrl = path.join(__dirname, '../../')
const cmock = new CMock()
cmock.setConfig({
    resAfter(param) {
        //处理当存在返回的数据是html时修改Content-Type，并且将回车符、表格符、换行符修改？还原？
        let endStr = param.data
        let isHtml = endStr.indexOf('<!DOCTYPE')
        isHtml = isHtml < 20 && isHtml >= 0
        if (isHtml) {
            param.headers['Content-Type'] = 'text/html; charset=utf-8'
            endStr = endStr.substring(1, endStr.length - 1)
            endStr = endStr.replace(/\\\"/g, '"').replace(/\\r/g, '\r').replace(/\\n/g, '\n').replace(/\\t/g, '\t')
        }
        param.data = endStr
    },
    staticFileUrl(url) {
        //将根转发到index.html
        if (url == '/') url = '/index.html'
        return url
    }
})

const init = async () => {
//   let wsdata = await FileUtil.readFile(path.join(dirUrl, "websocket.json"));
//   wsdata = JSON.parse(wsdata);
//   cmock.setWSData({
//     "/gameServer": {
//       async connection(req, ws) {
//         for (let i = 0; i < wsdata.list.length; i++) {
//           const item = wsdata.list[i];
//           ws.send(JSON.stringify(item));
//           await new Promise((res) => {
//             setTimeout(() => {
//               res(true);
//             }, 1000);
//           });
//         }
//       },
//       message(message, ws) {
//           message = message.toString()
//           message = JSON.parse(message)
//           ws.send(JSON.stringify(wsdata.code[message.proto+'']));
//       },
//     },
//   });

  cmock.setStaticDir(dirUrl)
  cmock.setData(MockData)
  
  cmock.run(2922)
};

init();