import * as path from 'path'
import { FileUtil } from 'tools-mock'

const dirUrl = path.join(__dirname, '../data')
const fileUrl = path.join(__dirname, './md')

/**
 * 获取uuid
 * @returns
 */
const uuid = () => {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
        var r = (Math.random() * 16) | 0,
            v = c == 'x' ? r : (r & 0x3) | 0x8
        return v.toString(16)
    })
}

const init = async () => {
    let resStr = `
import * as path from "path";
import { FileUtil } from 'tools-mock';
const fileUrl = path.join(__dirname, "./md");
export const md = {`
    if (FileUtil.exists(dirUrl, false)) {
        await FileUtil.readDirectory(dirUrl, async (filepath) => {
            // console.log(filepath);
            let str = await FileUtil.readFile(filepath)
            str = JSON.parse(str)

            let itemStrRes = undefined as any
            let itemStrStart = undefined as any

            const strArr = [] as any[]
            Object.keys(str).forEach((key) => {
                strArr.push(str[key])
            })

            if (strArr[0].res?.code == 0) {
                console.log(strArr[0].url)
            }

            for (let i = 0; i < strArr.length; i++) {
                const item = strArr[i]

                let itemReq = item.req
                let reqIsJSON = false
                try {
                    itemReq = JSON.stringify(itemReq)
                    reqIsJSON = true
                } catch (error) {}

                let itemRes = item.res
                let resIsJSON = false
                try {
                    itemRes = JSON.stringify(itemRes)
                    resIsJSON = true
                } catch (error) {}

                if (itemRes == undefined) {
                    const _arr = strArr.filter((item) => item.res !== undefined)
                    if (_arr.length > 0) itemRes = JSON.stringify(_arr[0].res)
                    else itemRes = ''
                }

                const fileid = uuid()
                FileUtil.writeFile(path.join(fileUrl, `${fileid}`), itemRes)
                if (resIsJSON) {
                    itemRes = itemRes.replace(/\r/g, '').replace(/\n/g, '').replace(/\t/g, '').replace(/\"/g, '\\"')
                    itemRes = itemRes.replace(/\\r/g, '\\\\r').replace(/\\n/g, '\\\\n').replace(/\\t/g, '\\\\t')
                }
                if (itemStrRes === undefined) {
                    itemStrRes = `
                '${item.url}': {
                    method: '${item.method}',
                    fun: async (data:any) => {
                        console.log('${item.url}',data);
            
            `

                    if (reqIsJSON) {
                        itemStrRes += `
                data = JSON.parse(data)
            `
                    }

                    itemStrStart = ` 
          let str = await FileUtil.readFile(path.join(fileUrl, '${fileid}'))
          return str
          `
                }

                if (reqIsJSON && item.req?.t && item.req?.id) {
                    itemStrRes += `
              if((data.id+'') == '${item.req.id}'){
                let str = await FileUtil.readFile(path.join(fileUrl, '${fileid}'))
                return str
              }
          `
                } else {
                    itemStrRes += `
            if(data==\`${encodeURI(itemReq)}\`) {
              let str = await FileUtil.readFile(path.join(fileUrl, '${fileid}'))
              return str
            }
        `
                }
            }

            itemStrRes += `
                        ${itemStrStart}
                    }
                },

                `

            resStr += itemStrRes
            // console.log(resStr);
        })
    }
    resStr += '}'
    const tsUrl = path.join(__dirname, './md.ts')
    FileUtil.writeFile(tsUrl, resStr)
}

init()
