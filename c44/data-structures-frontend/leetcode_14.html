<!DOCTYPE html>
<html>

<head>
  <meta charset="UTF-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link rel="stylesheet" href="css/prism.css">
  <style>
    html,
    body {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
      height: 100%;
      font-size: 12px;
    }

    body {
      min-height: 500px;
    }

    section {
      display: flex;
      flex-wrap: wrap;
    }

    .code {
      margin-top: 3px;
    }

    pre[class*=language-] {
      margin: 0;
      padding: 0;
    }

    main {
      border-top: 2px solid #ccc;
      width: 100%;
      height: 65%;
      min-height: 200px;
    }
  </style>
  <title>Leetcode 14 - 最长公共前缀</title>
</head>

<body>
  <section class="frames"></section>
  <section style="display: none;">
    <pre><code class="language-java">int factorial(int n) {
    if (n == 1) {
        return 1;
    }
    return n * factorial(n - 1);
}</code></pre>
  </section>
  <main></main>
  <section>
    <div style='background-color:#00FF99; margin: 2px 2px 0 0; padding: 4px 6px;'>已匹配</div>
  </section>
  <div style='margin: 2px 2px 0 0; padding: 4px 6px;'>
    <span>动画速度(ms)&nbsp;</span><input type="number" step="100" value="300" id="animate_speed" class="saveable int"
      style="width: 40px;">
    <span>矩形宽</span><input type="number" step="1" value="30" id="RECT_WIDTH" class="saveable int" style="width: 40px;">
    <span>矩形高</span><input type="number" step="1" value="20" id="RECT_HEIGHT" class="saveable int" style="width: 40px;">
    <input style='font-size:12px;' type="button" value="保存" onclick="onSave('leetcode_14')">
  </div>
  <script src="js/p5.js"></script>
  <script src="js/p5-svg.js"></script>
  <script src="js/util.js"></script>
  <script src="js/prism.js"></script>
  <script>
    const options = loadOptionsFromStorage('leetcode_14')
    const d = new Draw(options.animate_speed)
    const RECT_WIDTH = options.RECT_WIDTH
    const RECT_HEIGHT = options.RECT_HEIGHT
    const myString = [
      // "fl", // 情况3
      "flower",
      "flow",
      // "fl", // 情况2
      "flight",
      "fly"
    ]
    function setup() {
      const WIN_WIDTH = document.querySelector('main').clientWidth
      const WIN_HEIGHT = document.querySelector('main').clientHeight
      const FONT_SIZE = 16
      createCanvas(WIN_WIDTH, WIN_HEIGHT, SVG)
      textSize(FONT_SIZE)
      textAlign(CENTER)
      longestCommonPrefix()
      d.updateFrameButtons()
    }
    function draw() {
      d.draw(() => background('#eee'), () => true)
    }
    function frame({ cloned: node }) {
      if (node) {
        const startX = width / 2 - myString[0].length / 2 * RECT_WIDTH
        const startY = height / 2
        let x = startX
        let y = startY
        for (let j = 0; j < myString.length; j++) {
          const str = myString[j]
          for (let i = 0; i < str.length; i++) {
            stroke('black')
            strokeWeight(1)
            if (i == node.i && j <= node.j) {
              fill('rgba(0,255,0,.5)')
            } else {
              fill('white')
            }
            const ch = str[i]
            rect(x, y, RECT_WIDTH, RECT_HEIGHT)

            noStroke()
            fill('black')
            text(ch, x + RECT_WIDTH / 2, y + RECT_HEIGHT / 2 + 5)

            x += RECT_WIDTH
          }
          x = startX
          y += RECT_HEIGHT
        }
        x = startX
        y = startY
        noStroke()
        fill('red')
        text('i', x + node.i * RECT_WIDTH + RECT_WIDTH / 2, y - 8)
        text('j', x - 12, y + node.j * RECT_HEIGHT + RECT_HEIGHT / 2 + 5)
      }
    }

    let maxSub = { i: 0, length: 1 }
    function longestCommonPrefix() {
      const first = myString[0]
      for (let i = 0; i < first.length; i++) {
        d.add({ cloned: clone({ i, j: 0 }) }, frame)
        const ch = first[i]
        for (let j = 1; j < myString.length; j++) {
          if (i == myString[j].length || ch != myString[j][i]) {
            return
          } else {
            d.add({ cloned: clone({ i, j }) }, frame)
          }
        }
      }
      return first
    }

    function clone(tree) {
      return JSON.parse(JSON.stringify(tree))
    }

  </script>
</body>

</html>