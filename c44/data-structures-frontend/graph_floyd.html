<!DOCTYPE html>
<html lang="zh">

<head>
  <meta charset="UTF-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Floyd-<PERSON>hall</title>
  <style>
    html,
    body {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
      height: 100%;
      font-size: 12px;
    }

    body {
      min-height: 500px;
    }

    #cy {
      width: 100%;
      height: 100%;
      display: block;
      background-color: #eee;
    }
  </style>
</head>

<body>
  <div id="cy"></div>
  <script src="js/cytoscape.min.js"></script>
  <script>
    var cy = cytoscape({
      container: document.getElementById('cy'), // container to render in
      /* elements: [ // list of graph elements to start with
        {
          data: { id: 'v1' }
        },
        {
          data: { id: 'v2' }
        },
        {
          data: { id: 'v3' }
        },
        {
          data: { id: 'v4' }
        },
        {
          data: { id: 'e1', source: 'v2', target: 'v1', weight: "4" }
        },
        {
          data: { id: 'e2', source: 'v1', target: 'v3', weight: "-2" }
        },
        {
          data: { id: 'e3', source: 'v2', target: 'v3', weight: "3" }
        },
        {
          data: { id: 'e4', source: 'v3', target: 'v4', weight: "2" }
        },
        {
          data: { id: 'e5', source: 'v4', target: 'v2', weight: "-1" }
        }
      ], */

      elements: [ // list of graph elements to start with
        {
          data: { id: 'v1' }
        },
        {
          data: { id: 'v2' }
        },
        {
          data: { id: 'v3' }
        },
        { 
          data: { id: 'e1', source: 'v1', target: 'v3', weight: "2" }
        },
        {
          data: { id: 'e2', source: 'v1', target: 'v2', weight: "4" }
        },
        {
          data: { id: 'e3', source: 'v2', target: 'v3', weight: "1" }
        },
        {
          data: { id: 'e4', source: 'v3', target: 'v2', weight: "1" }
        },
        {
          data: { id: 'e5', source: 'v3', target: 'v1', weight: "2" }
        }
      ],

      style: [ // the stylesheet for the graph
        {
          selector: 'node',
          style: {
            'background-color': '#666',
            'label': 'data(id)'
          }
        },

        {
          selector: 'edge',
          style: {
            'content': 'data(weight)',
            'width': 3,
            'line-color': '#888',
            'target-arrow-color': '#888',
            'target-arrow-shape': 'triangle',
            'curve-style': 'bezier'
          }
        },

        {
          selector: ':selected',
          style: {
            'background-color': 'red',
            'line-color': 'red',
          }
        }
      ],

      layout: {
        name: 'grid',
        rows: 2
      }
    });

  </script>
</body>

</html>