<!DOCTYPE html>
<html lang="zh">

<head>
  <meta charset="utf-8"/>
  <link rel="stylesheet" href="css/prism.css">
  <style>
    html,
    body {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
      height: 100%;
      font-size: 12px;
    }

    body {
      min-height: 500px;
    }

    section {
      display: flex;
      flex-wrap: wrap;
    }

    .code {
      margin-top: 3px;
    }

    pre[class*=language-] {
      margin: 0;
      padding: 0;
    }

    main {
      border-top: 2px solid #ccc;
      width: 100%;
      height: 66%;
      min-height: 400px;
    }
  </style>
  <title>优先级队列(无序数组)</title>
</head>

<body>
  <section class="frames"></section>
  <div class="code" style="display: none;">
    <pre><code class="language-java">public static int binarySearch(int[] a, int target) {
    int i = 0, j = a.length - 1;
    int candidate = -1;
    while (i <= j) {
        int m = (i + j) >>> 1;
        if (target < a[m]) {          // 在左边
            j = m - 1;
        } else if (a[m] < target) {   // 在右边
            i = m + 1;
        } else {
            candidate = m;
            j = m - 1;
        }
    }
    return candidate;
}</code></pre>
  </div>
  <main></main>
  <section>
    <div style='background-color:#cc99cd; margin: 2px 2px 0 0; padding: 4px 6px;'>索引</div>
    <div style='background-color:#67cdcc; margin: 2px 2px 0 0; padding: 4px 6px;'>数据</div>
    <div style='background-color:#f08d49; margin: 2px 2px 0 0; padding: 4px 6px;'>比较</div>
  </section>
  <div style='margin: 2px 2px 0 0; padding: 4px 6px;'>
    <div style="margin-bottom: 2px;">
      <span>初始队列&nbsp;</span><input type="text" id='initQueue' class="saveable array" value="4,2,1,10,7">
    </div>
    <div style="margin-bottom: 2px;">
      <span>入队&nbsp;</span><input type="text" id='offered' class="saveable" value="3">
      <input style='font-size:12px;' type="button" value="offer()" onclick="offer()">
    </div>
    <div style="margin-bottom: 2px;">
      <span>出队&nbsp;</span><input style='font-size:12px;' type="button" value="poll()" onclick="poll()">
    </div>
  </div>
  <div style='margin: 2px 2px 0 0; padding: 4px 6px;'>
    <span>动画速度(ms)&nbsp;</span><input type="number" step="100" value="300" id="animate_speed" class="saveable int"
      style="width: 40px;">
    <span>直径</span><input type="number" step="1" value="30" id="DIAMETER" class="saveable int" style="width: 40px;">
    <input style='font-size:12px;' type="button" value="保存" onclick="onSave('priority_queue_1')">
  </div>
  <script src="js/p5.js"></script>
  <script src="js/p5-svg.js"></script>
  <script src="js/util.js"></script>
  <script src="js/prism.js"></script>
  <script>
    function poll() {
      if (dataArray.length == 0) {
        return
      }
      let max = 0
      let i = 1
      while (i < dataArray.length) {
        d.add({ array: dataArray, pointers: [{ index: i, text: 'i' }, { index: max, text: 'M' }] }, frame)
        if (dataArray[i] > dataArray[max]) {
          max = i
          d.add({ array: dataArray, pointers: [{ index: i, text: 'i' }, { index: max, text: 'M' }] }, frame)
        }
        i++
      }
      d.add({ array: dataArray, highlights: [max], pointers: [{ index: i, text: 'i' }, { index: max, text: 'M' }] }, frame)
      dataArray.splice(max, 1)
      d.add({ array: dataArray }, frame)
      d.updateFrameButtons()
    }

    function offer() {
      const offered = Number(document.querySelector('#offered').value)
      dataArray.push(offered)
      d.add({ array: dataArray, highlights: [dataArray.length - 1] }, frame)
      d.add({ array: dataArray }, frame)
      d.updateFrameButtons()
    }

    const options = loadOptionsFromStorage('priority_queue_1')
    const DIAMETER = options.DIAMETER
    const d = new Draw(options.animate_speed)
    const NODE_LEFT_PAD = 250
    const LEFT_PAD = 20
    const TOP_PAD = 40

    const dataArray = options.initQueue

    function setup() {
      const WIN_WIDTH = document.querySelector('main').clientWidth
      const WIN_HEIGHT = document.querySelector('main').clientHeight
      const FONT_SIZE = 10
      createCanvas(WIN_WIDTH, WIN_HEIGHT, SVG)
      textSize(FONT_SIZE)
      textAlign(CENTER)
      d.add({ array: dataArray }, frame)
      d.updateFrameButtons()
    }
    function draw() {
      d.draw(() => background('#eee'))
    }

    /*
      array: 数组
      pointers: 指针
      highlights: 高亮位置
      lineNumber: 高亮行号
    */
    function frame({ array, pointers, highlights, lineNumber }) {
      const pre = document.querySelector('pre')
      if (lineNumber > 0) {
        pre.setAttribute('data-line', lineNumber)
        Prism.highlightAllUnder(pre)
      }

      let x = LEFT_PAD, y = height - TOP_PAD - DIAMETER / 2
      for (let i = 0; i < array.length; i++) {
        stroke(0)
        pointers.draw2(i, y, x + DIAMETER)
        highlights.includes(i) ? fill('#f08d49') : fill('#67cdcc')
        rect(x + DIAMETER, y, DIAMETER, DIAMETER)
        fill('#ffffff')
        noStroke()
        text(array[i], x + DIAMETER + DIAMETER / 2, y + DIAMETER / 2 + 4)

        stroke(0)
        fill('#cc99cd')
        rect(x, y, DIAMETER, DIAMETER)
        fill('#ffffff')
        noStroke()
        text(i, x + DIAMETER / 2, y + DIAMETER / 2 + 4)

        y -= DIAMETER
      }
    }
  </script>
</body>

</html>
