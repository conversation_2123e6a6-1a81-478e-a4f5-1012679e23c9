<!DOCTYPE html><!-- Last Published: Tue Jul 01 2025 05:53:47 GMT+0000 (Coordinated Universal Time) -->
<html data-wf-domain="java-tech-blog.com" data-wf-page="6787c53671a97b8708dd6ff4"
      data-wf-site="6787c53671a97b8708dd7000" lang="zh-CN">
<head>
    <meta charset="utf-8"/>
    <title>Java技术博客 |  算法与数据结构 | AI技术分享</title>
    <meta content="专注于Java开发、数据结构与算法、人工智能技术的个人博客。分享编程实践、技术见解和学习心得。Blog focused on Java development, data structures, algorithms, and AI technology sharing."
          name="description"/>
    <meta content="Java技术博客 |  算法与数据结构 | AI技术分享" property="og:title"/>
    <meta content="专注于Java开发、数据结构与算法、人工智能技术的个人博客。分享编程实践、技术见解和学习心得。Blog focused on Java development, data structures, algorithms, and AI technology sharing."
          property="og:description"/>
    <meta content="images/java-developer-og.jpg"
          property="og:image"/>
    <meta content="Java技术博客 |  算法与数据结构 | AI技术分享"
          property="twitter:title"/>
    <meta content="专注于Java开发、数据结构与算法、人工智能技术的个人博客。分享编程实践、技术见解和学习心得。Blog focused on Java development, data structures, algorithms, and AI technology sharing."
          property="twitter:description"/>
    <meta content="images/java-developer-og.jpg"
          property="twitter:image"/>
    <meta property="og:type" content="website"/>
    <meta content="summary_large_image" name="twitter:card"/>
    <meta content="width=device-width, initial-scale=1" name="viewport"/>
    <link href="6787c53671a97b8708dd7000/css/dayanjayatilleka.webflow.shared.4502b6320.css"
          rel="stylesheet" type="text/css"/>
    <style>@media (min-width: 992px) {
        html.w-mod-js:not(.w-mod-ix) [data-w-id="08b32825-e172-acb3-ad21-9c4c2e11bd32"] {
            color: rgb(188, 136, 74);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="a9b6a89d-ff68-0b9c-0774-b50512a4832b"] {
            -webkit-transform: translate3d(0, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0deg) skew(0, 0);
            -moz-transform: translate3d(0, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0deg) skew(0, 0);
            -ms-transform: translate3d(0, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0deg) skew(0, 0);
            transform: translate3d(0, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0deg) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="a9b6a89d-ff68-0b9c-0774-b50512a4832a"] {
            opacity: 0;
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="5f0560bb-80ad-8019-e00e-3ad727b6da62"] {
            width: 0em;
            height: 0em;
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="f21f91db-54e6-d396-3c09-9197eae917d4"] {
            -webkit-transform: translate3d(0%, null, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -moz-transform: translate3d(0%, null, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -ms-transform: translate3d(0%, null, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            transform: translate3d(0%, null, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="08b32825-e172-acb3-ad21-9c4c2e11bd34"] {
            color: rgb(37, 35, 33);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="ddbb4ddb-e257-1f4c-8d55-76ecdc5627fe"] {
            width: 0%;
            height: 100%;
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="e74e48ca-6357-163d-9b00-460c023d37b0"] {
            color: rgb(37, 35, 33);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="e74e48ca-6357-163d-9b00-460c023d37b2"] {
            background-color: rgb(37, 35, 33);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="e74e48ca-6357-163d-9b00-460c023d37b3"] {
            background-color: rgb(246, 244, 241);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="ef9a8e5c-6b32-eb56-2877-2edd970241db"] {
            color: rgb(37, 35, 33);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="ef9a8e5c-6b32-eb56-2877-2edd970241dd"] {
            background-color: rgb(37, 35, 33);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="ef9a8e5c-6b32-eb56-2877-2edd970241de"] {
            opacity: 0;
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="ec8cd256-8985-f22f-1304-1154057dfb10"] {
            -webkit-transform: translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -moz-transform: translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -ms-transform: translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            transform: translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="a4efb18a-b1c1-a66b-e40e-8445e5b25a25"] {
            -webkit-transform: translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -moz-transform: translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -ms-transform: translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            transform: translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="3ef64cd4-7c47-6f5a-9839-05794a60ae8c"] {
            background-color: rgb(37, 35, 33);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="3ef64cd4-7c47-6f5a-9839-05794a60ae8d"] {
            opacity: 0;
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="c867052f-c484-f03c-9bee-e41888692301"] {
            opacity: 1;
            display: none;
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="b2880d8b-a9a6-4339-bbf4-83eec2220769"] {
            opacity: 1;
            -webkit-transform: translate3d(0, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0deg) skew(0, 0);
            -moz-transform: translate3d(0, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0deg) skew(0, 0);
            -ms-transform: translate3d(0, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0deg) skew(0, 0);
            transform: translate3d(0, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0deg) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="785f348f-1e0c-89e5-4e43-6d937c68dd88"] {
            display: flex;
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="785f348f-1e0c-89e5-4e43-6d937c68dd8d"] {
            width: 100%;
            height: 100%;
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="1ec6c8a1-2dc4-46d9-1190-d49a130355dd"] {
            -webkit-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -moz-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -ms-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="3ef0f252-de49-f4de-e4e1-f1029246cb9d"] {
            -webkit-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -moz-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -ms-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="1ec6c8a1-2dc4-46d9-1190-d49a130355df"] {
            -webkit-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -moz-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -ms-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="3ef0f252-de49-f4de-e4e1-f1029246cb9f"] {
            -webkit-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -moz-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -ms-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="1ec6c8a1-2dc4-46d9-1190-d49a130355e1"] {
            -webkit-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -moz-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -ms-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="3ef0f252-de49-f4de-e4e1-f1029246cba1"] {
            -webkit-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -moz-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -ms-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="16eef9e7-4006-8f37-44b3-ae591327adc1"] {
            -webkit-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -moz-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -ms-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="1ec6c8a1-2dc4-46d9-1190-d49a130355e3"] {
            -webkit-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -moz-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -ms-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="3ef0f252-de49-f4de-e4e1-f1029246cba3"] {
            -webkit-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -moz-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -ms-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="ceb2db9c-2ce8-649d-1855-9d319e9e85da"] {
            -webkit-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -moz-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -ms-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="1ec6c8a1-2dc4-46d9-1190-d49a130355e5"] {
            -webkit-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -moz-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -ms-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="3ef0f252-de49-f4de-e4e1-f1029246cba5"] {
            -webkit-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -moz-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -ms-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="785f348f-1e0c-89e5-4e43-6d937c68dd9b"] {
            -webkit-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -moz-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -ms-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="1ec6c8a1-2dc4-46d9-1190-d49a130355e7"] {
            -webkit-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -moz-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -ms-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="88222aec-0acc-cf38-0cd2-b81a6a934976"] {
            -webkit-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -moz-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -ms-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="3feac868-a1a3-9f35-f96f-051ef4976984"] {
            -webkit-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -moz-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -ms-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="1ec6c8a1-2dc4-46d9-1190-d49a130355e9"] {
            -webkit-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -moz-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -ms-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="30208dc1-b4c4-675b-5742-7b25f05f702d"] {
            -webkit-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -moz-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -ms-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="1ec6c8a1-2dc4-46d9-1190-d49a130355eb"] {
            -webkit-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -moz-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -ms-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="1ec6c8a1-2dc4-46d9-1190-d49a130355ed"] {
            -webkit-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -moz-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -ms-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="2aef2651-27e2-7123-341d-ebc0c97f7c0d"] {
            -webkit-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -moz-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -ms-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="91806017-2f8a-eb5a-34a9-ee454ea3a757"] {
            width: 100%;
            height: 100%;
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="91806017-2f8a-eb5a-34a9-ee454ea3a75c"] {
            width: 100%;
            height: 100%;
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="91806017-2f8a-eb5a-34a9-ee454ea3a763"] {
            width: 100%;
            height: 100%;
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="91806017-2f8a-eb5a-34a9-ee454ea3a76e"] {
            width: 100%;
            height: 100%;
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="c77b6c64-5bfe-080d-9e72-d287a64c7e04"] {
            width: 100%;
            height: 100%;
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="bb3186cc-ab0e-d293-f9e7-16a6d7cce449"] {
            width: 100%;
            height: 100%;
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="b83ce29c-b6c8-9966-d1f2-2b88cc21986b"] {
            width: 100%;
            height: 100%;
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="b83ce29c-b6c8-9966-d1f2-2b88cc219871"] {
            width: 100%;
            height: 100%;
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="97bf51a5-4ea3-36e9-15c0-1f47a93f76c4"] {
            width: 100%;
            height: 100%;
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="b83ce29c-b6c8-9966-d1f2-2b88cc219882"] {
            width: 100%;
            height: 100%;
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="b83ce29c-b6c8-9966-d1f2-2b88cc21988b"] {
            width: 100%;
            height: 100%;
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="5859b40a-043e-bc39-0860-c8366a4b5b1f"] {
            width: 100%;
            height: 100%;
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="631c39c8-3c5e-df62-bce8-e4b7b6e6bd52"] {
            -webkit-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -moz-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -ms-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="8f279a91-90ae-c725-84e7-f60f566262f7"] {
            width: 100%;
            height: 100%;
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="2e7d28b4-765d-ccce-9e43-d4dbf2307c8b"] {
            width: 100%;
            height: 100%;
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="9d0e3cde-dfa7-0dda-c180-ec7ad0373727"] {
            display: none;
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="b083bb2f-a78a-8b36-7a69-25ba88d151cd"] {
            width: 100%;
            height: 50%;
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="18b2d837-1acd-4b1e-2bcd-455984cc3ecd"] {
            width: 100%;
            height: 50%;
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="e0cb3584-c9c5-5972-8903-b9f5463ce844"] {
            color: rgb(37, 35, 33);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="e0cb3584-c9c5-5972-8903-b9f5463ce842"] {
            background-color: rgb(37, 35, 33);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="e0cb3584-c9c5-5972-8903-b9f5463ce846"] {
            opacity: 0;
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="81d3b725-19fd-b0c8-b1a9-fcd0b8fb0f01"] {
            color: rgb(37, 35, 33);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="81d3b725-19fd-b0c8-b1a9-fcd0b8fb0eff"] {
            background-color: rgb(37, 35, 33);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="81d3b725-19fd-b0c8-b1a9-fcd0b8fb0f03"] {
            opacity: 0;
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="88b8edde-dc24-7e76-4179-3612e00e1459"] {
            color: rgb(37, 35, 33);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="88b8edde-dc24-7e76-4179-3612e00e1457"] {
            background-color: rgb(37, 35, 33);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="88b8edde-dc24-7e76-4179-3612e00e145b"] {
            opacity: 0;
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="d148cf4d-dcde-504e-2ad7-a831118cb25c"] {
            opacity: 0;
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="0d4ca99a-ae50-8383-2bce-135328286865"] {
            -webkit-transform: translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -moz-transform: translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -ms-transform: translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            transform: translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="2f75b7c9-2d9f-76fa-8081-40c94c767144"] {
            -webkit-transform: translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -moz-transform: translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -ms-transform: translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            transform: translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="0d4ca99a-ae50-8383-2bce-135328286864"] {
            background-color: rgb(237, 231, 223);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="0d4ca99a-ae50-8383-2bce-135328286869"] {
            color: rgb(37, 35, 33);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="c236cb0a-85e5-b566-08db-5bc35e7b8080"] {
            color: rgb(37, 35, 33);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="291100c1-1af9-60fe-d690-0b9b45d03e0e"] {
            color: rgb(37, 35, 33);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="291100c1-1af9-60fe-d690-0b9b45d03e12"] {
            color: rgb(37, 35, 33);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="2f75b7c9-2d9f-76fa-8081-40c94c767147"] {
            color: rgb(37, 35, 33);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="2f75b7c9-2d9f-76fa-8081-40c94c76714b"] {
            color: rgb(37, 35, 33);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="2f75b7c9-2d9f-76fa-8081-40c94c767150"] {
            color: rgb(37, 35, 33);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="2f75b7c9-2d9f-76fa-8081-40c94c767154"] {
            color: rgb(37, 35, 33);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="0d4ca99a-ae50-8383-2bce-13532828686b"] {
            background-color: rgb(37, 35, 33);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="0d4ca99a-ae50-8383-2bce-13532828686f"] {
            background-color: rgb(37, 35, 33);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="291100c1-1af9-60fe-d690-0b9b45d03e10"] {
            background-color: rgb(37, 35, 33);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="291100c1-1af9-60fe-d690-0b9b45d03e14"] {
            background-color: rgb(37, 35, 33);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="2f75b7c9-2d9f-76fa-8081-40c94c767149"] {
            background-color: rgb(37, 35, 33);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="2f75b7c9-2d9f-76fa-8081-40c94c76714d"] {
            background-color: rgb(37, 35, 33);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="2f75b7c9-2d9f-76fa-8081-40c94c767152"] {
            background-color: rgb(37, 35, 33);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="2f75b7c9-2d9f-76fa-8081-40c94c767156"] {
            background-color: rgb(37, 35, 33);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="3e239b0a-e3c6-cd31-0686-592f50448add"] {
            width: 60%;
            height: 80%;
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="3e239b0a-e3c6-cd31-0686-592f50448adc"] {
            display: none;
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="3e239b0a-e3c6-cd31-0686-592f50448ade"] {
            -webkit-transform: translate3d(0, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(540deg) skew(0, 0);
            -moz-transform: translate3d(0, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(540deg) skew(0, 0);
            -ms-transform: translate3d(0, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(540deg) skew(0, 0);
            transform: translate3d(0, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(540deg) skew(0, 0);
            width: 0%;
            height: 0%;
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="3c2dc2c1-5833-7fbf-5033-b59166803337"] {
            background-color: rgb(37, 35, 33);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="715b3b6f-7bc7-298e-16f3-8ddda55edd83"] {
            -webkit-transform: translate3d(0, 100%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -moz-transform: translate3d(0, 100%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -ms-transform: translate3d(0, 100%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            transform: translate3d(0, 100%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="707df24f-f864-0c9d-b999-ad6ddf782419"] {
            color: rgb(37, 35, 33);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="5da752f5-780c-720a-e516-a8bf34072883"] {
            color: rgb(37, 35, 33);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="8a490c9d-0ef7-2ef7-5585-918bfadf178a"] {
            color: rgb(37, 35, 33);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="14985a2d-3227-5db4-1a41-9635056eb79f"] {
            color: rgb(37, 35, 33);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="0377e523-1715-1e95-92d4-bafc90038219"] {
            color: rgb(37, 35, 33);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="4823101c-4d0d-b692-61f3-2bdba51b8721"] {
            color: rgb(37, 35, 33);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="aa49dcfb-20db-4ce5-5274-089aebfe2f73"] {
            color: rgb(37, 35, 33);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="09042df4-67b2-28ad-9a1d-beccdf40ba44"] {
            color: rgb(37, 35, 33);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="d95a7c84-126d-6785-d94c-09f3f7f69209"] {
            color: rgb(37, 35, 33);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="9b380cec-cc56-2ccc-675c-9dc3f60565e2"] {
            color: rgb(37, 35, 33);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="7058f3a0-3516-62f6-0fac-c906b083a561"] {
            color: rgb(37, 35, 33);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="31296c1e-d06b-423d-477a-68d4b5a4e496"] {
            color: rgb(37, 35, 33);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="ecd28db9-3bec-84a1-83d0-4ceed96aa403"] {
            color: rgb(37, 35, 33);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="707df24f-f864-0c9d-b999-ad6ddf78241c"] {
            color: rgb(37, 35, 33);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="707df24f-f864-0c9d-b999-ad6ddf782422"] {
            color: rgb(37, 35, 33);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="2b797b82-22e3-b87e-893d-5fd1f42f3269"] {
            color: rgb(37, 35, 33);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="2b797b82-22e3-b87e-893d-5fd1f42f3267"] {
            background-color: rgb(37, 35, 33);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="2b797b82-22e3-b87e-893d-5fd1f42f326b"] {
            opacity: 0;
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="2b797b82-22e3-b87e-893d-5fd1f42f3270"] {
            color: rgb(37, 35, 33);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="2b797b82-22e3-b87e-893d-5fd1f42f326e"] {
            background-color: rgb(37, 35, 33);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="2b797b82-22e3-b87e-893d-5fd1f42f3272"] {
            opacity: 0;
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="f9de1fa6-2fb0-5f42-3d95-b5ae80d7e08d"] {
            -webkit-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(290deg) skew(0, 0);
            -moz-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(290deg) skew(0, 0);
            -ms-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(290deg) skew(0, 0);
            transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(290deg) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="bdbad17b-1e36-cdba-8231-14734c34943c"] {
            -webkit-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(320deg) skew(0, 0);
            -moz-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(320deg) skew(0, 0);
            -ms-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(320deg) skew(0, 0);
            transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(320deg) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="21107a80-5e47-ee59-4874-03b95c8c85d6"] {
            -webkit-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(-90deg) skew(0, 0);
            -moz-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(-90deg) skew(0, 0);
            -ms-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(-90deg) skew(0, 0);
            transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(-90deg) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="3f95ff90-f033-7532-4be1-0d37a695f2d3"] {
            -webkit-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(180deg) skew(0, 0);
            -moz-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(180deg) skew(0, 0);
            -ms-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(180deg) skew(0, 0);
            transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(180deg) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="88f8031c-a8bc-8659-b9a6-6603aefd0cd8"] {
            -webkit-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(-270deg) skew(0, 0);
            -moz-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(-270deg) skew(0, 0);
            -ms-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(-270deg) skew(0, 0);
            transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(-270deg) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="ed6164aa-06fa-e5c8-6d52-40cca4c57c36"] {
            -webkit-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(180deg) skew(0, 0);
            -moz-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(180deg) skew(0, 0);
            -ms-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(180deg) skew(0, 0);
            transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(180deg) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="651632ce-cef5-77d9-2a58-e6d0b1aa1e28"] {
            -webkit-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(240deg) skew(0, 0);
            -moz-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(240deg) skew(0, 0);
            -ms-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(240deg) skew(0, 0);
            transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(240deg) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="7c7eac27-6551-73d6-2cce-ba4945ee6cae"] {
            opacity: 0;
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="3173c8c1-435d-9bfc-5ef4-8b2a00edfce4"] {
            -webkit-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(240deg) skew(0, 0);
            -moz-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(240deg) skew(0, 0);
            -ms-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(240deg) skew(0, 0);
            transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(240deg) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="3173c8c1-435d-9bfc-5ef4-8b2a00edfce7"] {
            -webkit-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(-360deg) skew(0, 0);
            -moz-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(-360deg) skew(0, 0);
            -ms-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(-360deg) skew(0, 0);
            transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(-360deg) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="3173c8c1-435d-9bfc-5ef4-8b2a00edfcea"] {
            -webkit-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(200deg) skew(0, 0);
            -moz-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(200deg) skew(0, 0);
            -ms-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(200deg) skew(0, 0);
            transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(200deg) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="3173c8c1-435d-9bfc-5ef4-8b2a00edfced"] {
            -webkit-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(180deg) skew(0, 0);
            -moz-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(180deg) skew(0, 0);
            -ms-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(180deg) skew(0, 0);
            transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(180deg) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="3173c8c1-435d-9bfc-5ef4-8b2a00edfcf0"] {
            -webkit-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(240deg) skew(0, 0);
            -moz-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(240deg) skew(0, 0);
            -ms-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(240deg) skew(0, 0);
            transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(240deg) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="3173c8c1-435d-9bfc-5ef4-8b2a00edfcf3"] {
            -webkit-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -moz-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -ms-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="3173c8c1-435d-9bfc-5ef4-8b2a00edfcf4"] {
            -webkit-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -moz-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -ms-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="3173c8c1-435d-9bfc-5ef4-8b2a00edfcf7"] {
            -webkit-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -moz-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -ms-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="0eb141e7-cdfe-aca1-6f88-90ae0d02171f"] {
            opacity: 0;
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="98c9f5c6-e612-4115-6e05-62f2f36050fd"] {
            -webkit-transform: translate3d(0, 0, 0) scale3d(1, 1, 1) rotateX(-50deg) rotateY(null) rotateZ(0) skew(-30deg, 0);
            -moz-transform: translate3d(0, 0, 0) scale3d(1, 1, 1) rotateX(-50deg) rotateY(null) rotateZ(0) skew(-30deg, 0);
            -ms-transform: translate3d(0, 0, 0) scale3d(1, 1, 1) rotateX(-50deg) rotateY(null) rotateZ(0) skew(-30deg, 0);
            transform: translate3d(0, 0, 0) scale3d(1, 1, 1) rotateX(-50deg) rotateY(null) rotateZ(0) skew(-30deg, 0);
            transform-style: preserve-3d;
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="0651746c-3bae-bf6a-7116-2282832fad80"] {
            width: 100%;
        }
    }

    @media (max-width: 991px) and (min-width: 768px) {
        html.w-mod-js:not(.w-mod-ix) [data-w-id="f9de1fa6-2fb0-5f42-3d95-b5ae80d7e08d"] {
            -webkit-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(290deg) skew(0, 0);
            -moz-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(290deg) skew(0, 0);
            -ms-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(290deg) skew(0, 0);
            transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(290deg) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="bdbad17b-1e36-cdba-8231-14734c34943c"] {
            -webkit-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(320deg) skew(0, 0);
            -moz-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(320deg) skew(0, 0);
            -ms-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(320deg) skew(0, 0);
            transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(320deg) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="21107a80-5e47-ee59-4874-03b95c8c85d6"] {
            -webkit-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(-90deg) skew(0, 0);
            -moz-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(-90deg) skew(0, 0);
            -ms-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(-90deg) skew(0, 0);
            transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(-90deg) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="3f95ff90-f033-7532-4be1-0d37a695f2d3"] {
            -webkit-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(180deg) skew(0, 0);
            -moz-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(180deg) skew(0, 0);
            -ms-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(180deg) skew(0, 0);
            transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(180deg) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="88f8031c-a8bc-8659-b9a6-6603aefd0cd8"] {
            -webkit-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(-270deg) skew(0, 0);
            -moz-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(-270deg) skew(0, 0);
            -ms-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(-270deg) skew(0, 0);
            transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(-270deg) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="ed6164aa-06fa-e5c8-6d52-40cca4c57c36"] {
            -webkit-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(180deg) skew(0, 0);
            -moz-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(180deg) skew(0, 0);
            -ms-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(180deg) skew(0, 0);
            transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(180deg) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="651632ce-cef5-77d9-2a58-e6d0b1aa1e28"] {
            -webkit-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(240deg) skew(0, 0);
            -moz-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(240deg) skew(0, 0);
            -ms-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(240deg) skew(0, 0);
            transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(240deg) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="7c7eac27-6551-73d6-2cce-ba4945ee6cae"] {
            opacity: 0;
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="3173c8c1-435d-9bfc-5ef4-8b2a00edfce4"] {
            -webkit-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(240deg) skew(0, 0);
            -moz-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(240deg) skew(0, 0);
            -ms-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(240deg) skew(0, 0);
            transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(240deg) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="3173c8c1-435d-9bfc-5ef4-8b2a00edfce7"] {
            -webkit-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(-360deg) skew(0, 0);
            -moz-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(-360deg) skew(0, 0);
            -ms-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(-360deg) skew(0, 0);
            transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(-360deg) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="3173c8c1-435d-9bfc-5ef4-8b2a00edfcea"] {
            -webkit-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(200deg) skew(0, 0);
            -moz-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(200deg) skew(0, 0);
            -ms-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(200deg) skew(0, 0);
            transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(200deg) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="3173c8c1-435d-9bfc-5ef4-8b2a00edfced"] {
            -webkit-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(180deg) skew(0, 0);
            -moz-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(180deg) skew(0, 0);
            -ms-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(180deg) skew(0, 0);
            transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(180deg) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="3173c8c1-435d-9bfc-5ef4-8b2a00edfcf0"] {
            -webkit-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(240deg) skew(0, 0);
            -moz-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(240deg) skew(0, 0);
            -ms-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(240deg) skew(0, 0);
            transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(240deg) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="3173c8c1-435d-9bfc-5ef4-8b2a00edfcf3"] {
            -webkit-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -moz-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -ms-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="3173c8c1-435d-9bfc-5ef4-8b2a00edfcf4"] {
            -webkit-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -moz-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -ms-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="3173c8c1-435d-9bfc-5ef4-8b2a00edfcf7"] {
            -webkit-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -moz-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -ms-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="0eb141e7-cdfe-aca1-6f88-90ae0d02171f"] {
            opacity: 0;
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="98c9f5c6-e612-4115-6e05-62f2f36050fd"] {
            -webkit-transform: translate3d(0, 0, 0) scale3d(1, 1, 1) rotateX(-50deg) rotateY(null) rotateZ(0) skew(-30deg, 0);
            -moz-transform: translate3d(0, 0, 0) scale3d(1, 1, 1) rotateX(-50deg) rotateY(null) rotateZ(0) skew(-30deg, 0);
            -ms-transform: translate3d(0, 0, 0) scale3d(1, 1, 1) rotateX(-50deg) rotateY(null) rotateZ(0) skew(-30deg, 0);
            transform: translate3d(0, 0, 0) scale3d(1, 1, 1) rotateX(-50deg) rotateY(null) rotateZ(0) skew(-30deg, 0);
            transform-style: preserve-3d;
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="0651746c-3bae-bf6a-7116-2282832fad80"] {
            width: 100%;
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="3173c8c1-435d-9bfc-5ef4-8b2a00edfd1a"] {
            -webkit-transform: translate3d(0px, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -moz-transform: translate3d(0px, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -ms-transform: translate3d(0px, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            transform: translate3d(0px, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="811ae4a8-b038-47d1-3afd-668e7890f05c"] {
            -webkit-transform: translate3d(0px, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -moz-transform: translate3d(0px, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -ms-transform: translate3d(0px, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            transform: translate3d(0px, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="563cd65e-88c1-1adb-6aa6-5f0ab6ddfcb9"] {
            -webkit-transform: translate3d(0px, -240%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(6deg) skew(0, 0);
            -moz-transform: translate3d(0px, -240%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(6deg) skew(0, 0);
            -ms-transform: translate3d(0px, -240%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(6deg) skew(0, 0);
            transform: translate3d(0px, -240%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(6deg) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="369e7303-69e0-1753-1193-a5c01d4d62d3"] {
            -webkit-transform: translate3d(0px, 240%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(-6deg) skew(0, 0);
            -moz-transform: translate3d(0px, 240%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(-6deg) skew(0, 0);
            -ms-transform: translate3d(0px, 240%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(-6deg) skew(0, 0);
            transform: translate3d(0px, 240%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(-6deg) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="0d4ca99a-ae50-8383-2bce-135328286865"] {
            -webkit-transform: translate3d(0px, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -moz-transform: translate3d(0px, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -ms-transform: translate3d(0px, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            transform: translate3d(0px, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="2f75b7c9-2d9f-76fa-8081-40c94c767144"] {
            -webkit-transform: translate3d(0px, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -moz-transform: translate3d(0px, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -ms-transform: translate3d(0px, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            transform: translate3d(0px, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="ec8cd256-8985-f22f-1304-1154057dfb10"] {
            -webkit-transform: translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -moz-transform: translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -ms-transform: translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            transform: translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="a4efb18a-b1c1-a66b-e40e-8445e5b25a25"] {
            -webkit-transform: translate3d(0px, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -moz-transform: translate3d(0px, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -ms-transform: translate3d(0px, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            transform: translate3d(0px, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="c867052f-c484-f03c-9bee-e41888692301"] {
            opacity: 1;
            display: none;
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="b2880d8b-a9a6-4339-bbf4-83eec2220769"] {
            opacity: 1;
            -webkit-transform: translate3d(0, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0deg) skew(0, 0);
            -moz-transform: translate3d(0, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0deg) skew(0, 0);
            -ms-transform: translate3d(0, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0deg) skew(0, 0);
            transform: translate3d(0, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0deg) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="785f348f-1e0c-89e5-4e43-6d937c68dd88"] {
            display: flex;
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="785f348f-1e0c-89e5-4e43-6d937c68dd8d"] {
            width: 100%;
            height: 100%;
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="1ec6c8a1-2dc4-46d9-1190-d49a130355dd"] {
            -webkit-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -moz-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -ms-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="3ef0f252-de49-f4de-e4e1-f1029246cb9d"] {
            -webkit-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -moz-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -ms-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="1ec6c8a1-2dc4-46d9-1190-d49a130355df"] {
            -webkit-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -moz-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -ms-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="3ef0f252-de49-f4de-e4e1-f1029246cb9f"] {
            -webkit-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -moz-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -ms-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="1ec6c8a1-2dc4-46d9-1190-d49a130355e1"] {
            -webkit-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -moz-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -ms-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="3ef0f252-de49-f4de-e4e1-f1029246cba1"] {
            -webkit-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -moz-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -ms-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="16eef9e7-4006-8f37-44b3-ae591327adc1"] {
            -webkit-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -moz-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -ms-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="1ec6c8a1-2dc4-46d9-1190-d49a130355e3"] {
            -webkit-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -moz-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -ms-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="3ef0f252-de49-f4de-e4e1-f1029246cba3"] {
            -webkit-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -moz-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -ms-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="ceb2db9c-2ce8-649d-1855-9d319e9e85da"] {
            -webkit-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -moz-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -ms-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="1ec6c8a1-2dc4-46d9-1190-d49a130355e5"] {
            -webkit-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -moz-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -ms-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="3ef0f252-de49-f4de-e4e1-f1029246cba5"] {
            -webkit-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -moz-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -ms-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="785f348f-1e0c-89e5-4e43-6d937c68dd9b"] {
            -webkit-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -moz-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -ms-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="1ec6c8a1-2dc4-46d9-1190-d49a130355e7"] {
            -webkit-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -moz-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -ms-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="88222aec-0acc-cf38-0cd2-b81a6a934976"] {
            -webkit-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -moz-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -ms-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="3feac868-a1a3-9f35-f96f-051ef4976984"] {
            -webkit-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -moz-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -ms-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="1ec6c8a1-2dc4-46d9-1190-d49a130355e9"] {
            -webkit-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -moz-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -ms-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="30208dc1-b4c4-675b-5742-7b25f05f702d"] {
            -webkit-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -moz-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -ms-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="1ec6c8a1-2dc4-46d9-1190-d49a130355eb"] {
            -webkit-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -moz-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -ms-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="1ec6c8a1-2dc4-46d9-1190-d49a130355ed"] {
            -webkit-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -moz-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -ms-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="2aef2651-27e2-7123-341d-ebc0c97f7c0d"] {
            -webkit-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -moz-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -ms-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="91806017-2f8a-eb5a-34a9-ee454ea3a757"] {
            width: 100%;
            height: 100%;
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="91806017-2f8a-eb5a-34a9-ee454ea3a75c"] {
            width: 100%;
            height: 100%;
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="91806017-2f8a-eb5a-34a9-ee454ea3a763"] {
            width: 100%;
            height: 100%;
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="91806017-2f8a-eb5a-34a9-ee454ea3a76e"] {
            width: 100%;
            height: 100%;
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="c77b6c64-5bfe-080d-9e72-d287a64c7e04"] {
            width: 100%;
            height: 100%;
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="bb3186cc-ab0e-d293-f9e7-16a6d7cce449"] {
            width: 100%;
            height: 100%;
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="b83ce29c-b6c8-9966-d1f2-2b88cc21986b"] {
            width: 100%;
            height: 100%;
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="b83ce29c-b6c8-9966-d1f2-2b88cc219871"] {
            width: 100%;
            height: 100%;
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="97bf51a5-4ea3-36e9-15c0-1f47a93f76c4"] {
            width: 100%;
            height: 100%;
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="b83ce29c-b6c8-9966-d1f2-2b88cc219882"] {
            width: 100%;
            height: 100%;
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="b83ce29c-b6c8-9966-d1f2-2b88cc21988b"] {
            width: 100%;
            height: 100%;
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="5859b40a-043e-bc39-0860-c8366a4b5b1f"] {
            width: 100%;
            height: 100%;
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="631c39c8-3c5e-df62-bce8-e4b7b6e6bd52"] {
            -webkit-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -moz-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -ms-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="8f279a91-90ae-c725-84e7-f60f566262f7"] {
            width: 100%;
            height: 100%;
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="2e7d28b4-765d-ccce-9e43-d4dbf2307c8b"] {
            width: 100%;
            height: 100%;
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="9d0e3cde-dfa7-0dda-c180-ec7ad0373727"] {
            display: none;
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="b083bb2f-a78a-8b36-7a69-25ba88d151cd"] {
            width: 100%;
            height: 50%;
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="18b2d837-1acd-4b1e-2bcd-455984cc3ecd"] {
            width: 100%;
            height: 50%;
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="3e239b0a-e3c6-cd31-0686-592f50448add"] {
            width: 60%;
            height: 60%;
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="3e239b0a-e3c6-cd31-0686-592f50448adc"] {
            display: none;
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="3e239b0a-e3c6-cd31-0686-592f50448ade"] {
            -webkit-transform: translate3d(0, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(540deg) skew(0, 0);
            -moz-transform: translate3d(0, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(540deg) skew(0, 0);
            -ms-transform: translate3d(0, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(540deg) skew(0, 0);
            transform: translate3d(0, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(540deg) skew(0, 0);
            width: 0%;
            height: 0%;
        }
    }

    @media (max-width: 767px) and (min-width: 480px) {
        html.w-mod-js:not(.w-mod-ix) [data-w-id="f9de1fa6-2fb0-5f42-3d95-b5ae80d7e08d"] {
            -webkit-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(290deg) skew(0, 0);
            -moz-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(290deg) skew(0, 0);
            -ms-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(290deg) skew(0, 0);
            transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(290deg) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="bdbad17b-1e36-cdba-8231-14734c34943c"] {
            -webkit-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(320deg) skew(0, 0);
            -moz-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(320deg) skew(0, 0);
            -ms-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(320deg) skew(0, 0);
            transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(320deg) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="21107a80-5e47-ee59-4874-03b95c8c85d6"] {
            -webkit-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(-90deg) skew(0, 0);
            -moz-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(-90deg) skew(0, 0);
            -ms-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(-90deg) skew(0, 0);
            transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(-90deg) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="3f95ff90-f033-7532-4be1-0d37a695f2d3"] {
            -webkit-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(180deg) skew(0, 0);
            -moz-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(180deg) skew(0, 0);
            -ms-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(180deg) skew(0, 0);
            transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(180deg) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="88f8031c-a8bc-8659-b9a6-6603aefd0cd8"] {
            -webkit-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(-270deg) skew(0, 0);
            -moz-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(-270deg) skew(0, 0);
            -ms-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(-270deg) skew(0, 0);
            transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(-270deg) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="ed6164aa-06fa-e5c8-6d52-40cca4c57c36"] {
            -webkit-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(180deg) skew(0, 0);
            -moz-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(180deg) skew(0, 0);
            -ms-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(180deg) skew(0, 0);
            transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(180deg) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="651632ce-cef5-77d9-2a58-e6d0b1aa1e28"] {
            -webkit-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(240deg) skew(0, 0);
            -moz-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(240deg) skew(0, 0);
            -ms-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(240deg) skew(0, 0);
            transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(240deg) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="7c7eac27-6551-73d6-2cce-ba4945ee6cae"] {
            opacity: 0;
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="3173c8c1-435d-9bfc-5ef4-8b2a00edfce4"] {
            -webkit-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(240deg) skew(0, 0);
            -moz-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(240deg) skew(0, 0);
            -ms-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(240deg) skew(0, 0);
            transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(240deg) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="3173c8c1-435d-9bfc-5ef4-8b2a00edfce7"] {
            -webkit-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(-360deg) skew(0, 0);
            -moz-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(-360deg) skew(0, 0);
            -ms-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(-360deg) skew(0, 0);
            transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(-360deg) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="3173c8c1-435d-9bfc-5ef4-8b2a00edfcea"] {
            -webkit-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(200deg) skew(0, 0);
            -moz-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(200deg) skew(0, 0);
            -ms-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(200deg) skew(0, 0);
            transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(200deg) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="3173c8c1-435d-9bfc-5ef4-8b2a00edfced"] {
            -webkit-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(180deg) skew(0, 0);
            -moz-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(180deg) skew(0, 0);
            -ms-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(180deg) skew(0, 0);
            transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(180deg) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="3173c8c1-435d-9bfc-5ef4-8b2a00edfcf0"] {
            -webkit-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(240deg) skew(0, 0);
            -moz-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(240deg) skew(0, 0);
            -ms-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(240deg) skew(0, 0);
            transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(240deg) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="3173c8c1-435d-9bfc-5ef4-8b2a00edfcf3"] {
            -webkit-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -moz-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -ms-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="3173c8c1-435d-9bfc-5ef4-8b2a00edfcf4"] {
            -webkit-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -moz-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -ms-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="3173c8c1-435d-9bfc-5ef4-8b2a00edfcf7"] {
            -webkit-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -moz-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -ms-transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            transform: translate3d(0, -80em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="0eb141e7-cdfe-aca1-6f88-90ae0d02171f"] {
            opacity: 0;
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="98c9f5c6-e612-4115-6e05-62f2f36050fd"] {
            -webkit-transform: translate3d(0, 0, 0) scale3d(1, 1, 1) rotateX(-50deg) rotateY(null) rotateZ(0) skew(-30deg, 0);
            -moz-transform: translate3d(0, 0, 0) scale3d(1, 1, 1) rotateX(-50deg) rotateY(null) rotateZ(0) skew(-30deg, 0);
            -ms-transform: translate3d(0, 0, 0) scale3d(1, 1, 1) rotateX(-50deg) rotateY(null) rotateZ(0) skew(-30deg, 0);
            transform: translate3d(0, 0, 0) scale3d(1, 1, 1) rotateX(-50deg) rotateY(null) rotateZ(0) skew(-30deg, 0);
            transform-style: preserve-3d;
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="0651746c-3bae-bf6a-7116-2282832fad80"] {
            width: 100%;
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="3173c8c1-435d-9bfc-5ef4-8b2a00edfd1a"] {
            -webkit-transform: translate3d(0px, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -moz-transform: translate3d(0px, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -ms-transform: translate3d(0px, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            transform: translate3d(0px, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="811ae4a8-b038-47d1-3afd-668e7890f05c"] {
            -webkit-transform: translate3d(0px, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -moz-transform: translate3d(0px, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -ms-transform: translate3d(0px, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            transform: translate3d(0px, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="563cd65e-88c1-1adb-6aa6-5f0ab6ddfcb9"] {
            -webkit-transform: translate3d(0px, -240%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(6deg) skew(0, 0);
            -moz-transform: translate3d(0px, -240%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(6deg) skew(0, 0);
            -ms-transform: translate3d(0px, -240%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(6deg) skew(0, 0);
            transform: translate3d(0px, -240%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(6deg) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="369e7303-69e0-1753-1193-a5c01d4d62d3"] {
            -webkit-transform: translate3d(0px, 240%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(-6deg) skew(0, 0);
            -moz-transform: translate3d(0px, 240%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(-6deg) skew(0, 0);
            -ms-transform: translate3d(0px, 240%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(-6deg) skew(0, 0);
            transform: translate3d(0px, 240%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(-6deg) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="0d4ca99a-ae50-8383-2bce-135328286865"] {
            -webkit-transform: translate3d(0px, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -moz-transform: translate3d(0px, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -ms-transform: translate3d(0px, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            transform: translate3d(0px, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="2f75b7c9-2d9f-76fa-8081-40c94c767144"] {
            -webkit-transform: translate3d(0px, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -moz-transform: translate3d(0px, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -ms-transform: translate3d(0px, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            transform: translate3d(0px, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="ec8cd256-8985-f22f-1304-1154057dfb10"] {
            -webkit-transform: translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -moz-transform: translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -ms-transform: translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            transform: translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="a4efb18a-b1c1-a66b-e40e-8445e5b25a25"] {
            -webkit-transform: translate3d(0px, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -moz-transform: translate3d(0px, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -ms-transform: translate3d(0px, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            transform: translate3d(0px, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="c867052f-c484-f03c-9bee-e41888692301"] {
            opacity: 1;
            display: none;
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="b2880d8b-a9a6-4339-bbf4-83eec2220769"] {
            opacity: 1;
            -webkit-transform: translate3d(0, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0deg) skew(0, 0);
            -moz-transform: translate3d(0, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0deg) skew(0, 0);
            -ms-transform: translate3d(0, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0deg) skew(0, 0);
            transform: translate3d(0, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0deg) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="785f348f-1e0c-89e5-4e43-6d937c68dd88"] {
            display: flex;
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="785f348f-1e0c-89e5-4e43-6d937c68dd8d"] {
            width: 100%;
            height: 100%;
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="1ec6c8a1-2dc4-46d9-1190-d49a130355dd"] {
            -webkit-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -moz-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -ms-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="3ef0f252-de49-f4de-e4e1-f1029246cb9d"] {
            -webkit-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -moz-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -ms-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="1ec6c8a1-2dc4-46d9-1190-d49a130355df"] {
            -webkit-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -moz-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -ms-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="3ef0f252-de49-f4de-e4e1-f1029246cb9f"] {
            -webkit-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -moz-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -ms-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="1ec6c8a1-2dc4-46d9-1190-d49a130355e1"] {
            -webkit-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -moz-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -ms-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="3ef0f252-de49-f4de-e4e1-f1029246cba1"] {
            -webkit-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -moz-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -ms-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="16eef9e7-4006-8f37-44b3-ae591327adc1"] {
            -webkit-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -moz-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -ms-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="1ec6c8a1-2dc4-46d9-1190-d49a130355e3"] {
            -webkit-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -moz-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -ms-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="3ef0f252-de49-f4de-e4e1-f1029246cba3"] {
            -webkit-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -moz-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -ms-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="ceb2db9c-2ce8-649d-1855-9d319e9e85da"] {
            -webkit-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -moz-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -ms-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="1ec6c8a1-2dc4-46d9-1190-d49a130355e5"] {
            -webkit-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -moz-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -ms-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="3ef0f252-de49-f4de-e4e1-f1029246cba5"] {
            -webkit-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -moz-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -ms-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="785f348f-1e0c-89e5-4e43-6d937c68dd9b"] {
            -webkit-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -moz-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -ms-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="1ec6c8a1-2dc4-46d9-1190-d49a130355e7"] {
            -webkit-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -moz-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -ms-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="88222aec-0acc-cf38-0cd2-b81a6a934976"] {
            -webkit-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -moz-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -ms-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="3feac868-a1a3-9f35-f96f-051ef4976984"] {
            -webkit-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -moz-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -ms-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="1ec6c8a1-2dc4-46d9-1190-d49a130355e9"] {
            -webkit-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -moz-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -ms-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="30208dc1-b4c4-675b-5742-7b25f05f702d"] {
            -webkit-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -moz-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -ms-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="1ec6c8a1-2dc4-46d9-1190-d49a130355eb"] {
            -webkit-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -moz-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -ms-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="1ec6c8a1-2dc4-46d9-1190-d49a130355ed"] {
            -webkit-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -moz-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -ms-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="2aef2651-27e2-7123-341d-ebc0c97f7c0d"] {
            -webkit-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -moz-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -ms-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="91806017-2f8a-eb5a-34a9-ee454ea3a757"] {
            width: 100%;
            height: 100%;
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="91806017-2f8a-eb5a-34a9-ee454ea3a75c"] {
            width: 100%;
            height: 100%;
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="91806017-2f8a-eb5a-34a9-ee454ea3a763"] {
            width: 100%;
            height: 100%;
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="91806017-2f8a-eb5a-34a9-ee454ea3a76e"] {
            width: 100%;
            height: 100%;
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="c77b6c64-5bfe-080d-9e72-d287a64c7e04"] {
            width: 100%;
            height: 100%;
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="bb3186cc-ab0e-d293-f9e7-16a6d7cce449"] {
            width: 100%;
            height: 100%;
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="b83ce29c-b6c8-9966-d1f2-2b88cc21986b"] {
            width: 100%;
            height: 100%;
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="b83ce29c-b6c8-9966-d1f2-2b88cc219871"] {
            width: 100%;
            height: 100%;
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="97bf51a5-4ea3-36e9-15c0-1f47a93f76c4"] {
            width: 100%;
            height: 100%;
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="b83ce29c-b6c8-9966-d1f2-2b88cc219882"] {
            width: 100%;
            height: 100%;
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="b83ce29c-b6c8-9966-d1f2-2b88cc21988b"] {
            width: 100%;
            height: 100%;
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="5859b40a-043e-bc39-0860-c8366a4b5b1f"] {
            width: 100%;
            height: 100%;
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="631c39c8-3c5e-df62-bce8-e4b7b6e6bd52"] {
            -webkit-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -moz-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -ms-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="8f279a91-90ae-c725-84e7-f60f566262f7"] {
            width: 100%;
            height: 100%;
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="2e7d28b4-765d-ccce-9e43-d4dbf2307c8b"] {
            width: 100%;
            height: 100%;
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="9d0e3cde-dfa7-0dda-c180-ec7ad0373727"] {
            display: none;
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="b083bb2f-a78a-8b36-7a69-25ba88d151cd"] {
            width: 100%;
            height: 50%;
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="18b2d837-1acd-4b1e-2bcd-455984cc3ecd"] {
            width: 100%;
            height: 50%;
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="3e239b0a-e3c6-cd31-0686-592f50448add"] {
            width: 60%;
            height: 60%;
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="3e239b0a-e3c6-cd31-0686-592f50448adc"] {
            display: none;
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="3e239b0a-e3c6-cd31-0686-592f50448ade"] {
            -webkit-transform: translate3d(0, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(540deg) skew(0, 0);
            -moz-transform: translate3d(0, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(540deg) skew(0, 0);
            -ms-transform: translate3d(0, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(540deg) skew(0, 0);
            transform: translate3d(0, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(540deg) skew(0, 0);
            width: 0%;
            height: 0%;
        }
    }

    @media (max-width: 479px) {
        html.w-mod-js:not(.w-mod-ix) [data-w-id="3173c8c1-435d-9bfc-5ef4-8b2a00edfd1a"] {
            -webkit-transform: translate3d(0px, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -moz-transform: translate3d(0px, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -ms-transform: translate3d(0px, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            transform: translate3d(0px, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="811ae4a8-b038-47d1-3afd-668e7890f05c"] {
            -webkit-transform: translate3d(0px, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -moz-transform: translate3d(0px, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -ms-transform: translate3d(0px, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            transform: translate3d(0px, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="563cd65e-88c1-1adb-6aa6-5f0ab6ddfcb9"] {
            -webkit-transform: translate3d(0px, -240%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(6deg) skew(0, 0);
            -moz-transform: translate3d(0px, -240%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(6deg) skew(0, 0);
            -ms-transform: translate3d(0px, -240%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(6deg) skew(0, 0);
            transform: translate3d(0px, -240%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(6deg) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="369e7303-69e0-1753-1193-a5c01d4d62d3"] {
            -webkit-transform: translate3d(0px, 240%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(-6deg) skew(0, 0);
            -moz-transform: translate3d(0px, 240%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(-6deg) skew(0, 0);
            -ms-transform: translate3d(0px, 240%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(-6deg) skew(0, 0);
            transform: translate3d(0px, 240%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(-6deg) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="0d4ca99a-ae50-8383-2bce-135328286865"] {
            -webkit-transform: translate3d(0px, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -moz-transform: translate3d(0px, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -ms-transform: translate3d(0px, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            transform: translate3d(0px, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="2f75b7c9-2d9f-76fa-8081-40c94c767144"] {
            -webkit-transform: translate3d(0px, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -moz-transform: translate3d(0px, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -ms-transform: translate3d(0px, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            transform: translate3d(0px, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="ec8cd256-8985-f22f-1304-1154057dfb10"] {
            -webkit-transform: translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -moz-transform: translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -ms-transform: translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            transform: translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="a4efb18a-b1c1-a66b-e40e-8445e5b25a25"] {
            -webkit-transform: translate3d(0px, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -moz-transform: translate3d(0px, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -ms-transform: translate3d(0px, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            transform: translate3d(0px, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="c867052f-c484-f03c-9bee-e41888692301"] {
            opacity: 1;
            display: none;
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="b2880d8b-a9a6-4339-bbf4-83eec2220769"] {
            opacity: 1;
            -webkit-transform: translate3d(0, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0deg) skew(0, 0);
            -moz-transform: translate3d(0, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0deg) skew(0, 0);
            -ms-transform: translate3d(0, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0deg) skew(0, 0);
            transform: translate3d(0, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0deg) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="785f348f-1e0c-89e5-4e43-6d937c68dd88"] {
            display: flex;
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="785f348f-1e0c-89e5-4e43-6d937c68dd8d"] {
            width: 100%;
            height: 100%;
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="1ec6c8a1-2dc4-46d9-1190-d49a130355dd"] {
            -webkit-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -moz-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -ms-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="3ef0f252-de49-f4de-e4e1-f1029246cb9d"] {
            -webkit-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -moz-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -ms-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="1ec6c8a1-2dc4-46d9-1190-d49a130355df"] {
            -webkit-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -moz-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -ms-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="3ef0f252-de49-f4de-e4e1-f1029246cb9f"] {
            -webkit-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -moz-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -ms-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="1ec6c8a1-2dc4-46d9-1190-d49a130355e1"] {
            -webkit-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -moz-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -ms-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="3ef0f252-de49-f4de-e4e1-f1029246cba1"] {
            -webkit-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -moz-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -ms-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="16eef9e7-4006-8f37-44b3-ae591327adc1"] {
            -webkit-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -moz-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -ms-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="1ec6c8a1-2dc4-46d9-1190-d49a130355e3"] {
            -webkit-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -moz-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -ms-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="3ef0f252-de49-f4de-e4e1-f1029246cba3"] {
            -webkit-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -moz-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -ms-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="ceb2db9c-2ce8-649d-1855-9d319e9e85da"] {
            -webkit-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -moz-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -ms-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="1ec6c8a1-2dc4-46d9-1190-d49a130355e5"] {
            -webkit-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -moz-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -ms-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="3ef0f252-de49-f4de-e4e1-f1029246cba5"] {
            -webkit-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -moz-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -ms-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="785f348f-1e0c-89e5-4e43-6d937c68dd9b"] {
            -webkit-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -moz-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -ms-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="1ec6c8a1-2dc4-46d9-1190-d49a130355e7"] {
            -webkit-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -moz-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -ms-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="88222aec-0acc-cf38-0cd2-b81a6a934976"] {
            -webkit-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -moz-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -ms-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="3feac868-a1a3-9f35-f96f-051ef4976984"] {
            -webkit-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -moz-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -ms-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="1ec6c8a1-2dc4-46d9-1190-d49a130355e9"] {
            -webkit-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -moz-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -ms-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="30208dc1-b4c4-675b-5742-7b25f05f702d"] {
            -webkit-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -moz-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -ms-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="1ec6c8a1-2dc4-46d9-1190-d49a130355eb"] {
            -webkit-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -moz-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -ms-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="1ec6c8a1-2dc4-46d9-1190-d49a130355ed"] {
            -webkit-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -moz-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -ms-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="2aef2651-27e2-7123-341d-ebc0c97f7c0d"] {
            -webkit-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -moz-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -ms-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="91806017-2f8a-eb5a-34a9-ee454ea3a757"] {
            width: 100%;
            height: 100%;
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="91806017-2f8a-eb5a-34a9-ee454ea3a75c"] {
            width: 100%;
            height: 100%;
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="91806017-2f8a-eb5a-34a9-ee454ea3a763"] {
            width: 100%;
            height: 100%;
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="91806017-2f8a-eb5a-34a9-ee454ea3a76e"] {
            width: 100%;
            height: 100%;
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="c77b6c64-5bfe-080d-9e72-d287a64c7e04"] {
            width: 100%;
            height: 100%;
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="bb3186cc-ab0e-d293-f9e7-16a6d7cce449"] {
            width: 100%;
            height: 100%;
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="b83ce29c-b6c8-9966-d1f2-2b88cc21986b"] {
            width: 100%;
            height: 100%;
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="b83ce29c-b6c8-9966-d1f2-2b88cc219871"] {
            width: 100%;
            height: 100%;
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="97bf51a5-4ea3-36e9-15c0-1f47a93f76c4"] {
            width: 100%;
            height: 100%;
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="b83ce29c-b6c8-9966-d1f2-2b88cc219882"] {
            width: 100%;
            height: 100%;
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="b83ce29c-b6c8-9966-d1f2-2b88cc21988b"] {
            width: 100%;
            height: 100%;
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="5859b40a-043e-bc39-0860-c8366a4b5b1f"] {
            width: 100%;
            height: 100%;
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="631c39c8-3c5e-df62-bce8-e4b7b6e6bd52"] {
            -webkit-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -moz-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -ms-transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            transform: translate3d(0, 140%, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="8f279a91-90ae-c725-84e7-f60f566262f7"] {
            width: 100%;
            height: 100%;
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="2e7d28b4-765d-ccce-9e43-d4dbf2307c8b"] {
            width: 100%;
            height: 100%;
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="9d0e3cde-dfa7-0dda-c180-ec7ad0373727"] {
            display: none;
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="b083bb2f-a78a-8b36-7a69-25ba88d151cd"] {
            width: 100%;
            height: 50%;
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="18b2d837-1acd-4b1e-2bcd-455984cc3ecd"] {
            width: 100%;
            height: 50%;
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="3e239b0a-e3c6-cd31-0686-592f50448add"] {
            width: 60%;
            height: 60%;
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="3e239b0a-e3c6-cd31-0686-592f50448adc"] {
            display: none;
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="3e239b0a-e3c6-cd31-0686-592f50448ade"] {
            -webkit-transform: translate3d(0, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(540deg) skew(0, 0);
            -moz-transform: translate3d(0, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(540deg) skew(0, 0);
            -ms-transform: translate3d(0, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(540deg) skew(0, 0);
            transform: translate3d(0, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(540deg) skew(0, 0);
            width: 0%;
            height: 0%;
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="f9de1fa6-2fb0-5f42-3d95-b5ae80d7e08d"] {
            -webkit-transform: translate3d(0, -160em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(290deg) skew(0, 0);
            -moz-transform: translate3d(0, -160em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(290deg) skew(0, 0);
            -ms-transform: translate3d(0, -160em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(290deg) skew(0, 0);
            transform: translate3d(0, -160em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(290deg) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="bdbad17b-1e36-cdba-8231-14734c34943c"] {
            -webkit-transform: translate3d(0, -160em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(320deg) skew(0, 0);
            -moz-transform: translate3d(0, -160em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(320deg) skew(0, 0);
            -ms-transform: translate3d(0, -160em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(320deg) skew(0, 0);
            transform: translate3d(0, -160em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(320deg) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="21107a80-5e47-ee59-4874-03b95c8c85d6"] {
            -webkit-transform: translate3d(0, -160em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(-90deg) skew(0, 0);
            -moz-transform: translate3d(0, -160em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(-90deg) skew(0, 0);
            -ms-transform: translate3d(0, -160em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(-90deg) skew(0, 0);
            transform: translate3d(0, -160em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(-90deg) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="3f95ff90-f033-7532-4be1-0d37a695f2d3"] {
            -webkit-transform: translate3d(0, -160em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(180deg) skew(0, 0);
            -moz-transform: translate3d(0, -160em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(180deg) skew(0, 0);
            -ms-transform: translate3d(0, -160em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(180deg) skew(0, 0);
            transform: translate3d(0, -160em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(180deg) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="88f8031c-a8bc-8659-b9a6-6603aefd0cd8"] {
            -webkit-transform: translate3d(0, -160em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(-270deg) skew(0, 0);
            -moz-transform: translate3d(0, -160em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(-270deg) skew(0, 0);
            -ms-transform: translate3d(0, -160em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(-270deg) skew(0, 0);
            transform: translate3d(0, -160em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(-270deg) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="ed6164aa-06fa-e5c8-6d52-40cca4c57c36"] {
            -webkit-transform: translate3d(0, -160em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(180deg) skew(0, 0);
            -moz-transform: translate3d(0, -160em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(180deg) skew(0, 0);
            -ms-transform: translate3d(0, -160em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(180deg) skew(0, 0);
            transform: translate3d(0, -160em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(180deg) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="651632ce-cef5-77d9-2a58-e6d0b1aa1e28"] {
            -webkit-transform: translate3d(0, -160em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(240deg) skew(0, 0);
            -moz-transform: translate3d(0, -160em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(240deg) skew(0, 0);
            -ms-transform: translate3d(0, -160em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(240deg) skew(0, 0);
            transform: translate3d(0, -160em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(240deg) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="7c7eac27-6551-73d6-2cce-ba4945ee6cae"] {
            opacity: 0;
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="c0e55528-f719-097f-e39e-9de511c3a8bd"] {
            opacity: 0;
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="c0e55528-f719-097f-e39e-9de511c3a8bc"] {
            -webkit-transform: translate3d(0, 0, 0) scale3d(1, 1, 1) rotateX(-50deg) rotateY(null) rotateZ(0) skew(-30deg, 0);
            -moz-transform: translate3d(0, 0, 0) scale3d(1, 1, 1) rotateX(-50deg) rotateY(null) rotateZ(0) skew(-30deg, 0);
            -ms-transform: translate3d(0, 0, 0) scale3d(1, 1, 1) rotateX(-50deg) rotateY(null) rotateZ(0) skew(-30deg, 0);
            transform: translate3d(0, 0, 0) scale3d(1, 1, 1) rotateX(-50deg) rotateY(null) rotateZ(0) skew(-30deg, 0);
            transform-style: preserve-3d;
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="3173c8c1-435d-9bfc-5ef4-8b2a00edfce4"] {
            -webkit-transform: translate3d(0, -200em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(240deg) skew(0, 0);
            -moz-transform: translate3d(0, -200em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(240deg) skew(0, 0);
            -ms-transform: translate3d(0, -200em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(240deg) skew(0, 0);
            transform: translate3d(0, -200em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(240deg) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="3173c8c1-435d-9bfc-5ef4-8b2a00edfce7"] {
            -webkit-transform: translate3d(0, -200em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(-360deg) skew(0, 0);
            -moz-transform: translate3d(0, -200em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(-360deg) skew(0, 0);
            -ms-transform: translate3d(0, -200em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(-360deg) skew(0, 0);
            transform: translate3d(0, -200em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(-360deg) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="3173c8c1-435d-9bfc-5ef4-8b2a00edfcea"] {
            -webkit-transform: translate3d(0, -200em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(200deg) skew(0, 0);
            -moz-transform: translate3d(0, -200em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(200deg) skew(0, 0);
            -ms-transform: translate3d(0, -200em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(200deg) skew(0, 0);
            transform: translate3d(0, -200em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(200deg) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="3173c8c1-435d-9bfc-5ef4-8b2a00edfced"] {
            -webkit-transform: translate3d(0, -200em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(180deg) skew(0, 0);
            -moz-transform: translate3d(0, -200em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(180deg) skew(0, 0);
            -ms-transform: translate3d(0, -200em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(180deg) skew(0, 0);
            transform: translate3d(0, -200em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(180deg) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="3173c8c1-435d-9bfc-5ef4-8b2a00edfcf0"] {
            -webkit-transform: translate3d(0, -200em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(240deg) skew(0, 0);
            -moz-transform: translate3d(0, -200em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(240deg) skew(0, 0);
            -ms-transform: translate3d(0, -200em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(240deg) skew(0, 0);
            transform: translate3d(0, -200em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(240deg) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="3173c8c1-435d-9bfc-5ef4-8b2a00edfcf3"] {
            -webkit-transform: translate3d(0, -200em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -moz-transform: translate3d(0, -200em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -ms-transform: translate3d(0, -200em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            transform: translate3d(0, -200em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="3173c8c1-435d-9bfc-5ef4-8b2a00edfcf4"] {
            -webkit-transform: translate3d(0, -200em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -moz-transform: translate3d(0, -200em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -ms-transform: translate3d(0, -200em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            transform: translate3d(0, -200em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
        }

        html.w-mod-js:not(.w-mod-ix) [data-w-id="3173c8c1-435d-9bfc-5ef4-8b2a00edfcf7"] {
            -webkit-transform: translate3d(0, -200em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -moz-transform: translate3d(0, -200em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            -ms-transform: translate3d(0, -200em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
            transform: translate3d(0, -200em, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);
        }
    }

    /* 修复Java文字J字母被裁切的问题 */
    .h1-italic.is-hero {
        line-height: 1.2 !important;
        padding-bottom: 0.1em !important;
    }

    .h-italic-wrapper.is-hero {
        overflow: visible !important;
        padding-bottom: 0.1em !important;
    }

    .h-hide-italic {
        overflow: visible !important;
        padding-bottom: 0.1em !important;
    }

    /* 算法演示按钮样式 */
    .btn-outer-frame.is-algorithm-demo:hover {
        transform: translateY(-3px) !important;
        box-shadow: 0 15px 40px rgba(102, 126, 234, 0.4) !important;
    }

    .btn-outer-frame.is-algorithm-demo:active {
        transform: translateY(-1px) !important;
        box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3) !important;
    }

    .btn-outer-frame.is-algorithm-demo .btn-circle {
        transition: all 0.3s ease !important;
    }

    .btn-outer-frame.is-algorithm-demo:hover .btn-circle {
        background: rgba(255,255,255,0.3) !important;
        transform: scale(1.1) !important;
    }

    @media (max-width: 768px) {
        .btn-outer-frame.is-algorithm-demo {
            margin: 20px auto !important;
            display: block !important;
            width: fit-content !important;
        }
    }

    /* 主要算法演示按钮样式 */
    .btn-outer-frame.is-algorithm-main:hover {
        transform: translateY(-5px) !important;
        box-shadow: 0 20px 50px rgba(0,0,0,0.3) !important;
    }

    .btn-outer-frame.is-algorithm-main:active {
        transform: translateY(-2px) !important;
    }

    /* 算法专区响应式 */
    @media (max-width: 768px) {
        .section-part-algorithm h2 {
            font-size: 2rem !important;
        }

        .section-part-algorithm p {
            font-size: 1rem !important;
        }

        .section-part-algorithm > div > div:nth-child(2) {
            grid-template-columns: 1fr !important;
            gap: 20px !important;
        }

        .section-part-algorithm {
            padding: 50px 0 !important;
        }
    }

    /* 算法图标按钮样式 */
    .algorithm-icon-btn:hover {
        transform: translateY(-3px) scale(1.1) !important;
        box-shadow: 0 12px 35px rgba(102, 126, 234, 0.6) !important;
    }

    .algorithm-icon-btn:hover > div {
        opacity: 1 !important;
    }

    .algorithm-icon-btn:active {
        transform: translateY(-1px) scale(1.05) !important;
    }

    .algorithm-icon-btn-mobile:hover {
        transform: translateY(-2px) scale(1.1) !important;
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6) !important;
    }

    .algorithm-icon-btn-mobile:active {
        transform: translateY(0px) scale(1.05) !important;
    }

    /* 图标按钮脉冲动画 */
    @keyframes pulse {
        0% { box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4); }
        50% { box-shadow: 0 8px 25px rgba(102, 126, 234, 0.7); }
        100% { box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4); }
    }

    .algorithm-icon-btn, .algorithm-icon-btn-mobile {
        animation: pulse 2s infinite !important;
    }
    </style>
    <!-- Local Font CSS -->
    <style>
        /* Oswald Font */
        @font-face {
            font-family: 'Oswald';
            src: url('s/oswald/v56/TK3iWkUHHAIjg752GT8G.woff2') format('woff2');
            font-weight: 200 700;
            font-style: normal;
            font-display: swap;
        }

        /* Inter Font */
        @font-face {
            font-family: 'Inter';
            src: url('s/inter/v19/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1ZL7.woff2') format('woff2');
            font-weight: 300 900;
            font-style: normal;
            font-display: swap;
        }

        /* Instrument Serif Font */
        @font-face {
            font-family: 'Instrument Serif';
            src: url('s/instrumentserif/v4/jizBRFtNs2ka5fXjeivQ4LroWlx-6zUTjg.woff2') format('woff2');
            font-weight: 400;
            font-style: normal;
            font-display: swap;
        }

        @font-face {
            font-family: 'Instrument Serif';
            src: url('s/instrumentserif/v4/jizHRFtNs2ka5fXjeivQ4LroWlx-6zAjjH7M.woff2') format('woff2');
            font-weight: 400;
            font-style: italic;
            font-display: swap;
        }
    </style>
    <script type="text/javascript">!function (o, c) {
        var n = c.documentElement, t = " w-mod-";
        n.className += t + "js", ("ontouchstart" in o || o.DocumentTouch && c instanceof DocumentTouch) && (n.className += t + "touch")
    }(window, document);</script>
    <link href="6787c53671a97b8708dd7000/6787c53671a97b8708dd7072_dj%20-%20favicon.png"
          rel="shortcut icon" type="image/x-icon"/>
    <link href="6787c53671a97b8708dd7000/6787c53671a97b8708dd7071_dj%20-%20webclip.png"
          rel="apple-touch-icon"/>
    <style>
        .cursor-parent {
            user-select: none;
            pointer-events: none;
            /*mix-blend-mode: difference;*/
        }

        .drop-pop-wrapper {
            user-select: none;
            pointer-events: none;
        }

        .gallery-row {
            user-select: none;
            pointer-events: none;
        }

        .gal-inner-transition {
            user-select: none;
            pointer-events: none;
        }

        .inner-set-gal {
            user-select: none;
            pointer-events: none;
        }

        .banner-img-about-top {
            user-select: none;
            pointer-events: none;
        }

        .empty-section {
            user-select: none;
            pointer-events: none;
        }
    </style>
</head>
<body class="body">
<div class="mobile-menu-container">
    <div class="nav-block is-1">
        <div class="nav-item-mobile">
            <div class="nav-text">关于我</div>
        </div>
    </div>
    <div class="nav-block is-2">
        <div class="nav-item-mobile">
            <div class="nav-text">技术文章</div>
        </div>
    </div>
    <div class="nav-block is-3">
        <div class="nav-item-mobile">
            <div class="nav-text">项目展示</div>
        </div>
    </div>
    <div class="nav-block is-4">
        <div class="nav-item-mobile">
            <div class="nav-text">学习笔记</div>
        </div>
    </div>
    <div class="nav-block is-5">
        <div class="nav-item-mobile">
            <div class="nav-text">代码库</div>
        </div>
    </div>
    <div class="whipe-cream-btm"></div>
</div>
<nav data-w-id="a61a6bc6-c003-09d7-99a6-25d14b0d1d4d" class="navigation">
    <div class="container-lg is-nav"><a href="/" aria-current="page" class="logo-container w-inline-block w--current">
        <div class="logo-text">Java Developer</div>
    </a>
        <div class="nav-menu">
            <div class="nav-item">
                <div class="nav-text">关于我</div>
            </div>
            <div class="nav-line"></div>
            <div class="nav-item">
                <div class="nav-text">技术文章</div>
            </div>
            <div class="nav-line"></div>
            <div class="nav-item">
                <a href="algorithms.html" class="nav-text" style="text-decoration: none; color: inherit;">项目展示</a>
            </div>
            <div class="nav-line"></div>
            <div class="nav-item">
                <div class="nav-text">学习笔记</div>
            </div>
            <div class="nav-line"></div>
            <div class="nav-item">
                <div class="nav-text">代码库</div>
            </div>
        </div>
        <div data-w-id="7b485b96-d889-6249-5663-884f96d353de" class="burger-menu">
            <div class="burger-line-div">
                <div class="burger-line-1"></div>
                <div class="burger-line-2"></div>
                <div class="border-line-3"></div>
            </div>
            <div class="cross-div">
                <div class="cross-line is-1"></div>
                <div class="cross-line is-2"></div>
            </div>
        </div>
    </div>
    <div class="div-hide">
        <div class="mobile-menu"><a href="/about" class="mobile-nav-link w-inline-block">
            <div class="text-block-5">About</div>
        </a>
            <div class="divider"></div>
            <a href="/articles" class="mobile-nav-link w-inline-block">
                <div class="text-block-5">Articles</div>
            </a>
            <div class="divider"></div>
            <a href="/archive/events" class="mobile-nav-link hidden w-inline-block">
                <div class="text-block-5">Events</div>
            </a><a href="algorithms.html" class="mobile-nav-link w-inline-block">
                <div class="text-block-5">项目展示</div>
            </a>
            <div class="divider"></div>
            <a href="/books" class="mobile-nav-link w-inline-block">
                <div class="text-block-5">Books</div>
            </a>
            <div class="divider"></div>
            <a href="/archive/conact" class="mobile-nav-link hidden w-inline-block">
                <div class="text-block-5">Reach Out</div>
            </a></div>
    </div>
</nav>
<div class="drop-pop-wrapper">
    <div class="drop-pop is-1">
        <div class="drop-pop-img is-1"></div>
    </div>
    <div class="drop-pop is-2">
        <div class="drop-pop-img is-2"></div>
    </div>
    <div class="drop-pop is-3">
        <div class="drop-pop-img is-3"></div>
    </div>
    <div class="drop-pop is-4">
        <div class="drop-pop-img is-4"></div>
    </div>
    <div class="drop-pop is-5">
        <div class="drop-pop-img is-5"></div>
    </div>
</div>
<div data-w-id="c867052f-c484-f03c-9bee-e41888692301" class="main-container">
    <div class="page-bar">
        <div class="h-scroll-bar">
            <div class="scroll-point is-1"></div>
            <div class="sroll-line"></div>
            <div style="background-color:rgba(246,244,241,0)" class="scroll-point is-2"></div>
            <div class="sroll-line"></div>
            <div style="background-color:rgba(246,244,241,0)" class="scroll-point is-3"></div>
            <div class="sroll-line"></div>
            <div style="background-color:rgba(246,244,241,0)" class="scroll-point is-4"></div>
            <div class="sroll-line"></div>
            <div style="background-color:rgba(246,244,241,0)" class="scroll-point is-5"></div>
            <div class="sroll-line"></div>
            <div style="background-color:rgba(246,244,241,0)" class="scroll-point is-6"></div>
        </div>
        <div class="page-tip-wrapper">
            <div data-w-id="d148cf4d-dcde-504e-2ad7-a831118cb25c" class="page-no-wrap is-tip">
                <div class="nav-text is-tip">提示：向上滚动以查看</div>
            </div>
        </div>
        <div class="flex-left is-auto">
            <div class="page-no-wrap">
                <div class="nav-text pg-top">Page - Index</div>
            </div>
        </div>
    </div>
    <div class="home-hero-section is-home">
        <section class="hero-100 is-home">
            <div class="hero-content">
                <div class="hero-h-wrapper is-80">
                    <div class="h-row">
                        <div class="h-hide is-hero"><h1 class="h1-hero">Technology Sharing</h1></div>
                        <div class="h-hide-italic">
                            <div class="h-italic-wrapper is-hero"><h1 class="h1-italic is-hero">Blog</h1></div>
                            <div data-w-id="91806017-2f8a-eb5a-34a9-ee454ea3a757" class="whipe-hero"></div>
                        </div>
                    </div>
                    <div class="h-row">
                        <div class="h-hide-italic">
                            <div class="h-italic-wrapper is-hero"><h1 class="h1-italic is-hero">数据结构</h1></div>
                            <div data-w-id="91806017-2f8a-eb5a-34a9-ee454ea3a75c" class="whipe-hero"></div>
                        </div>
                        <div class="h-hide is-hero"><h1 class="h1-hero">and</h1></div>
                        <div class="h-hide-italic">
                            <div class="h-italic-wrapper is-hero"><h1 class="h1-italic is-hero">算法</h1></div>
                            <div data-w-id="91806017-2f8a-eb5a-34a9-ee454ea3a75c" class="whipe-hero"></div>
                        </div>
                    </div>
                    <div class="h-row">
                        <div class="h-hide-italic">
                            <div class="h-italic-wrapper is-hero"><h1 class="h1-italic is-hero">AI Enthusiast</h1></div>
                            <div data-w-id="91806017-2f8a-eb5a-34a9-ee454ea3a763" class="whipe-hero"></div>
                        </div>
                        <div class="h-hide is-hero"><h1 class="h1-hero">On</h1></div>
                    </div>
                    <div class="h-row">
                        <div class="h-hide-italic">
                            <div class="h-italic-wrapper is-hero"><h1 class="h1-italic is-hero">Data Structure</h1></div>
                            <div data-w-id="91806017-2f8a-eb5a-34a9-ee454ea3a76e" class="whipe-hero"></div>
                        </div>
                        <div class="h-hide is-hero"><h1 class="h1-hero">And</h1></div>
                        <div class="h-hide-italic">
                            <div class="h-italic-wrapper is-hero"><h1 class="h1-italic is-hero">Algorithm</h1></div>
                            <div data-w-id="c77b6c64-5bfe-080d-9e72-d287a64c7e04" class="whipe-hero"></div>
                        </div>
                    </div>
                    <div class="h-row is---action">
                        <div class="h-hide-italic">
                            <div class="h-italic-wrapper is-hero"><h1 class="h1-italic is-hero">Java.</h1></div>
                            <div data-w-id="bb3186cc-ab0e-d293-f9e7-16a6d7cce449" class="whipe-hero"></div>
                        </div>
                        <!-- 算法演示按钮 -->
                        <div class="h-hide" style="margin-left: 40px;">
                            <a href="algorithms.html" class="algorithm-icon-btn" style="display: inline-flex; align-items: center; gap: 12px; padding: 15px 25px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 50px; text-decoration: none; box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4); transition: all 0.3s ease; position: relative; overflow: hidden; white-space: nowrap;">
                                <span style="font-size: 28px; color: white; z-index: 2;">🚀</span>
                                <span style="color: white; font-weight: 600; font-size: 18px; z-index: 2;">算法演示</span>
                                <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: linear-gradient(135deg, #764ba2 0%, #667eea 100%); opacity: 0; transition: opacity 0.3s ease;"></div>
                            </a>
                        </div>

                    </div>
                </div>
                <div class="hero-h-wrapper is-80 is-mobile">
                    <div class="h-row">
                        <div class="h-hide is-hero"><h1 class="h1-hero">Technology Sharing</h1></div>
                    </div>
                    <div class="h-row">
                        <div class="h-hide-italic">
                            <div class="h-italic-wrapper is-hero"><h1 class="h1-italic is-hero">Blog</h1></div>
                            <div data-w-id="b83ce29c-b6c8-9966-d1f2-2b88cc21986b" class="whipe-hero"></div>
                        </div>
                    </div>
                    <div class="h-row">
                        <div class="h-hide-italic">
                            <div class="h-italic-wrapper is-hero"><h1 class="h1-italic is-hero">Technology Sharing</h1></div>
                            <div data-w-id="b83ce29c-b6c8-9966-d1f2-2b88cc219871" class="whipe-hero"></div>
                        </div>
                        <div class="h-hide is-hero"><h1 class="h1-hero">and</h1></div>
                    </div>
                    <div class="h-row">
                        <div class="h-hide-italic">
                            <div class="h-italic-wrapper is-hero"><h1 class="h1-italic is-hero">AI Enthusiast</h1></div>
                            <div data-w-id="97bf51a5-4ea3-36e9-15c0-1f47a93f76c4" class="whipe-hero"></div>
                        </div>
                    </div>
                    <div class="h-row">
                        <div class="h-hide is-hero"><h1 class="h1-hero">On</h1></div>
                        <div class="h-hide-italic">
                            <div class="h-italic-wrapper is-hero"><h1 class="h1-italic is-hero">Data Structure</h1></div>
                            <div data-w-id="b83ce29c-b6c8-9966-d1f2-2b88cc219882" class="whipe-hero"></div>
                        </div>
                    </div>
                    <div class="h-row">
                        <div class="h-hide is-hero"><h1 class="h1-hero">And</h1></div>
                        <div class="h-hide-italic">
                            <div class="h-italic-wrapper is-hero"><h1 class="h1-italic is-hero">Algorithm</h1></div>
                            <div data-w-id="b83ce29c-b6c8-9966-d1f2-2b88cc21988b" class="whipe-hero"></div>
                        </div>
                    </div>
                    <div class="h-row">
                        <div class="h-hide-italic">
                            <div class="h-italic-wrapper is-hero"><h1 class="h1-italic is-hero">Java.</h1></div>
                            <div data-w-id="5859b40a-043e-bc39-0860-c8366a4b5b1f" class="whipe-hero"></div>
                        </div>
                        <!-- 移动端算法演示按钮 -->
                        <div class="h-hide" style="margin-left: 25px;">
                            <a href="algorithms.html" class="algorithm-icon-btn-mobile" style="display: inline-flex; align-items: center; gap: 8px; padding: 12px 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 40px; text-decoration: none; box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4); transition: all 0.3s ease; white-space: nowrap;">
                                <span style="font-size: 22px; color: white;">🚀</span>
                                <span style="color: white; font-weight: 600; font-size: 14px;">算法演示</span>
                            </a>
                        </div>
                    </div>

                </div>
            </div>
            <div class="hero-img-wrapper">
                <div class="hero-img is-top"></div>
                <div class="hero-img is-btm"></div>
            </div>
        </section>
    </div>
    <div class="section-part-2 is-sp">
        <div data-w-id="de98f391-d89d-dbe4-3c1b-472e90ff9363" class="section is-2 is-home">
            <div class="container-lg ov-hidden">
                <div data-w-id="98cf82af-225d-843f-cad3-6fc18cc962c7" class="flex-split is-highlight">
                    <div class="d-60">
                        <div class="h-light-text-wrap is-1"><p data-w-id="your-id-here" class="text" style="color: rgb(37, 35, 33);">
                            我是一位热爱算法与数据结构的技术爱好者，专注于探索排序、动态规划、递归分析算法等核心问题的底层逻辑与性能优化。在这个网站中，我不仅分享自己的理解与实践，还通过动画形式呈现算法的运行过程，使抽象的计算过程更直观、更可视、更易于理解。希望这里能成为一个交流思路、验证逻辑、激发灵感的技术空间。
                        </p>
                        </div>
                        <!-- 算法演示按钮 -->
                        <div style="margin-top: 30px;">
                            <a href="algorithms.html" class="btn-outer-frame is-algorithm-demo w-inline-block" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 50px; padding: 0; border: none; box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3); transition: all 0.3s ease;">
                                <div class="btn-inner-frame" style="background: transparent; border-radius: 50px;">
                                    <div class="btn-text-wrap" style="padding: 15px 30px;">
                                        <div class="btn-text" style="color: white; font-weight: 600; font-size: 16px; display: flex; align-items: center; gap: 10px;">
                                            <span style="font-size: 20px;">🚀</span>
                                            体验算法演示
                                        </div>
                                    </div>
                                    <div class="btn-circle" style="background: rgba(255,255,255,0.2);"></div>
                                    <div class="btn-bg" style="background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);"></div>
                                </div>
                            </a>
                        </div>
                    </div>
                </div>
                <div class="marquee-cont is-left">
                    <div class="marquee-cream">
                        <div data-w-id="ec8cd256-8985-f22f-1304-1154057dfb10" class="marquee-track is-home-cream">
                            <div class="marquee-div is-cream">
                                <div class="marquee-text-wrapper">
                                    <div class="marquee-text is-italic">算法与数据结构</div>
                                </div>
                                <div class="marquee-text is-italic">-</div>
                                <div class="marquee-text-wrapper">
                                    <div class="marquee-text is-italic">Java编程实践</div>
                                </div>
                                <div class="marquee-text is-italic">-</div>
                                <div class="marquee-text-wrapper">
                                    <div class="marquee-text is-italic">算法与数据结构</div>
                                </div>
                                <div class="marquee-text is-italic">-</div>
                                <div class="marquee-text-wrapper">
                                    <div class="marquee-text is-italic">Java编程实践</div>
                                </div>
                                <div class="marquee-text is-italic">-</div>
                            </div>
                            <div class="marquee-div is-cream">
                                <div class="marquee-text-wrapper">
                                    <div class="marquee-text is-italic">算法与数据结构</div>
                                </div>
                                <div class="marquee-text is-italic">-</div>
                                <div class="marquee-text-wrapper">
                                    <div class="marquee-text is-italic">Java编程实践</div>
                                </div>
                                <div class="marquee-text is-italic">-</div>
                                <div class="marquee-text-wrapper">
                                    <div class="marquee-text is-italic">算法与数据结构</div>
                                </div>
                                <div class="marquee-text is-italic">-</div>
                                <div class="marquee-text-wrapper">
                                    <div class="marquee-text is-italic">Java编程实践</div>
                                </div>
                                <div class="marquee-text is-italic">-</div>
                            </div>
                        </div>
                    </div>
                    <div class="marquee-cont is-right">
                        <div class="marquee-black">
                            <div data-w-id="a4efb18a-b1c1-a66b-e40e-8445e5b25a25" class="marquee-track is-home-black">
                                <div class="marquee-div is-black">
                                    <div class="marquee-text-wrapper">
                                        <div class="marquee-text is-italic is-white">人工智能技术</div>
                                    </div>
                                    <div class="marquee-text is-italic is-white">-</div>
                                    <div class="marquee-text-wrapper">
                                        <div class="marquee-text is-italic is-white">软件开发</div>
                                    </div>
                                    <div class="marquee-text is-italic is-white">-</div>
                                    <div class="marquee-text-wrapper">
                                        <div class="marquee-text is-italic is-white">人工智能技术</div>
                                    </div>
                                    <div class="marquee-text is-italic is-white">-</div>
                                    <div class="marquee-text-wrapper">
                                        <div class="marquee-text is-italic is-white">软件开发</div>
                                    </div>
                                    <div class="marquee-text is-italic is-white">-</div>
                                </div>
                                <div class="marquee-div is-black">
                                    <div class="marquee-text-wrapper">
                                        <div class="marquee-text is-italic is-white">人工智能技术</div>
                                    </div>
                                    <div class="marquee-text is-italic is-white">-</div>
                                    <div class="marquee-text-wrapper">
                                        <div class="marquee-text is-italic is-white">软件开发</div>
                                    </div>
                                    <div class="marquee-text is-italic is-white">-</div>
                                    <div class="marquee-text-wrapper">
                                        <div class="marquee-text is-italic is-white">人工智能技术</div>
                                    </div>
                                    <div class="marquee-text is-italic is-white">-</div>
                                    <div class="marquee-text-wrapper">
                                        <div class="marquee-text is-italic is-white">软件开发</div>
                                    </div>
                                    <div class="marquee-text is-italic is-white">-</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div data-w-id="8a490c9d-0ef7-2ef7-5585-918bfadf1781" class="flex-center is-v is-home">
                    <div class="d-50">
                        <div class="h-light-text-wrap is-2"><p data-w-id="8a490c9d-0ef7-2ef7-5585-918bfadf178a"
                                                               class="text">Spring AI 是一个强大的Java框架，专为构建
                            AI驱动的应用程序而设计，支持多种 </p></div>
                        <h3 class="h3-iitalic">OpenAI、Azure OpenAI、Anthropic Claude、Google Gemini、Ollama 等主流AI模型。</h3></div>
                    <a href="https://spring.io/projects/spring-ai" target="_blank" class="btn-outer-frame w-inline-block">
                        <div class="btn-inner-frame">
                            <div class="btn-circle"></div>
                            <div class="btn-text-wrap">
                                <div class="btn-text">查看详情</div>
                            </div>
                            <div class="btn-bg"></div>
                        </div>
                    </a></div>
                <div data-w-id="bdbad17b-1e36-cdba-8231-14734c349423" class="article-fall-container is-logos">
                    <div class="article-fall-wrapper is-home is-default">
                        <div data-w-id="f9de1fa6-2fb0-5f42-3d95-b5ae80d7e08d" class="logo-card-wrap is-h-1 is-def">
                            <div class="logo-card is-h-1"></div>
                        </div>
                        <div data-w-id="bdbad17b-1e36-cdba-8231-14734c34943c" class="logo-card-wrap is-h-2 is-def">
                            <div class="logo-card is-h-2"></div>
                        </div>
                        <div data-w-id="21107a80-5e47-ee59-4874-03b95c8c85d6" class="logo-card-wrap is-h-3 is-def">
                            <div class="logo-card is-h-3"></div>
                        </div>
                        <div data-w-id="3f95ff90-f033-7532-4be1-0d37a695f2d3" class="logo-card-wrap is-h-4 is-def">
                            <div class="logo-card is-h-4"></div>
                        </div>
                        <div data-w-id="88f8031c-a8bc-8659-b9a6-6603aefd0cd8" class="logo-card-wrap is-h-5 is-def">
                            <div class="logo-card is-h-5"></div>
                        </div>
                        <div data-w-id="ed6164aa-06fa-e5c8-6d52-40cca4c57c36" class="logo-card-wrap is-h-6 is-def">
                            <div class="logo-card is-h-6"></div>
                        </div>
                        <div data-w-id="651632ce-cef5-77d9-2a58-e6d0b1aa1e28" class="logo-card-wrap is-h-7 is-def">
                            <div class="logo-card is-h-7"></div>
                        </div>
                    </div>
                    <div data-w-id="7c7eac27-6551-73d6-2cce-ba4945ee6cae" class="article-fall-wrapper is-home is-fall">
                        <div class="logo-card-wrap is-h-1 is-fall">
                            <div class="logo-card is-h-1"></div>
                        </div>
                        <div class="logo-card-wrap is-h-2 is-fall">
                            <div class="logo-card is-h-2"></div>
                        </div>
                        <div class="logo-card-wrap is-h-3 is-fall">
                            <div class="logo-card is-h-3"></div>
                        </div>
                        <div class="logo-card-wrap is-h-4 is-fall">
                            <div class="logo-card is-h-4"></div>
                        </div>
                        <div class="logo-card-wrap is-h-5 is-fall">
                            <div class="logo-card is-h-5"></div>
                        </div>
                        <div class="logo-card-wrap is-h-6 is-fall">
                            <div class="logo-card is-h-6"></div>
                        </div>
                        <div class="logo-card-wrap is-h-7 is-fall">
                            <div class="logo-card is-h-7"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="marq-inner border-nopad">
            <div class="moving-wrapper logo">
                <!-- 无用的外部CDN图片已删除 -->
            </div>
        </div>

        <!-- 算法演示专区 -->
        <div class="section-part-algorithm" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 80px 0; position: relative; overflow: hidden;">
            <!-- 背景装饰 -->
            <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\"><defs><pattern id=\"grid\" width=\"10\" height=\"10\" patternUnits=\"userSpaceOnUse\"><path d=\"M 10 0 L 0 0 0 10\" fill=\"none\" stroke=\"rgba(255,255,255,0.1)\" stroke-width=\"0.5\"/></pattern></defs><rect width=\"100\" height=\"100\" fill=\"url(%23grid)\"/></svg>'); opacity: 0.3;"></div>

            <div class="container-lg" style="position: relative; z-index: 2;">
                <div style="text-align: center; margin-bottom: 50px;">
                    <h2 style="color: white; font-size: 3rem; font-weight: 700; margin-bottom: 20px; text-shadow: 2px 2px 4px rgba(0,0,0,0.3);">
                        🚀 算法可视化演示
                    </h2>
                    <p style="color: rgba(255,255,255,0.9); font-size: 1.2rem; max-width: 600px; margin: 0 auto; line-height: 1.6;">
                        探索100+个交互式算法演示，包含排序、搜索、图算法、动态规划等，让抽象的算法变得生动有趣
                    </p>
                </div>

                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 30px; margin-bottom: 50px;">
                    <div style="background: rgba(255,255,255,0.1); backdrop-filter: blur(10px); border-radius: 20px; padding: 30px; text-align: center; border: 1px solid rgba(255,255,255,0.2);">
                        <div style="font-size: 3rem; margin-bottom: 15px;">📊</div>
                        <h3 style="color: white; margin-bottom: 10px; font-size: 1.3rem;">排序算法</h3>
                        <p style="color: rgba(255,255,255,0.8); font-size: 0.9rem;">冒泡、快排、归并等10+种排序算法可视化</p>
                    </div>

                    <div style="background: rgba(255,255,255,0.1); backdrop-filter: blur(10px); border-radius: 20px; padding: 30px; text-align: center; border: 1px solid rgba(255,255,255,0.2);">
                        <div style="font-size: 3rem; margin-bottom: 15px;">🌳</div>
                        <h3 style="color: white; margin-bottom: 10px; font-size: 1.3rem;">树与图</h3>
                        <p style="color: rgba(255,255,255,0.8); font-size: 0.9rem;">二叉树、AVL、红黑树、最短路径算法</p>
                    </div>

                    <div style="background: rgba(255,255,255,0.1); backdrop-filter: blur(10px); border-radius: 20px; padding: 30px; text-align: center; border: 1px solid rgba(255,255,255,0.2);">
                        <div style="font-size: 3rem; margin-bottom: 15px;">🧩</div>
                        <h3 style="color: white; margin-bottom: 10px; font-size: 1.3rem;">LeetCode题解</h3>
                        <p style="color: rgba(255,255,255,0.8); font-size: 0.9rem;">50+道经典题目的可视化解答过程</p>
                    </div>
                </div>

                <div style="text-align: center;">
                    <a href="algorithms.html" class="btn-outer-frame is-algorithm-main w-inline-block" style="background: white; border-radius: 50px; padding: 0; border: none; box-shadow: 0 15px 40px rgba(0,0,0,0.2); transition: all 0.3s ease; display: inline-block;">
                        <div class="btn-inner-frame" style="background: transparent; border-radius: 50px;">
                            <div class="btn-text-wrap" style="padding: 20px 40px;">
                                <div class="btn-text" style="color: #667eea; font-weight: 700; font-size: 18px; display: flex; align-items: center; gap: 15px; justify-content: center;">
                                    <span style="font-size: 24px;">🎯</span>
                                    立即体验算法演示
                                    <span style="font-size: 16px;">→</span>
                                </div>
                            </div>
                        </div>
                    </a>
                </div>
            </div>
        </div>

        <div class="section-part-3">
            <div data-w-id="9c958bef-b742-ca03-d024-86d0abf10e76" class="section is-h-articles">
                <div class="container-lg h-padded">
                    <div class="flex-split-btm">
                        <div data-w-id="63773e64-5275-ce39-5005-369fdb5705fc" class="hero-h-wrapper">
                            <div class="h-row">
                                <div class="h-hide-italic">
                                    <div data-w-id="98c9f5c6-e612-4115-6e05-62f2f36050fd" class="h-italic-wrapper"><h1
                                            data-w-id="0eb141e7-cdfe-aca1-6f88-90ae0d02171f" class="h1-italic">Technical Articles</h1></div>
                                    <div data-w-id="0651746c-3bae-bf6a-7116-2282832fad80" class="whipe-r-brown"></div>
                                </div>
                            </div>
                            <div class="h-row">
                                <div class="h-hide"><h1>数据结构 &amp; 算法</h1></div>
                            </div>
                        </div>
                        <div data-w-id="c0e55528-f719-097f-e39e-9de511c3a8b9" class="hero-h-wrapper is-mobile">
                            <div class="h-row">
                                <div class="h-hide-italic">
                                    <div data-w-id="c0e55528-f719-097f-e39e-9de511c3a8bc" class="h-italic-wrapper"><h1
                                            data-w-id="c0e55528-f719-097f-e39e-9de511c3a8bd" class="h1-italic">Tech
                                        Archive:</h1></div>
                                    <div class="whipe-r-brown is-hero"></div>
                                </div>
                            </div>
                            <div class="h-row">
                                <div class="h-hide"><h1>数据结构 &amp; </h1></div>
                            </div>
                            <div class="h-row">
                                <div class="h-hide"><h1>算法</h1></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="container-lg spaced">
                    <div class="article-list-wrapper-home w-dyn-list">
                        <div role="list" class="w-dyn-items">
                            <div role="listitem" class="w-dyn-item"><a href="red-black-tree.html"
                                                                       class="article-wrapper w-inline-block">
                                <div class="article-flex">
                                    <div class="article-date-wrap">
                                        <div class="text-para article-date">2025-01-15
                                        </div>
                                        <div class="whipe-l-black"></div>
                                    </div>
                                    <div class="art-heading-wrapper">
                                        <h3 class="article-heading">深入理解红黑树：手撕红黑树的完整实现与性能分析</h3></div>
                                </div>
                                <div class="card-whipe-btm bg-cream ab-low"></div>
                                <div class="paper-div">
                                    <div class="triangle"></div>
                                    <div class="btn-circle is-ab">
                                        <div class="btn-arrow-div"></div>
                                    </div>
                                </div>
                            </a></div>
                            <div role="listitem" class="w-dyn-item"><a href="blocking-queue.html"
                                                                       class="article-wrapper w-inline-block">
                                <div class="article-flex">
                                    <div class="article-date-wrap">
                                        <div class="text-para article-date">2025-01-12
                                        </div>
                                        <div class="whipe-l-black"></div>
                                    </div>
                                    <div class="art-heading-wrapper">
                                        <h3 class="article-heading">并发编程核心：手撕双锁阻塞队列的设计与实现原理</h3></div>
                                </div>
                                <div class="card-whipe-btm bg-cream ab-low"></div>
                                <div class="paper-div">
                                    <div class="triangle"></div>
                                    <div class="btn-circle is-ab">
                                        <div class="btn-arrow-div"></div>
                                    </div>
                                </div>
                            </a></div>
                            <div role="listitem" class="w-dyn-item"><a href="quick-sort.html"
                                                                       class="article-wrapper w-inline-block">
                                <div class="article-flex">
                                    <div class="article-date-wrap">
                                        <div class="text-para article-date">2025-01-08
                                        </div>
                                        <div class="whipe-l-black"></div>
                                    </div>
                                    <div class="art-heading-wrapper">
                                        <h3 class="article-heading">高效排序算法：双边快速排序的分治思想与优化策略详解</h3></div>
                                </div>
                                <div class="card-whipe-btm bg-cream ab-low"></div>
                                <div class="paper-div">
                                    <div class="triangle"></div>
                                    <div class="btn-circle is-ab">
                                        <div class="btn-arrow-div"></div>
                                    </div>
                                </div>
                            </a></div>
                            <div role="listitem" class="w-dyn-item"><a href="http://算法后续补充"
                                                                       target="_blank"
                                                                       class="article-wrapper w-inline-block">
                                <div class="article-flex">
                                    <div class="article-date-wrap">
                                        <div class="text-para article-date">2025-01-06
                                        </div>
                                        <div class="whipe-l-black"></div>
                                    </div>
                                    <div class="art-heading-wrapper">
                                        <h3 class="article-heading">稳定排序算法：归并排序的递归实现与时间复杂度分析</h3></div>
                                </div>
                                <div class="card-whipe-btm bg-cream ab-low"></div>
                                <div class="paper-div">
                                    <div class="triangle"></div>
                                    <div class="btn-circle is-ab">
                                        <div class="btn-arrow-div"></div>
                                    </div>
                                </div>
                            </a></div>
                            <div role="listitem" class="w-dyn-item"><a href="http://算法后续补充"
                                                                       target="_blank"
                                                                       class="article-wrapper w-inline-block">
                                <div class="article-flex">
                                    <div class="article-date-wrap">
                                        <div class="text-para article-date">2025-01-10
                                        </div>
                                        <div class="whipe-l-black"></div>
                                    </div>
                                    <div class="art-heading-wrapper">
                                        <h3 data-w-id="08b32825-e172-acb3-ad21-9c4c2e11bd34" class="article-heading">
                                            Java并发编程：手写ReentrantLock可重入锁的底层实现</h3></div>
                                </div>
                                <div class="card-whipe-btm bg-cream ab-low"></div>
                                <div data-w-id="5f0560bb-80ad-8019-e00e-3ad727b6da62" class="paper-div">
                                    <div class="triangle"></div>
                                    <div data-w-id="a9b6a89d-ff68-0b9c-0774-b50512a4832a" class="btn-circle is-ab">
                                        <div data-w-id="a9b6a89d-ff68-0b9c-0774-b50512a4832b"
                                             class="btn-arrow-div"></div>
                                    </div>
                                </div>
                            </a></div>
                            <div role="listitem" class="w-dyn-item"><a href="http://算法后续补充"
                                                                       target="_blank"
                                                                       class="article-wrapper w-inline-block">
                                <div class="article-flex">
                                    <div class="article-date-wrap">
                                        <div class="text-para article-date">2025-01-08
                                        </div>
                                        <div data-w-id="ddbb4ddb-e257-1f4c-8d55-76ecdc5627fe"
                                             class="whipe-l-black"></div>
                                    </div>
                                    <div data-w-id="f21f91db-54e6-d396-3c09-9197eae917d4" class="art-heading-wrapper">
                                        <h3 data-w-id="08b32825-e172-acb3-ad21-9c4c2e11bd34" class="article-heading">
                                            图论算法经典：迪杰斯特拉最短路径算法的贪心策略与实现</h3></div>
                                </div>
                                <div class="card-whipe-btm bg-cream ab-low"></div>
                                <div data-w-id="5f0560bb-80ad-8019-e00e-3ad727b6da62" class="paper-div">
                                    <div class="triangle"></div>
                                    <div data-w-id="a9b6a89d-ff68-0b9c-0774-b50512a4832a" class="btn-circle is-ab">
                                        <div data-w-id="a9b6a89d-ff68-0b9c-0774-b50512a4832b"
                                             class="btn-arrow-div"></div>
                                    </div>
                                </div>
                            </a></div>
                            <div role="listitem" class="w-dyn-item"><a href="http://算法后续补充"
                                                                       target="_blank"
                                                                       class="article-wrapper w-inline-block">
                                <div class="article-flex">
                                    <div class="article-date-wrap">
                                        <div class="text-para article-date">2025-01-04
                                        </div>
                                        <div data-w-id="ddbb4ddb-e257-1f4c-8d55-76ecdc5627fe"
                                             class="whipe-l-black"></div>
                                    </div>
                                    <div data-w-id="f21f91db-54e6-d396-3c09-9197eae917d4" class="art-heading-wrapper">
                                        <h3 class="article-heading">
                                            数据结构与算法：基于二叉堆的原地堆排序算法详解</h3></div>
                                </div>
                                <div class="card-whipe-btm bg-cream ab-low"></div>
                                <div data-w-id="5f0560bb-80ad-8019-e00e-3ad727b6da62" class="paper-div">
                                    <div class="triangle"></div>
                                    <div data-w-id="a9b6a89d-ff68-0b9c-0774-b50512a4832a" class="btn-circle is-ab">
                                        <div data-w-id="a9b6a89d-ff68-0b9c-0774-b50512a4832b"
                                             class="btn-arrow-div"></div>
                                    </div>
                                </div>
                            </a></div>
                            <div role="listitem" class="w-dyn-item"><a href="http://算法后续补充"
                                                                       target="_blank"
                                                                       class="article-wrapper w-inline-block">
                                <div class="article-flex">
                                    <div class="article-date-wrap">
                                        <div class="text-para article-date">2025-01-02
                                        </div>
                                        <div data-w-id="ddbb4ddb-e257-1f4c-8d55-76ecdc5627fe"
                                             class="whipe-l-black"></div>
                                    </div>
                                    <div data-w-id="f21f91db-54e6-d396-3c09-9197eae917d4" class="art-heading-wrapper">
                                        <h3 class="article-heading">字符串匹配算法：KMP算法原理与next数组构建详解</h3></div>
                                </div>
                                <div class="card-whipe-btm bg-cream ab-low"></div>
                                <div data-w-id="5f0560bb-80ad-8019-e00e-3ad727b6da62" class="paper-div">
                                    <div class="triangle"></div>
                                    <div data-w-id="a9b6a89d-ff68-0b9c-0774-b50512a4832a" class="btn-circle is-ab">
                                        <div data-w-id="a9b6a89d-ff68-0b9c-0774-b50512a4832b"
                                             class="btn-arrow-div"></div>
                                    </div>
                                </div>
                            </a></div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="section-part-4">
                <div data-w-id="872a7b88-2671-35b5-7146-d94249887452" class="section home-int-wrapper">
                    <div data-w-id="f7d79d73-4ef9-8b1b-f541-f6ea4501c5d2" class="section-100 is-home-int">
                        <div class="container-lg">
                            <div class="circle-cta-wrapper is-home is-interview">
                                <div class="inner-set-gal">
                                    <div class="inner-circle-cream is-home"></div>
                                    <div class="inner-circle-white is-home"></div>
                                    <div style="-webkit-transform:translate3d(0, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0deg) skew(0, 0);-moz-transform:translate3d(0, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0deg) skew(0, 0);-ms-transform:translate3d(0, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0deg) skew(0, 0);transform:translate3d(0, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0deg) skew(0, 0)"
                                         class="inner-dotted is-home"></div>
                                </div>
                                <div class="div-block-12">
                                    <div class="div-block-11 is-home">
                                        <div class="h-hide-italic"><h1 class="h1-italic">TECH VISION:</h1></div>
                                        <div class="h-hide"><h1>探索AI前沿技术</h1></div>
                                        <div class="h-hide"><h1>人工智能技术分享</h1></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="interview-frame-wrapper">
                            <div class="int-set is-1">
                                <div class="collection-list-wrapper-4 w-dyn-list">
                                    <div role="list" class="collection-list-3 w-dyn-items">
                                        <div role="listitem" class="collection-item-4 w-dyn-item">
                                            <div class="home-int-div is-1">
                                                <div class="home-int-img-wrapper">
                                                    <div class="img-parent"><a
                                                            data-w-id="e74e48ca-6357-163d-9b00-460c023d37ad"
                                                            href="https://www.youtube.com/watch?v=8bfMq2ZqEZU"
                                                            class="btn-outer-frame is-ab w-inline-block">
                                                        <div class="btn-inner-frame">
                                                            <div data-w-id="e74e48ca-6357-163d-9b00-460c023d37b2"
                                                                 class="btn-circle"></div>
                                                            <div class="btn-text-wrap">
                                                                <div data-w-id="e74e48ca-6357-163d-9b00-460c023d37b0"
                                                                     class="btn-text">查看详情
                                                                </div>
                                                            </div>
                                                            <div data-w-id="e74e48ca-6357-163d-9b00-460c023d37b3"
                                                                 class="btn-bg-fill"></div>
                                                        </div>
                                                    </a>
                                                        <div style="background-color: #f0f0f0;"
                                                             class="ft-image"></div>
                                                    </div>
                                                </div>
                                                <div class="flex-v-split is-home-int">
                                                    <div class="d-60"><h3>Java并发编程最佳实践分享</h3></div>
                                                    <div class="flex-right">
                                                        <div class="d-60 is-flex-l"><p
                                                                data-w-id="14985a2d-3227-5db4-1a41-9635056eb79f"
                                                                class="text">深入探讨Java并发编程的核心概念，包括线程池、锁机制、并发集合等关键技术的实际应用和性能优化策略。</p></div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="int-set is-2">
                                    <div class="collection-list-wrapper-4 w-dyn-list">
                                        <div role="list" class="collection-list-3 w-dyn-items">
                                            <div role="listitem" class="collection-item-4 w-dyn-item">
                                                <div class="home-int-div is-2">
                                                    <div class="home-int-img-wrapper">
                                                        <div class="img-parent">
                                                            <div style="background-color: #f0f0f0;"
                                                                 class="ft-image"></div>
                                                        </div>
                                                    </div>
                                                    <div class="flex-v-split is-home-int">
                                                        <div class="d-60"><h3>Spring Boot微服务架构设计</h3>
                                                        </div>
                                                        <div class="flex-right">
                                                            <div class="d-60 is-flex-l"><p
                                                                    data-w-id="0377e523-1715-1e95-92d4-bafc90038219"
                                                                    class="text">Special Session of the Spring Boot微服务架构设计中 in May
                                                                2009, titled “Out in the Wilderness: Dayan Jayatilleka
                                                                on 13th Amendment and getting sacked by Boggles”</p><a
                                                                    data-w-id="ef9a8e5c-6b32-eb56-2877-2edd970241d8"
                                                                    href="https://blacklightarrow.wordpress.com/2009/07/23/out-in-the-wilderness-dayan-jayatilleka-on-pleading-the-13th-being-a-hippy-and-getting-sacked-by-boggles/"
                                                                    class="btn-outer-frame w-inline-block">
                                                                <div class="btn-inner-frame">
                                                                    <div class="btn-text-wrap">
                                                                        <div data-w-id="ef9a8e5c-6b32-eb56-2877-2edd970241db"
                                                                             class="btn-text">查看详情
                                                                        </div>
                                                                    </div>
                                                                    <div data-w-id="ef9a8e5c-6b32-eb56-2877-2edd970241dd"
                                                                         class="btn-circle"></div>
                                                                    <div data-w-id="ef9a8e5c-6b32-eb56-2877-2edd970241de"
                                                                         class="btn-bg"></div>
                                                                </div>
                                                            </a></div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="int-set is-3">
                                        <div class="collection-list-wrapper-4 w-dyn-list">
                                            <div role="list" class="collection-list-3 w-dyn-items">
                                                <div role="listitem" class="collection-item-4 w-dyn-item">
                                                    <div class="home-int-div is-3">
                                                        <div class="home-int-img-wrapper">
                                                            <div class="img-parent">
                                                                <div style="background-color: #f0f0f0;"
                                                                     class="ft-image"></div>
                                                            </div>
                                                        </div>
                                                        <div class="flex-v-split is-home-int">
                                                            <div class="d-60"><h3>算法优化：从O(n²)到O(n log n)</h3></div>
                                                            <div class="flex-right">
                                                                <div class="d-60"><p
                                                                        data-w-id="4823101c-4d0d-b692-61f3-2bdba51b8721"
                                                                        class="text">深入分析常见算法的时间复杂度优化技巧，通过实际案例展示如何将低效算法优化为高效解决方案。</p></div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="int-set is-4">
                                            <div class="collection-list-wrapper-4 w-dyn-list">
                                                <div role="list" class="collection-list-3 w-dyn-items">
                                                    <div role="listitem" class="collection-item-4 w-dyn-item">
                                                        <div class="home-int-div is-4">
                                                            <div class="home-int-img-wrapper">
                                                                <div class="img-parent">
                                                                    <div style="background-color: #f0f0f0;"
                                                                         class="ft-image"></div>
                                                                </div>
                                                            </div>
                                                            <div class="flex-v-split is-home-int">
                                                                <div class="d-60"><h3>数据结构在实际项目中的应用</h3></div>
                                                                <div class="flex-right">
                                                                    <div class="d-60"><p
                                                                            data-w-id="aa49dcfb-20db-4ce5-5274-089aebfe2f73"
                                                                            class="text">The first interview on video
                                                                        given by 数据结构专家 after he was asked to
                                                                        quit his position as Sri Lanka’s Permanent
                                                                        Representative to the UN in Geneva</p></div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="int-set is-5">
                                                <div class="int-cta-block">
                                                    <div class="flex-v-split is-home-int final-cta">
                                                        <div class="d-60 is-flex-l"><h3>Discover More</h3>
                                                            <p data-w-id="09042df4-67b2-28ad-9a1d-beccdf40ba44"
                                                               class="text">A collection of nterviews taken over the
                                                                years of service as an 软件工程师和技术
                                                                专家, and  项目开发经验分享。<br/><br/>发现更多关于软件工程和技术创新的精彩内容。 </p></div>
                                                        <div class="flex-right"><a
                                                                data-w-id="3ef64cd4-7c47-6f5a-9839-05794a60ae87"
                                                                href="algorithms.html"
                                                                class="btn-outer-frame is-desktop w-inline-block">
                                                            <div class="btn-inner-frame">
                                                                <div class="btn-text-wrap">
                                                                    <div class="btn-text text-white">查看所有项目
                                                                    </div>
                                                                </div>
                                                                <div data-w-id="3ef64cd4-7c47-6f5a-9839-05794a60ae8c"
                                                                     class="btn-circle"></div>
                                                                <div data-w-id="3ef64cd4-7c47-6f5a-9839-05794a60ae8d"
                                                                     class="btn-bg"></div>
                                                            </div>
                                                        </a></div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="int-card-wrapper">
                        <div class="int-card-set">
                            <div class="int-set is-1">
                                <div class="collection-list-wrapper-4 w-dyn-list">
                                    <div role="list" class="collection-list-3 w-dyn-items">
                                        <div role="listitem" class="collection-item-4 w-dyn-item"><a
                                                href="https://www.anthropic.com/claude" target="_blank"
                                                class="home-int-div is-1 w-inline-block">
                                            <div class="home-int-img-wrapper">
                                                <div class="img-parent">
                                                    <div style="background-image:url(&quot;./images/claude4.png&quot;)"
                                                         class="ft-image"></div>
                                                </div>
                                            </div>
                                            <div class="flex-v-split is-home-int"><h3>Claude 4.0 正式发布：
                                                下一代AI助手</h3>
                                                <div class="d-80"><p data-w-id="d95a7c84-126d-6785-d94c-09f3f7f69209"
                                                                     class="text">Anthropic发布了Claude 4.0，
                                                    在推理能力、安全性和多模态理解方面实现了重大突破，标志着AI助手进入新时代。</p></div>
                                            </div>
                                            <div class="paper-div is-int">
                                                <div class="triangle"></div>
                                                <div class="btn-circle is-ab">
                                                    <div class="btn-arrow-div"></div>
                                                </div>
                                            </div>
                                        </a></div>
                                    </div>
                                </div>
                            </div>
                            <div class="int-set is-2">
                                <div class="collection-list-wrapper-4 w-dyn-list">
                                    <div role="list" class="collection-list-3 w-dyn-items">
                                        <div role="listitem" class="collection-item-4 w-dyn-item"><a
                                                href="https://openai.com/research/gpt-4"
                                                target="_blank" class="home-int-div is-1 w-inline-block">
                                            <div class="home-int-img-wrapper">
                                                <div class="img-parent">
                                                    <div style="background-image:url(&quot;./images/gpt4.jpg&quot;)"
                                                         class="ft-image"></div>
                                                </div>
                                            </div>
                                            <div class="flex-v-split is-home-int"><h3>GPT-4多模态能力深度解析</h3>
                                                <div class="d-80"><p data-w-id="9b380cec-cc56-2ccc-675c-9dc3f60565e2"
                                                                     class="text">OpenAI的GPT-4展现了强大的多模态理解能力，
                                                    2009, titled “能够同时处理文本、图像等多种输入格式，为AI应用开发带来了新的可能性”</p></div>
                                            </div>
                                            <div class="paper-div is-int">
                                                <div class="triangle"></div>
                                                <div class="btn-circle is-ab">
                                                    <div class="btn-arrow-div"></div>
                                                </div>
                                            </div>
                                        </a></div>
                                    </div>
                                </div>
                            </div>
                            <div class="int-set is-3">
                                <div class="collection-list-wrapper-4 w-dyn-list">
                                    <div role="list" class="collection-list-3 w-dyn-items">
                                        <div role="listitem" class="collection-item-4 w-dyn-item"><a
                                                href="https://github.com/microsoft/semantic-kernel"
                                                target="_blank" class="home-int-div is-1 w-inline-block">
                                            <div class="home-int-img-wrapper">
                                                <div class="img-parent">
                                                    <div style="background-image:url(&quot;./images/semantic.webp&quot;)"
                                                         class="ft-image"></div>
                                                </div>
                                            </div>
                                            <div class="flex-v-split is-home-int"><h3>微软Semantic Kernel框架详解</h3>
                                                <div class="d-80"><p data-w-id="7058f3a0-3516-62f6-0fac-c906b083a561"
                                                                     class="text">微软推出的Semantic Kernel是一个轻量级SDK，
                                                    让开发者能够轻松地将AI大语言模型集成到传统编程语言中。</p></div>
                                            </div>
                                            <div class="paper-div is-int">
                                                <div class="triangle"></div>
                                                <div class="btn-circle is-ab">
                                                    <div class="btn-arrow-div"></div>
                                                </div>
                                            </div>
                                        </a></div>
                                    </div>
                                </div>
                            </div>
                            <div class="int-set is-4">
                                <div class="collection-list-wrapper-4 w-dyn-list">
                                    <div role="list" class="collection-list-3 w-dyn-items">
                                        <div role="listitem" class="collection-item-4 w-dyn-item"><a
                                                href="https://www.modelcontextprotocol.io/"
                                                target="_blank" class="home-int-div is-1 w-inline-block">
                                            <div class="home-int-img-wrapper">
                                                <div class="img-parent">
                                                    <div style="background-image:url(&quot;./images/mcp.webp&quot;)"
                                                         class="ft-image"></div>
                                                </div>
                                            </div>
                                            <div class="flex-v-split is-home-int"><h3>MCP协议：AI应用的新标准</h3>
                                                <div class="d-80"><p data-w-id="31296c1e-d06b-423d-477a-68d4b5a4e496"
                                                                     class="text">Model Context Protocol (MCP) 是一个开放标准，
                                                    旨在统一AI应用与外部数据源的交互方式，提高开发效率和系统互操作性。该协议为开发者提供了标准化的接口 after he was asked to quit his position as Sri
                                                    Lanka’s Permanent Representative to the UN in Geneva</p></div>
                                            </div>
                                            <div class="paper-div is-int">
                                                <div class="triangle"></div>
                                                <div class="btn-circle is-ab">
                                                    <div class="btn-arrow-div"></div>
                                                </div>
                                            </div>
                                        </a></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="gallery-marquee-container is-home"><div
                                data-w-id="0d4ca99a-ae50-8383-2bce-135328286864"
                                class="gallery-marq-wrap is-home w-inline-block">
                            <div class="marquee-move-container">
                                <div data-w-id="0d4ca99a-ae50-8383-2bce-135328286865"
                                     class="marquee-mov is-home-int is-1">
                                    <div class="marq-set is-home-int">
                                        <div class="text-cap-wrap is-home-int">
                                            <div data-w-id="0d4ca99a-ae50-8383-2bce-135328286869"
                                                 class="text-caps is-home-int">数据结构
                                            </div>
                                        </div>
                                        <div data-w-id="0d4ca99a-ae50-8383-2bce-13532828686b"
                                             class="marquee-circle is-gal-dash"></div>
                                        <div class="text-cap-wrap is-home-int">
                                            <div data-w-id="c236cb0a-85e5-b566-08db-5bc35e7b8080"
                                                 class="text-caps is-home-int">算法可视化
                                            </div>
                                        </div>
                                        <div data-w-id="0d4ca99a-ae50-8383-2bce-13532828686f"
                                             class="marquee-circle is-gal-dash"></div>
                                    </div>
                                    <div class="marq-set is-home-int">
                                        <div class="text-cap-wrap is-home-int">
                                            <div data-w-id="291100c1-1af9-60fe-d690-0b9b45d03e0e"
                                                 class="text-caps is-home-int">LeetCode题解
                                            </div>
                                        </div>
                                        <div data-w-id="291100c1-1af9-60fe-d690-0b9b45d03e10"
                                             class="marquee-circle is-gal-dash"></div>
                                        <div class="text-cap-wrap is-home-int">
                                            <div data-w-id="291100c1-1af9-60fe-d690-0b9b45d03e12"
                                                 class="text-caps is-home-int">交互式演示
                                            </div>
                                        </div>
                                        <div data-w-id="291100c1-1af9-60fe-d690-0b9b45d03e14"
                                             class="marquee-circle is-gal-dash"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="marquee-move-container is-right">
                                <div data-w-id="2f75b7c9-2d9f-76fa-8081-40c94c767144"
                                     class="marquee-mov is-home-int is-2">
                                    <div class="marq-set is-home-int">
                                        <div class="text-cap-wrap is-home-int">
                                            <div data-w-id="2f75b7c9-2d9f-76fa-8081-40c94c767147"
                                                 class="text-caps is-home-int">神经网络
                                            </div>
                                        </div>
                                        <div data-w-id="2f75b7c9-2d9f-76fa-8081-40c94c767149"
                                             class="marquee-circle is-gal-dash"></div>
                                        <div class="text-cap-wrap is-home-int">
                                            <div data-w-id="2f75b7c9-2d9f-76fa-8081-40c94c76714b"
                                                 class="text-caps is-home-int">自然语言处理
                                            </div>
                                        </div>
                                        <div data-w-id="2f75b7c9-2d9f-76fa-8081-40c94c76714d"
                                             class="marquee-circle is-gal-dash"></div>
                                    </div>
                                    <div class="marq-set is-home-int">
                                        <div class="text-cap-wrap is-home-int">
                                            <div data-w-id="2f75b7c9-2d9f-76fa-8081-40c94c767150"
                                                 class="text-caps is-home-int">计算机视觉
                                            </div>
                                        </div>
                                        <div data-w-id="2f75b7c9-2d9f-76fa-8081-40c94c767152"
                                             class="marquee-circle is-gal-dash"></div>
                                        <div class="text-cap-wrap is-home-int">
                                            <div data-w-id="2f75b7c9-2d9f-76fa-8081-40c94c767154"
                                                 class="text-caps is-home-int">大语言模型
                                            </div>
                                        </div>
                                        <div data-w-id="2f75b7c9-2d9f-76fa-8081-40c94c767156"
                                             class="marquee-circle is-gal-dash"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                            <div style="-webkit-transform:translate3d(0, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0deg) skew(0, 0);-moz-transform:translate3d(0, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0deg) skew(0, 0);-ms-transform:translate3d(0, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0deg) skew(0, 0);transform:translate3d(0, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0deg) skew(0, 0)"
                                 class="gallery-marquee-bdr is-home"></div>
                        </div>
                    </div>
                </div>

                    <div class="section is-h-books">
                        <div class="container-lg h-padded">
                            <div class="flex-split-btm">
                                <div data-w-id="6e9f24a5-fc88-80db-cb3c-ea5a9f733967" class="hero-h-wrapper">
                                    <div class="h-row">
                                        <div class="h-hide-italic">
                                            <div style="-webkit-transform:translate3d(0, 0, 0) scale3d(1, 1, 1) rotateX(-50deg) rotateY(null) rotateZ(0) skew(-30deg, 0);-moz-transform:translate3d(0, 0, 0) scale3d(1, 1, 1) rotateX(-50deg) rotateY(null) rotateZ(0) skew(-30deg, 0);-ms-transform:translate3d(0, 0, 0) scale3d(1, 1, 1) rotateX(-50deg) rotateY(null) rotateZ(0) skew(-30deg, 0);transform:translate3d(0, 0, 0) scale3d(1, 1, 1) rotateX(-50deg) rotateY(null) rotateZ(0) skew(-30deg, 0);transform-style:preserve-3d"
                                                 class="h-italic-wrapper"><h1 style="opacity:0" class="h1-italic">
                                                推荐书籍</h1></div>
                                            <div style="width:100%" class="whipe-r-brown"></div>
                                        </div>
                                        <div class="h-hide"><h1>Java编程</h1></div>
                                    </div>
                                    <div class="h-row">
                                        <div class="h-hide"><h1>与技术成长</h1></div>
                                    </div>
                                </div>
                                <div data-w-id="eaa7a905-2516-9b42-9df8-8d82b31b2c98" class="hero-h-wrapper is-mobile">
                                    <div class="h-row">
                                        <div class="h-hide-italic">
                                            <div style="-webkit-transform:translate3d(0, 0, 0) scale3d(1, 1, 1) rotateX(-50deg) rotateY(null) rotateZ(0) skew(-30deg, 0);-moz-transform:translate3d(0, 0, 0) scale3d(1, 1, 1) rotateX(-50deg) rotateY(null) rotateZ(0) skew(-30deg, 0);-ms-transform:translate3d(0, 0, 0) scale3d(1, 1, 1) rotateX(-50deg) rotateY(null) rotateZ(0) skew(-30deg, 0);transform:translate3d(0, 0, 0) scale3d(1, 1, 1) rotateX(-50deg) rotateY(null) rotateZ(0) skew(-30deg, 0);transform-style:preserve-3d"
                                                 class="h-italic-wrapper"><h1 style="opacity:0" class="h1-italic">
                                                推荐书籍</h1></div>
                                            <div class="whipe-r-brown is-hero"></div>
                                        </div>
                                    </div>
                                    <div class="h-row">
                                        <div class="h-hide"><h1>Java编程</h1></div>
                                    </div>
                                    <div class="h-row">
                                        <div class="h-hide"><h1>与技术成长</h1></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="container-lg h-padded is-mob-change is-books">
                            <div data-w-id="f15e6e28-708c-7666-9390-4119b1a237dd" class="book-card-wrapper">
                                <div class="book-collection-wrapper is-1 w-dyn-list">
                                    <div role="list" class="book-col-home w-dyn-items">
                                        <div id="w-node-_71b3ccf8-2e5e-4785-7ecd-7b3d5ab32ef4-08dd6ff4" role="listitem"
                                             class="collection-item-2 w-dyn-item">
                                            <div class="book-card home">
                                                <div class="top-wrapper">
                                                    <div class="book-img-wrapper">
                                                        <div class="img-parent">
                                                            <div style="background-image:url(&quot;./images/effective-java-book.jpg&quot;)"
                                                                 class="img-child home books"></div>
                                                        </div>
                                                    </div>
                                                    <div class="book-title-wrapper">
                                                        <div class="book-title">Effective Java:
                                                            编写高质量Java代码的90个建议
                                                        </div>
                                                    </div>
                                                </div>
                                                <a href="https://www.amazon.com/Effective-Java-Joshua-Bloch/dp/0134685997"
                                                   target="_blank" class="btn-outer-cta-external not-ab w-inline-block">
                                                    <div class="btn-inner-frame is-small">
                                                        <div class="btn-circle"></div>
                                                        <div class="btn-text-wrap">
                                                            <div class="btn-text">阅读书籍</div>
                                                        </div>
                                                        <div class="btn-bg"></div>
                                                    </div>
                                                </a></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="book-collection-wrapper is-2 w-dyn-list">
                                    <div role="list" class="book-col-home w-dyn-items">
                                        <div id="w-node-_88b8edde-dc24-7e76-4179-3612e00e144d-08dd6ff4" role="listitem"
                                             class="collection-item-2 w-dyn-item">
                                            <div class="book-card home">
                                                <div class="top-wrapper">
                                                    <div class="book-img-wrapper">
                                                        <div class="img-parent">
                                                            <div style="background-image:url(&quot;./images/spring-in-action-book.jpg&quot;)"
                                                                 class="img-child home books"></div>
                                                        </div>
                                                    </div>
                                                    <div class="book-title-wrapper">
                                                        <div class="book-title">Spring实战：
                                                            构建企业级Java应用程序
                                                        </div>
                                                    </div>
                                                </div>
                                                <a data-w-id="88b8edde-dc24-7e76-4179-3612e00e1455"
                                                   href="https://www.amazon.com/Spring-Action-Craig-Walls/dp/1617294942"
                                                   target="_blank" class="btn-outer-cta-external not-ab w-inline-block">
                                                    <div class="btn-inner-frame is-small">
                                                        <div data-w-id="88b8edde-dc24-7e76-4179-3612e00e1457"
                                                             class="btn-circle"></div>
                                                        <div class="btn-text-wrap">
                                                            <div data-w-id="88b8edde-dc24-7e76-4179-3612e00e1459"
                                                                 class="btn-text">阅读书籍
                                                            </div>
                                                        </div>
                                                        <div data-w-id="88b8edde-dc24-7e76-4179-3612e00e145b"
                                                             class="btn-bg"></div>
                                                    </div>
                                                </a></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="book-collection-wrapper is-3 w-dyn-list">
                                    <div role="list" class="book-col-home w-dyn-items">
                                        <div id="w-node-_81d3b725-19fd-b0c8-b1a9-fcd0b8fb0ef5-08dd6ff4" role="listitem"
                                             class="collection-item-2 w-dyn-item">
                                            <div class="book-card home">
                                                <div class="top-wrapper">
                                                    <div class="book-img-wrapper">
                                                        <div class="img-parent">
                                                            <div style="background-image:url(&quot;./images/clean-code-book.jpg&quot;)"
                                                                 class="img-child home books"></div>
                                                        </div>
                                                    </div>
                                                    <div class="book-title-wrapper">
                                                        <div class="book-title">代码整洁之道：
                                                            编写可读、可维护、可扩展代码的艺术
                                                        </div>
                                                    </div>
                                                </div>
                                                <a data-w-id="81d3b725-19fd-b0c8-b1a9-fcd0b8fb0efd"
                                                   href="https://www.amazon.com/Clean-Code-Handbook-Software-Craftsmanship/dp/0132350882"
                                                   target="_blank" class="btn-outer-cta-external not-ab w-inline-block">
                                                    <div class="btn-inner-frame is-small">
                                                        <div data-w-id="81d3b725-19fd-b0c8-b1a9-fcd0b8fb0eff"
                                                             class="btn-circle"></div>
                                                        <div class="btn-text-wrap">
                                                            <div data-w-id="81d3b725-19fd-b0c8-b1a9-fcd0b8fb0f01"
                                                                 class="btn-text">阅读书籍
                                                            </div>
                                                        </div>
                                                        <div data-w-id="81d3b725-19fd-b0c8-b1a9-fcd0b8fb0f03"
                                                             class="btn-bg"></div>
                                                    </div>
                                                </a></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="book-collection-wrapper is-4 w-dyn-list">
                                    <div role="list" class="book-col-home w-dyn-items">
                                        <div id="w-node-e0cb3584-c9c5-5972-8903-b9f5463ce838-08dd6ff4" role="listitem"
                                             class="collection-item-2 w-dyn-item">
                                            <div class="book-card home">
                                                <div class="top-wrapper">
                                                    <div class="book-img-wrapper">
                                                        <div class="img-parent">
                                                            <div style="background-image:url(&quot;./images/java-concurrency-book.jpg&quot;)"
                                                                 class="img-child home books"></div>
                                                        </div>
                                                    </div>
                                                    <div class="book-title-wrapper">
                                                        <div class="book-title">Java并发编程实战：
                                                            构建高效、可扩展的并发应用
                                                        </div>
                                                    </div>
                                                </div>
                                                <a data-w-id="e0cb3584-c9c5-5972-8903-b9f5463ce840"
                                                   href="https://www.amazon.com/Java-Concurrency-Practice-Brian-Goetz/dp/0321349601"
                                                   target="_blank" class="btn-outer-cta-external not-ab w-inline-block">
                                                    <div class="btn-inner-frame is-small">
                                                        <div data-w-id="e0cb3584-c9c5-5972-8903-b9f5463ce842"
                                                             class="btn-circle"></div>
                                                        <div class="btn-text-wrap">
                                                            <div data-w-id="e0cb3584-c9c5-5972-8903-b9f5463ce844"
                                                                 class="btn-text">阅读书籍
                                                            </div>
                                                        </div>
                                                        <div data-w-id="e0cb3584-c9c5-5972-8903-b9f5463ce846"
                                                             class="btn-bg"></div>
                                                    </div>
                                                </a></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div data-w-id="7119ab40-7a6d-0d87-ac62-dab6c31a2f86" class="home-article-marquee-section">
                            <div class="ab-btn-wrapper"><a href="/books" class="btn-outer-cta is-big w-inline-block">
                                <div class="btn-inner-frame is-big">
                                    <div class="btn-circle"></div>
                                    <div class="btn-text-wrap">
                                        <div class="btn-text">查看更多书籍</div>
                                    </div>
                                    <div class="btn-bg-white"></div>
                                </div>
                            </a></div>
                        </div>
                    </div>
                    <div class="section-part-6">
                        <div class="footer">
                            <div class="footer-flex">
                                <div class="footer-big-txt-wrap">
                                    <div data-w-id="707df24f-f864-0c9d-b999-ad6ddf782419" class="footer-big-txt" style="font-size: 3rem; line-height: 1.4; margin-bottom: 20px;">联系我
                                    </div>
                                </div>
                                <div class="footer-text-wrap">
                                    <div data-w-id="707df24f-f864-0c9d-b999-ad6ddf78241c" class="text">专注于Java开发、人工智能技术研究与分享。
                                        欢迎交流学习，共同探索技术前沿。<br/>
                                        <br/>
                                        📧 邮箱: <EMAIL><br/>
                                        🐙 GitHub: github.com/yourusername</div>
                                </div>
                                <!-- 社交媒体链接 -->
                                <div class="footer-social-links" style="margin-top: 20px;">
                                    <a href="mailto:<EMAIL>" class="social-link" style="color: rgb(37, 35, 33); text-decoration: none; margin-right: 20px; display: inline-block;">
                                        <span style="font-size: 18px;">📧</span> 邮箱联系
                                    </a>
                                    <a href="https://github.com/yourusername" target="_blank" class="social-link" style="color: rgb(37, 35, 33); text-decoration: none; display: inline-block;">
                                        <span style="font-size: 18px;">🐙</span> GitHub
                                    </a>
                                </div>
                            </div>
                            <div class="footer-right">
                                <div class="gtnp-text">了解更多</div>
                                <a href="/about" class="np-link-wrap w-inline-block">
                                    <div data-w-id="3c2dc2c1-5833-7fbf-5033-b59166803337"
                                         style="-webkit-transform:translate3d(0, 0, 0) scale3d(0.6, 0.6, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);-moz-transform:translate3d(0, 0, 0) scale3d(0.6, 0.6, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);-ms-transform:translate3d(0, 0, 0) scale3d(0.6, 0.6, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);transform:translate3d(0, 0, 0) scale3d(0.6, 0.6, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0)"
                                         class="btn-circle is-footer"></div>
                                    <div data-w-id="707df24f-f864-0c9d-b999-ad6ddf782422" class="np-text">关于我</div>
                                </a></div>
                            <div data-w-id="715b3b6f-7bc7-298e-16f3-8ddda55edd83" class="footer-bg"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<div data-w-id="3e239b0a-e3c6-cd31-0686-592f50448adc" class="transition">
    <div data-w-id="3e239b0a-e3c6-cd31-0686-592f50448add" class="transing-out-box">
        <div data-w-id="3e239b0a-e3c6-cd31-0686-592f50448ade" class="trans-box"></div>
    </div>
</div>
<div data-w-id="785f348f-1e0c-89e5-4e43-6d937c68dd88" class="transition-intro">
    <div data-w-id="785f348f-1e0c-89e5-4e43-6d937c68dd8d" class="trans-box-out">
        <div class="trans-box-in">
            <div class="intro-hide-box">
                <div data-w-id="b2880d8b-a9a6-4339-bbf4-83eec2220769" class="home-intro-marquee-box">
                    <div class="marquee-flex is-hidden">
                        <div data-w-id="2e7d28b4-765d-ccce-9e43-d4dbf2307c8b" class="whipe-cream-intro is-top"></div>
                        <div class="intro-marquee-container">
                            <div style="-webkit-transform:translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);-moz-transform:translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);-ms-transform:translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);transform:translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0)"
                                 class="home-intro-marquee is-1">
                                <div class="home-intro-m-set">
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Java Developer</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Software Engineer</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">AI Enthusiast</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Author</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Tech Blogger</div>
                                    </div>
                                    <div class="m-dash"></div>
                                </div>
                                <div class="home-intro-m-set">
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Java Developer</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Software Engineer</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">AI Enthusiast</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Author</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Tech Blogger</div>
                                    </div>
                                    <div class="m-dash"></div>
                                </div>
                                <div class="home-intro-m-set">
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Java Developer</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Software Engineer</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">AI Enthusiast</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Author</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Tech Blogger</div>
                                    </div>
                                    <div class="m-dash"></div>
                                </div>
                                <div class="home-intro-m-set">
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Java Developer</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Software Engineer</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">AI Enthusiast</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Author</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Tech Blogger</div>
                                    </div>
                                    <div class="m-dash"></div>
                                </div>
                            </div>
                        </div>
                        <div class="intro-marquee-container is-right">
                            <div style="-webkit-transform:translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);-moz-transform:translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);-ms-transform:translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);transform:translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0)"
                                 class="home-intro-marquee is-2">
                                <div class="home-intro-m-set is-grey">
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">500+ Commits</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">30+ Projects</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">10+ Libraries</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">20+ Years</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                </div>
                                <div class="home-intro-m-set is-grey">
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">500+ Commits</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">30+ Projects</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">10+ Libraries</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">20+ Years</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                </div>
                                <div class="home-intro-m-set is-grey">
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">500+ Commits</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">30+ Projects</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">10+ Libraries</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">20+ Years</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                </div>
                                <div class="home-intro-m-set is-grey">
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">500+ Commits</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">30+ Projects</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">10+ Libraries</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">20+ Years</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                </div>
                            </div>
                        </div>
                        <div class="intro-marquee-container">
                            <div style="-webkit-transform:translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);-moz-transform:translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);-ms-transform:translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);transform:translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0)"
                                 class="home-intro-marquee is-1">
                                <div class="home-intro-m-set">
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Java Developer</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Software Engineer</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">AI Enthusiast</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Author</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Tech Blogger</div>
                                    </div>
                                    <div class="m-dash"></div>
                                </div>
                                <div class="home-intro-m-set">
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Java Developer</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Software Engineer</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">AI Enthusiast</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Author</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Tech Blogger</div>
                                    </div>
                                    <div class="m-dash"></div>
                                </div>
                                <div class="home-intro-m-set">
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Java Developer</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Software Engineer</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">AI Enthusiast</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Author</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Tech Blogger</div>
                                    </div>
                                    <div class="m-dash"></div>
                                </div>
                                <div class="home-intro-m-set">
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Java Developer</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Software Engineer</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">AI Enthusiast</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Author</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Tech Blogger</div>
                                    </div>
                                    <div class="m-dash"></div>
                                </div>
                            </div>
                        </div>
                        <div class="intro-marquee-container is-right">
                            <div style="-webkit-transform:translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);-moz-transform:translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);-ms-transform:translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);transform:translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0)"
                                 class="home-intro-marquee is-2">
                                <div class="home-intro-m-set is-grey">
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">500+ Commits</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">30+ Projects</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">10+ Libraries</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">20+ Years</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                </div>
                                <div class="home-intro-m-set is-grey">
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">500+ Commits</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">30+ Projects</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">10+ Libraries</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">20+ Years</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                </div>
                                <div class="home-intro-m-set is-grey">
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">500+ Commits</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">30+ Projects</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">10+ Libraries</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">20+ Years</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                </div>
                                <div class="home-intro-m-set is-grey">
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">500+ Commits</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">30+ Projects</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">10+ Libraries</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">20+ Years</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                </div>
                            </div>
                        </div>
                        <div class="intro-marquee-container">
                            <div style="-webkit-transform:translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);-moz-transform:translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);-ms-transform:translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);transform:translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0)"
                                 class="home-intro-marquee is-1">
                                <div class="home-intro-m-set">
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Java Developer</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Software Engineer</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">AI Enthusiast</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Author</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Tech Blogger</div>
                                    </div>
                                    <div class="m-dash"></div>
                                </div>
                                <div class="home-intro-m-set">
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Java Developer</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Software Engineer</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">AI Enthusiast</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Author</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Tech Blogger</div>
                                    </div>
                                    <div class="m-dash"></div>
                                </div>
                                <div class="home-intro-m-set">
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Java Developer</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Software Engineer</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">AI Enthusiast</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Author</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Tech Blogger</div>
                                    </div>
                                    <div class="m-dash"></div>
                                </div>
                                <div class="home-intro-m-set">
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Java Developer</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Software Engineer</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">AI Enthusiast</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Author</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Tech Blogger</div>
                                    </div>
                                    <div class="m-dash"></div>
                                </div>
                            </div>
                        </div>
                        <div class="intro-marquee-container is-right">
                            <div style="-webkit-transform:translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);-moz-transform:translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);-ms-transform:translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);transform:translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0)"
                                 class="home-intro-marquee is-2">
                                <div class="home-intro-m-set is-grey">
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">500+ Commits</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">30+ Projects</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">10+ Libraries</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">20+ Years</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                </div>
                                <div class="home-intro-m-set is-grey">
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">500+ Commits</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">30+ Projects</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">10+ Libraries</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">20+ Years</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                </div>
                                <div class="home-intro-m-set is-grey">
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">500+ Commits</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">30+ Projects</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">10+ Libraries</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">20+ Years</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                </div>
                                <div class="home-intro-m-set is-grey">
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">500+ Commits</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">30+ Projects</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">10+ Libraries</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">20+ Years</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                </div>
                            </div>
                        </div>
                        <div class="intro-marquee-container">
                            <div style="-webkit-transform:translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);-moz-transform:translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);-ms-transform:translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);transform:translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0)"
                                 class="home-intro-marquee is-1">
                                <div class="home-intro-m-set">
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Java Developer</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Software Engineer</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">AI Enthusiast</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Author</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Tech Blogger</div>
                                    </div>
                                    <div class="m-dash"></div>
                                </div>
                                <div class="home-intro-m-set">
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Java Developer</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Software Engineer</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">AI Enthusiast</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Author</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Tech Blogger</div>
                                    </div>
                                    <div class="m-dash"></div>
                                </div>
                                <div class="home-intro-m-set">
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Java Developer</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Software Engineer</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">AI Enthusiast</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Author</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Tech Blogger</div>
                                    </div>
                                    <div class="m-dash"></div>
                                </div>
                                <div class="home-intro-m-set">
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Java Developer</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Software Engineer</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">AI Enthusiast</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Author</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Tech Blogger</div>
                                    </div>
                                    <div class="m-dash"></div>
                                </div>
                            </div>
                        </div>
                        <div class="intro-marquee-container is-right">
                            <div style="-webkit-transform:translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);-moz-transform:translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);-ms-transform:translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);transform:translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0)"
                                 class="home-intro-marquee is-2">
                                <div class="home-intro-m-set is-grey">
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">500+ Commits</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">30+ Projects</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">10+ Libraries</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">20+ Years</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                </div>
                                <div class="home-intro-m-set is-grey">
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">500+ Commits</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">30+ Projects</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">10+ Libraries</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">20+ Years</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                </div>
                                <div class="home-intro-m-set is-grey">
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">500+ Commits</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">30+ Projects</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">10+ Libraries</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">20+ Years</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                </div>
                                <div class="home-intro-m-set is-grey">
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">500+ Commits</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">30+ Projects</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">10+ Libraries</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">20+ Years</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                </div>
                            </div>
                        </div>
                        <div class="intro-marquee-container">
                            <div style="-webkit-transform:translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);-moz-transform:translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);-ms-transform:translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);transform:translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0)"
                                 class="home-intro-marquee is-1">
                                <div class="home-intro-m-set">
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Java Developer</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Software Engineer</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">AI Enthusiast</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Author</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Tech Blogger</div>
                                    </div>
                                    <div class="m-dash"></div>
                                </div>
                                <div class="home-intro-m-set">
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Java Developer</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Software Engineer</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">AI Enthusiast</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Author</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Tech Blogger</div>
                                    </div>
                                    <div class="m-dash"></div>
                                </div>
                                <div class="home-intro-m-set">
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Java Developer</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Software Engineer</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">AI Enthusiast</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Author</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Tech Blogger</div>
                                    </div>
                                    <div class="m-dash"></div>
                                </div>
                                <div class="home-intro-m-set">
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Java Developer</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Software Engineer</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">AI Enthusiast</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Author</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Tech Blogger</div>
                                    </div>
                                    <div class="m-dash"></div>
                                </div>
                            </div>
                        </div>
                        <div class="intro-marquee-container is-right">
                            <div style="-webkit-transform:translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);-moz-transform:translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);-ms-transform:translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);transform:translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0)"
                                 class="home-intro-marquee is-2">
                                <div class="home-intro-m-set is-grey">
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">500+ Commits</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">30+ Projects</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">10+ Libraries</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">20+ Years</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                </div>
                                <div class="home-intro-m-set is-grey">
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">500+ Commits</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">30+ Projects</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">10+ Libraries</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">20+ Years</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                </div>
                                <div class="home-intro-m-set is-grey">
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">500+ Commits</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">30+ Projects</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">10+ Libraries</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">20+ Years</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                </div>
                                <div class="home-intro-m-set is-grey">
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">500+ Commits</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">30+ Projects</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">10+ Libraries</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">20+ Years</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                </div>
                            </div>
                        </div>
                        <div class="intro-marquee-container">
                            <div style="-webkit-transform:translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);-moz-transform:translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);-ms-transform:translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);transform:translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0)"
                                 class="home-intro-marquee is-1">
                                <div class="home-intro-m-set">
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Java Developer</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Software Engineer</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">AI Enthusiast</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Author</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Tech Blogger</div>
                                    </div>
                                    <div class="m-dash"></div>
                                </div>
                                <div class="home-intro-m-set">
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Java Developer</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Software Engineer</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">AI Enthusiast</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Author</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Tech Blogger</div>
                                    </div>
                                    <div class="m-dash"></div>
                                </div>
                                <div class="home-intro-m-set">
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Java Developer</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Software Engineer</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">AI Enthusiast</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Author</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Tech Blogger</div>
                                    </div>
                                    <div class="m-dash"></div>
                                </div>
                                <div class="home-intro-m-set">
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Java Developer</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Software Engineer</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">AI Enthusiast</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Author</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Tech Blogger</div>
                                    </div>
                                    <div class="m-dash"></div>
                                </div>
                            </div>
                        </div>
                        <div class="intro-marquee-container is-right">
                            <div style="-webkit-transform:translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);-moz-transform:translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);-ms-transform:translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);transform:translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0)"
                                 class="home-intro-marquee is-2">
                                <div class="home-intro-m-set is-grey">
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">500+ Commits</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">30+ Projects</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">10+ Libraries</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">20+ Years</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                </div>
                                <div class="home-intro-m-set is-grey">
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">500+ Commits</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">30+ Projects</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">10+ Libraries</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">20+ Years</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                </div>
                                <div class="home-intro-m-set is-grey">
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">500+ Commits</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">30+ Projects</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">10+ Libraries</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">20+ Years</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                </div>
                                <div class="home-intro-m-set is-grey">
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">500+ Commits</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">30+ Projects</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">10+ Libraries</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">20+ Years</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="marquee-flex is-main">
                        <div class="intro-marquee-container">
                            <div style="-webkit-transform:translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);-moz-transform:translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);-ms-transform:translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);transform:translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0)"
                                 class="home-intro-marquee is-1">
                                <div class="home-intro-m-set">
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Java Developer</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Software Engineer</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">AI Enthusiast</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Author</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Tech Blogger</div>
                                    </div>
                                    <div class="m-dash"></div>
                                </div>
                                <div class="home-intro-m-set">
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Java Developer</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Software Engineer</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">AI Enthusiast</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Author</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Tech Blogger</div>
                                    </div>
                                    <div class="m-dash"></div>
                                </div>
                                <div class="home-intro-m-set">
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Java Developer</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Software Engineer</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">AI Enthusiast</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Author</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Tech Blogger</div>
                                    </div>
                                    <div class="m-dash"></div>
                                </div>
                                <div class="home-intro-m-set">
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Java Developer</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Software Engineer</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">AI Enthusiast</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Author</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Tech Blogger</div>
                                    </div>
                                    <div class="m-dash"></div>
                                </div>
                            </div>
                        </div>
                        <div class="intro-marquee-container is-right">
                            <div style="-webkit-transform:translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);-moz-transform:translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);-ms-transform:translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);transform:translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0)"
                                 class="home-intro-marquee is-2">
                                <div class="home-intro-m-set is-grey">
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">500+ Commits</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">30+ Projects</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">10+ Libraries</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">20+ Years</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                </div>
                                <div class="home-intro-m-set is-grey">
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">500+ Commits</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">30+ Projects</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">10+ Libraries</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">20+ Years</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                </div>
                                <div class="home-intro-m-set is-grey">
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">500+ Commits</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">30+ Projects</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">10+ Libraries</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">20+ Years</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                </div>
                                <div class="home-intro-m-set is-grey">
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">500+ Commits</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">30+ Projects</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">10+ Libraries</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">20+ Years</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                </div>
                            </div>
                        </div>
                        <div data-w-id="b083bb2f-a78a-8b36-7a69-25ba88d151cd"
                             class="whipe-cream-intro is-panel-top"></div>
                        <div data-w-id="18b2d837-1acd-4b1e-2bcd-455984cc3ecd"
                             class="whipe-cream-intro is-panel-btm"></div>
                    </div>
                    <div class="marquee-flex is-hidden">
                        <div data-w-id="8f279a91-90ae-c725-84e7-f60f566262f7" class="whipe-cream-intro is-btm"></div>
                        <div class="intro-marquee-container">
                            <div style="-webkit-transform:translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);-moz-transform:translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);-ms-transform:translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);transform:translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0)"
                                 class="home-intro-marquee is-1">
                                <div class="home-intro-m-set">
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Java Developer</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Software Engineer</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">AI Enthusiast</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Author</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Tech Blogger</div>
                                    </div>
                                    <div class="m-dash"></div>
                                </div>
                                <div class="home-intro-m-set">
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Java Developer</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Software Engineer</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">AI Enthusiast</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Author</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Tech Blogger</div>
                                    </div>
                                    <div class="m-dash"></div>
                                </div>
                                <div class="home-intro-m-set">
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Java Developer</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Software Engineer</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">AI Enthusiast</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Author</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Tech Blogger</div>
                                    </div>
                                    <div class="m-dash"></div>
                                </div>
                                <div class="home-intro-m-set">
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Java Developer</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Software Engineer</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">AI Enthusiast</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Author</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Tech Blogger</div>
                                    </div>
                                    <div class="m-dash"></div>
                                </div>
                            </div>
                        </div>
                        <div class="intro-marquee-container is-right">
                            <div style="-webkit-transform:translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);-moz-transform:translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);-ms-transform:translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);transform:translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0)"
                                 class="home-intro-marquee is-2">
                                <div class="home-intro-m-set is-grey">
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">500+ Commits</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">30+ Projects</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">10+ Libraries</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">20+ Years</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                </div>
                                <div class="home-intro-m-set is-grey">
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">500+ Commits</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">30+ Projects</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">10+ Libraries</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">20+ Years</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                </div>
                                <div class="home-intro-m-set is-grey">
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">500+ Commits</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">30+ Projects</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">10+ Libraries</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">20+ Years</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                </div>
                                <div class="home-intro-m-set is-grey">
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">500+ Commits</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">30+ Projects</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">10+ Libraries</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">20+ Years</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                </div>
                            </div>
                        </div>
                        <div class="intro-marquee-container">
                            <div style="-webkit-transform:translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);-moz-transform:translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);-ms-transform:translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);transform:translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0)"
                                 class="home-intro-marquee is-1">
                                <div class="home-intro-m-set">
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Java Developer</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Software Engineer</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">AI Enthusiast</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Author</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Tech Blogger</div>
                                    </div>
                                    <div class="m-dash"></div>
                                </div>
                                <div class="home-intro-m-set">
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Java Developer</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Software Engineer</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">AI Enthusiast</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Author</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Tech Blogger</div>
                                    </div>
                                    <div class="m-dash"></div>
                                </div>
                                <div class="home-intro-m-set">
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Java Developer</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Software Engineer</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">AI Enthusiast</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Author</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Tech Blogger</div>
                                    </div>
                                    <div class="m-dash"></div>
                                </div>
                                <div class="home-intro-m-set">
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Java Developer</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Software Engineer</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">AI Enthusiast</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Author</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Tech Blogger</div>
                                    </div>
                                    <div class="m-dash"></div>
                                </div>
                            </div>
                        </div>
                        <div class="intro-marquee-container is-right">
                            <div style="-webkit-transform:translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);-moz-transform:translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);-ms-transform:translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);transform:translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0)"
                                 class="home-intro-marquee is-2">
                                <div class="home-intro-m-set is-grey">
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">500+ Commits</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">30+ Projects</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">10+ Libraries</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">20+ Years</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                </div>
                                <div class="home-intro-m-set is-grey">
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">500+ Commits</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">30+ Projects</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">10+ Libraries</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">20+ Years</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                </div>
                                <div class="home-intro-m-set is-grey">
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">500+ Commits</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">30+ Projects</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">10+ Libraries</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">20+ Years</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                </div>
                                <div class="home-intro-m-set is-grey">
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">500+ Commits</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">30+ Projects</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">10+ Libraries</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">20+ Years</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                </div>
                            </div>
                        </div>
                        <div class="intro-marquee-container">
                            <div style="-webkit-transform:translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);-moz-transform:translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);-ms-transform:translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);transform:translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0)"
                                 class="home-intro-marquee is-1">
                                <div class="home-intro-m-set">
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Java Developer</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Software Engineer</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">AI Enthusiast</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Author</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Tech Blogger</div>
                                    </div>
                                    <div class="m-dash"></div>
                                </div>
                                <div class="home-intro-m-set">
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Java Developer</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Software Engineer</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">AI Enthusiast</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Author</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Tech Blogger</div>
                                    </div>
                                    <div class="m-dash"></div>
                                </div>
                                <div class="home-intro-m-set">
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Java Developer</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Software Engineer</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">AI Enthusiast</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Author</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Tech Blogger</div>
                                    </div>
                                    <div class="m-dash"></div>
                                </div>
                                <div class="home-intro-m-set">
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Java Developer</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Software Engineer</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">AI Enthusiast</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Author</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Tech Blogger</div>
                                    </div>
                                    <div class="m-dash"></div>
                                </div>
                            </div>
                        </div>
                        <div class="intro-marquee-container is-right">
                            <div style="-webkit-transform:translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);-moz-transform:translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);-ms-transform:translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);transform:translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0)"
                                 class="home-intro-marquee is-2">
                                <div class="home-intro-m-set is-grey">
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">500+ Commits</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">30+ Projects</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">10+ Libraries</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">20+ Years</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                </div>
                                <div class="home-intro-m-set is-grey">
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">500+ Commits</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">30+ Projects</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">10+ Libraries</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">20+ Years</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                </div>
                                <div class="home-intro-m-set is-grey">
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">500+ Commits</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">30+ Projects</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">10+ Libraries</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">20+ Years</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                </div>
                                <div class="home-intro-m-set is-grey">
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">500+ Commits</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">30+ Projects</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">10+ Libraries</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">20+ Years</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                </div>
                            </div>
                        </div>
                        <div class="intro-marquee-container">
                            <div style="-webkit-transform:translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);-moz-transform:translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);-ms-transform:translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);transform:translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0)"
                                 class="home-intro-marquee is-1">
                                <div class="home-intro-m-set">
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Java Developer</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Software Engineer</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">AI Enthusiast</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Author</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Tech Blogger</div>
                                    </div>
                                    <div class="m-dash"></div>
                                </div>
                                <div class="home-intro-m-set">
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Java Developer</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Software Engineer</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">AI Enthusiast</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Author</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Tech Blogger</div>
                                    </div>
                                    <div class="m-dash"></div>
                                </div>
                                <div class="home-intro-m-set">
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Java Developer</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Software Engineer</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">AI Enthusiast</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Author</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Tech Blogger</div>
                                    </div>
                                    <div class="m-dash"></div>
                                </div>
                                <div class="home-intro-m-set">
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Java Developer</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Software Engineer</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">AI Enthusiast</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Author</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Tech Blogger</div>
                                    </div>
                                    <div class="m-dash"></div>
                                </div>
                            </div>
                        </div>
                        <div class="intro-marquee-container is-right">
                            <div style="-webkit-transform:translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);-moz-transform:translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);-ms-transform:translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);transform:translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0)"
                                 class="home-intro-marquee is-2">
                                <div class="home-intro-m-set is-grey">
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">500+ Commits</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">30+ Projects</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">10+ Libraries</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">20+ Years</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                </div>
                                <div class="home-intro-m-set is-grey">
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">500+ Commits</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">30+ Projects</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">10+ Libraries</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">20+ Years</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                </div>
                                <div class="home-intro-m-set is-grey">
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">500+ Commits</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">30+ Projects</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">10+ Libraries</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">20+ Years</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                </div>
                                <div class="home-intro-m-set is-grey">
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">500+ Commits</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">30+ Projects</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">10+ Libraries</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">20+ Years</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                </div>
                            </div>
                        </div>
                        <div class="intro-marquee-container">
                            <div style="-webkit-transform:translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);-moz-transform:translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);-ms-transform:translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);transform:translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0)"
                                 class="home-intro-marquee is-1">
                                <div class="home-intro-m-set">
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Java Developer</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Software Engineer</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">AI Enthusiast</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Author</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Tech Blogger</div>
                                    </div>
                                    <div class="m-dash"></div>
                                </div>
                                <div class="home-intro-m-set">
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Java Developer</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Software Engineer</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">AI Enthusiast</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Author</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Tech Blogger</div>
                                    </div>
                                    <div class="m-dash"></div>
                                </div>
                                <div class="home-intro-m-set">
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Java Developer</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Software Engineer</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">AI Enthusiast</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Author</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Tech Blogger</div>
                                    </div>
                                    <div class="m-dash"></div>
                                </div>
                                <div class="home-intro-m-set">
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Java Developer</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Software Engineer</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">AI Enthusiast</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Author</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Tech Blogger</div>
                                    </div>
                                    <div class="m-dash"></div>
                                </div>
                            </div>
                        </div>
                        <div class="intro-marquee-container is-right">
                            <div style="-webkit-transform:translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);-moz-transform:translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);-ms-transform:translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);transform:translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0)"
                                 class="home-intro-marquee is-2">
                                <div class="home-intro-m-set is-grey">
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">500+ Commits</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">30+ Projects</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">10+ Libraries</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">20+ Years</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                </div>
                                <div class="home-intro-m-set is-grey">
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">500+ Commits</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">30+ Projects</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">10+ Libraries</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">20+ Years</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                </div>
                                <div class="home-intro-m-set is-grey">
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">500+ Commits</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">30+ Projects</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">10+ Libraries</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">20+ Years</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                </div>
                                <div class="home-intro-m-set is-grey">
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">500+ Commits</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">30+ Projects</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">10+ Libraries</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">20+ Years</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                </div>
                            </div>
                        </div>
                        <div class="intro-marquee-container">
                            <div style="-webkit-transform:translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);-moz-transform:translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);-ms-transform:translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);transform:translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0)"
                                 class="home-intro-marquee is-1">
                                <div class="home-intro-m-set">
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Java Developer</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Software Engineer</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">AI Enthusiast</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Author</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Tech Blogger</div>
                                    </div>
                                    <div class="m-dash"></div>
                                </div>
                                <div class="home-intro-m-set">
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Java Developer</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Software Engineer</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">AI Enthusiast</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Author</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Tech Blogger</div>
                                    </div>
                                    <div class="m-dash"></div>
                                </div>
                                <div class="home-intro-m-set">
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Java Developer</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Software Engineer</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">AI Enthusiast</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Author</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Tech Blogger</div>
                                    </div>
                                    <div class="m-dash"></div>
                                </div>
                                <div class="home-intro-m-set">
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Java Developer</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Software Engineer</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">AI Enthusiast</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Author</div>
                                    </div>
                                    <div class="m-dash"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text">Tech Blogger</div>
                                    </div>
                                    <div class="m-dash"></div>
                                </div>
                            </div>
                        </div>
                        <div class="intro-marquee-container is-right">
                            <div style="-webkit-transform:translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);-moz-transform:translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);-ms-transform:translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);transform:translate3d(0em, 0, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0)"
                                 class="home-intro-marquee is-2">
                                <div class="home-intro-m-set is-grey">
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">500+ Commits</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">30+ Projects</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">10+ Libraries</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">20+ Years</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                </div>
                                <div class="home-intro-m-set is-grey">
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">500+ Commits</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">30+ Projects</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">10+ Libraries</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">20+ Years</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                </div>
                                <div class="home-intro-m-set is-grey">
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">500+ Commits</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">30+ Projects</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">10+ Libraries</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">20+ Years</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                </div>
                                <div class="home-intro-m-set is-grey">
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">500+ Commits</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">30+ Projects</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">10+ Libraries</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                    <div class="home-intro-m-text-wrap">
                                        <div class="home-intro-m-text is-grey">20+ Years</div>
                                    </div>
                                    <div class="m-dash bg-grey"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div data-w-id="9d0e3cde-dfa7-0dda-c180-ec7ad0373727" class="page-intro-main-box">
                <div class="page-intro-text-wrap is-sp-home-top is-m-hidden">
                    <div class="page-intro-text is-home" style="overflow: visible; padding-bottom: 100px;"><span data-w-id="1ec6c8a1-2dc4-46d9-1190-d49a130355dd" class="span-text is-1 is-fill">J</span><span
                            class="span-text is-2 is-fill">A</span><span class="span-text is-3 is-fill">V</span><span
                            data-w-id="16eef9e7-4006-8f37-44b3-ae591327adc1" class="span-text is-4 is-fill">A</span> <span
                            data-w-id="ceb2db9c-2ce8-649d-1855-9d319e9e85da" class="span-text is-5">B</span><span
                            class="span-text is-6 is-sp">L</span><span data-w-id="3feac868-a1a3-9f35-f96f-051ef4976984"
                                                                       class="span-text is-7">O</span><span
                            data-w-id="30208dc1-b4c4-675b-5742-7b25f05f702d" class="span-text is-8">G</span></div>
                </div>
                <div class="page-intro-text-wrap is-sp-home is-m-hidden" style="display: none;">
                    <div class="page-intro-text is-home"><span data-w-id="1ec6c8a1-2dc4-46d9-1190-d49a130355dd"
                                                               class="span-text is-1">O</span><span
                            data-w-id="1ec6c8a1-2dc4-46d9-1190-d49a130355df" class="span-text is-2">P</span><span
                            data-w-id="1ec6c8a1-2dc4-46d9-1190-d49a130355e1" class="span-text is-3">E</span><span
                            data-w-id="1ec6c8a1-2dc4-46d9-1190-d49a130355e3" class="span-text is-4">R</span> <span
                            data-w-id="1ec6c8a1-2dc4-46d9-1190-d49a130355e5" class="span-text is-5">E</span><span
                            data-w-id="1ec6c8a1-2dc4-46d9-1190-d49a130355e7" class="span-text is-6">N</span><span
                            data-w-id="1ec6c8a1-2dc4-46d9-1190-d49a130355e9" class="span-text is-7">G</span><span
                            data-w-id="1ec6c8a1-2dc4-46d9-1190-d49a130355eb" class="span-text is-8">I</span><span
                            data-w-id="1ec6c8a1-2dc4-46d9-1190-d49a130355ed" class="span-text is-9">N</span><span
                            data-w-id="2aef2651-27e2-7123-341d-ebc0c97f7c0d" class="span-text is-10">E</span><span
                            data-w-id="631c39c8-3c5e-df62-bce8-e4b7b6e6bd52" class="span-text is-11">R</span></div>
                </div>
            </div>
            <div class="page-intro-main-box is-mobile">
                <div class="page-intro-text-wrap is-sp-home-top">
                    <div class="page-intro-text is-home" style="overflow: visible; padding-bottom: 20px;"><span data-w-id="3ef0f252-de49-f4de-e4e1-f1029246cb9d" class="span-text is-1 is-fill">J</span><span
                            class="span-text is-2 is-fill">A</span><span class="span-text is-3 is-fill">V</span><span
                            class="span-text is-4 is-fill">A</span> <span class="span-text is-5">B</span><span
                            class="span-text is-6">L</span><span class="span-text is-7">O</span><span
                            class="span-text is-8">G</span></div>
                </div>
                <div class="page-intro-text-wrap is-sp-home" style="display: none;">
                    <div class="page-intro-text is-sm"><span data-w-id="3ef0f252-de49-f4de-e4e1-f1029246cb9d"
                                                             class="span-text is-1">A</span><span
                            data-w-id="3ef0f252-de49-f4de-e4e1-f1029246cb9f" class="span-text is-2">D</span><span
                            data-w-id="3ef0f252-de49-f4de-e4e1-f1029246cba1" class="span-text is-3">E</span><span
                            data-w-id="3ef0f252-de49-f4de-e4e1-f1029246cba3" class="span-text is-4">V</span><span
                            data-w-id="3ef0f252-de49-f4de-e4e1-f1029246cba5" class="span-text is-5">E</span>  <span
                            data-w-id="88222aec-0acc-cf38-0cd2-b81a6a934976" class="span-text is-6">L</span></div>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="cursor-parent">
    <div class="cursor-div"></div>
</div>
<script src="js/jquery-3.5.1.min.dc5e7f18c8.js" type="text/javascript"></script>
<script src="6787c53671a97b8708dd7000/js/webflow.schunk.36b8fb49256177c8.js"
        type="text/javascript"></script>
<script src="6787c53671a97b8708dd7000/js/webflow.schunk.c91078ba52477730.js"
        type="text/javascript"></script>
<script src="6787c53671a97b8708dd7000/js/webflow.90da128a.e60d2f6da20aec26.js"
        type="text/javascript"></script>
<script src="npm/swiper@11/swiper-bundle.min.js"></script>

<script>
    const techswiper = new Swiper('.swiper.is-tech', {
            // Optional parameters
            loop: true,
            spaceBetween: '1em',

            // And if we need scrollbar
            scrollbar: {
                el: '.swiper-scrollbar'
            }
        });
</script>

<script>
    $('a.btn-filter, a.btn-outer-cta, a.logo-container, a.gallery-marq-wrap, a.np-link-wrap, a.btn-outer-frame, a.rot-int-wrapper, a.n-link, a.nav-link, a.drop-pop, a.footer-link, a.link, a.link-wrapper, a.m-menu-link, a.btn, a.n-p-link, a.btn-parent-s, a.img-parent-link').click(function (e) {
        e.preventDefault();                   // prevent default anchor behavior
        var goTo = this.getAttribute("href"); // store anchor href

        setTimeout(function () {
            window.location = goTo;
        }, 2000);
    });
    // Adobe Typekit disabled - using local fonts instead
    /*
    (function (d) {
        var config = {
                kitId: 'vqw1kwc',
                scriptTimeout: 2000,
                async: true
            },
            h = d.documentElement, t = setTimeout(function () {
                h.className = h.className.replace(/\bwf-loading\b/g, "") + " wf-inactive";
            }, config.scriptTimeout), tk = d.createElement("script"), f = false, s = d.getElementsByTagName("script")[0], a;
        h.className += " wf-loading";
        tk.src = 'https://use.typekit.net/' + config.kitId + '.js';
        tk.async = true;
        tk.onload = tk.onreadystatechange = function () {
            a = this.readyState;
            if (f || a && a != "complete" && a != "loaded") return;
            f = true;
            clearTimeout(t);
            try {
                Typekit.load(config)
            } catch (e) {
            }
        };
        s.parentNode.insertBefore(tk, s)
    })(document);
    */
</script>

<script>
    $('.open').on('tap', function () {
        $('body').toggleClass('no-scroll');
    });
</script>


<script>
    $(window).bind("pageshow", function (event) {
        if (event.originalEvent.persisted) {
            window.location.reload()
        }
    });
</script>


<script src="gh/studio-freight/lenis@1.0.23/bundled/lenis.min.js"></script>
<script>
    let lenis = new Lenis({
        lerp: 0.1,
        wheelMultiplier: 0.7,
        gestureOrientation: "vertical",
        normalizeWheel: false,
        smoothTouch: false,
    });

    function raf(time) {
        lenis.raf(time);
        requestAnimationFrame(raf);
    }

    requestAnimationFrame(raf);
    $("[data-lenis-start]").on("click", function () {
        lenis.start();
    });
    $("[data-lenis-stop]").on("click", function () {
        lenis.stop();
    });
    $("[data-lenis-toggle]").on("click", function () {
        $(this).toggleClass("stop-scroll");
        if ($(this).hasClass("stop-scroll")) {
            lenis.stop();
        } else {
            lenis.start();
        }
    });
</script>


<script src='ajax/libs/waypoints/2.0.3/waypoints.min.js'></script>
<script src='jquery.counterup/1.0/jquery.counterup.min.js'></script>
<script>
    $('.counter').counterUp({
        time: 3000
    });
    $('.counter').addClass('animated fadeIn');
</script>
</body>
</html>